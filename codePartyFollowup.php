<?php 
include("template.php");
function main()
{
	$heading="<span>Manage</span> Followup ";
	$pageName="codePartyFollowup.php";	
    include("inc/clsObj.php");	
	
	extract($_POST);	
	
/*	if(!isset($_GET['fstatus']) &&  $_GET['fstatus']=="")
		$_GET['fstatus'] = "Today"; */
	
	$cdate = explode("-",$Date);
	$fdate=$cdate[2]."-".$cdate[1]."-".$cdate[0];
	
	$ndate = explode("-",$Next_Date);
	$nextdate=$ndate[2]."-".$ndate[1]."-".$ndate[0];
	

	$rdate = explode("-",$Due_Date);
	$duedate=$rdate[2]."-".$rdate[1]."-".$rdate[0];

	$objFollow->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objFollow->company=$_SESSION['company'];
	$objFollow->financial_year=$_SESSION['fyear'];
	$objFollow->party=$Party_Name;
	$objFollow->followup_date=$fdate;
	$objFollow->next_date=$nextdate;
	$objFollow->remarks=$Remarks;
	$objFollow->due_date=$duedate;
	
	$objFollow->amount=$Amount;
	$objFollow->status=0;
	
	if(isset($_POST['btnAdd']))
	{
		$objFollow->status=1;
		$objFollow->insert();
		redirect("codePartyFollowup.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objFollow->update();
		redirect("codePartyFollowup.php?msg=edit&fstatus=".$_POST['fstatus']);
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);

		switch($optAction)
		{
			case 0:
				$objFollow->deleteSelect($chkAction);
				break;
			case 1:
				$objFollow->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objFollow->statusUpdateUnPublish($chkAction);
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codePartyFollowup.php?msg=edit&fstatus=".$_REQUEST['fstatus']);
	}	

	if(isset($_GET['delete']))
	{	
		$objFollow->delete();
		//$objFollow->deleteItemsById();
		redirect("codePartyFollowup.php?msg=del&fstatus=".$_REQUEST['fstatus']);
	}	

	if(isset($_GET['status']))
	{	
		$objFollow->status = $_GET['status'];
		$objFollow->status();
		redirect("codePartyFollowup.php?msg=status&fstatus=".$_REQUEST['fstatus']);		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objFollow->selectRecById();

		$objFollow->cid = $listEdit[0]['id'];
		$listItems=$objFollow->selectRecById();	
	}
	elseif($_GET['Client_Name']!="" ||  ($_GET['From_Date']!="" && $_GET['To_Date']!=""  && $_GET['From_Date']!="" && $_GET['To_Date']!=""))
	{
		$query = "";
		if($_GET['Client_Name']!="")
			$query.=" and flu.party='".$_GET['Client_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!=""  && $_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
		
			$query.=" and flu.next_date between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";

		$listRec=$objFollow->paging($query);
    }
	else
	{
		$query = "";
		
		if((!isset($_GET['fstatus']) && $_GET['fstatus']=="") || $_GET['fstatus']=="Today")
		{	
			$_GET['fstatus']="Today";
			$query.=" and flu.next_date = '".date("Y-m-d")."'";
		}
		elseif($_GET['fstatus']=="Pending")
			$query.=" and flu.next_date < '".date("Y-m-d")."'";
		elseif($_GET['fstatus']=="Future")
			$query.=" and flu.next_date > '".date("Y-m-d")."'";
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";

		$listRec=$objFollow->selectByQuery($query);
    }

/*	else
	{
		$query.=" and financial_year='".$_SESSION['fyear']."'";
		$listRec=$objFollow->paging($query);
	}*/
	include("html/frmPartyFollowup.php");
} 
?>
