<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();
//include("template.php");
//function main()
//{
    include("inc/clsObj.php");	
/*	$objBill->id=$_GET['id'];

	$listEdit=$objBill->selectRecById();
		
	$objBill->bid = $listEdit[0]['id'];
	$listItems=$objBill->selectItemsByBillNo();		*/
	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}

td
{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>

<div style="width:100%;" id="print_header">
<?  
	
	if($_GET['Party_Name']!="" ||  ($_GET['From_Date']!="" && $_GET['To_Date']!=""))
	{
		$query = "";
		if($_GET['Party_Name']!="")
			$query.=" and party='".$_GET['Party_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
			$query.=" and `transaction_date` between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		
		$objTds->party = $_GET['party'];
		$listRec=$objTds->selectRecBySearch($query);
    }
	//else
		// $listRec=$objTds->paging_report();
	
?>
<div style="width:800px; margin:0 auto;">
<table width="100%" border="1" cellspacing="0" cellpadding="3" style="border-collapse:collapse; border:#CCCCCC 1px solid;">
<tr><td colspan="5" align="center">
<strong><?=$_SESSION['company'];?></strong><br />
      304-A, Ashoka,<br/>
      Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
      Ahmedabad-380014<br />
     
      Ph: +91-79-40391397<br />
      E-Mail: 
      <a href="mailto:<EMAIL>"><EMAIL></a><br />
      Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>
      </td></tr>
  	<tr style="background:#ccc; font-size:12px;">
    <th width="14%">Sr. No. </th>
    <th width="60%">Party</th>
    <th width="13%">Date</th>
    <th width="13%" align="right">Amount</th>
	</tr>
   <?  $n=0;
   		$total_amt = 0;
    if(count($listRec)>0)
	 {
      	$colorflg=0;
	   	for($e=0;$e<count($listRec);$e++)
		{
			 $n++;
			$total_amt+=$final_amount;
		
		  if ($colorflg==1){ $colorflg=0;?>
  		  <tr class="odd" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='odd'">
   <? }	else {	$colorflg=1;?>
		 <tr class="even" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='even'">
   <? } ?>
            <td><?=$n;?></td>
            <td>
            <?
                $objParty->id = $listRec[$e]['party'];
                $partynm = $objParty->selectRecById();
                echo $partynm[0]['party_name'];
            ?></td>
            <td><?=$listRec[$e]['tdt'];?></td>
            <td align="right"><?=number_format($listRec[$e]['credit_amount'],2);?></td>
     </tr>
  <?  $total+=$listRec[$e]['credit_amount'];
  } ?>
  <tr style="background:#333333; color:#FFFFFF; height:30px;">
    <td colspan="3" style="font-weight:bold;">Total Amount : </td>
    <td align="right" style="font-weight:bold;"><?=number_format($total,2);?></td></tr> 
    <tr class="toprow">
            <td colspan="4" style="text-align:right; font-style:italic;">Report by : <?=$_SESSION['membername'];?></td>
        </tr>
  <?
  } else { ?>
  		<tr>
        	<td colspan="11" align="center">
            	No Records Found...</td>
		</tr>
    <?php }?>    
  </table>
</div>

</div>
<script language="javascript">
	window.print();
</script>
<? // } ?>