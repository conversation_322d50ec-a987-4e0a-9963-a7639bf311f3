<script>
function ValidateForm(){
	var errors='';
	if(document.getElementById("txtPassword").value==document.getElementById("txtOldPassword").value)
		errors+='Passowrd must be different than previous password.\n';
	
	if(document.getElementById("txtPassword").value!=document.getElementById("txtVerifyPassword").value)
		errors+='Passowrd and confirm password should be same.\n';
	
	if(errors){
		document.getElementById("error_msg").innerHTML = errors;
		return false;
	}
	else
		return true;
}
function showPWDOld(){
	if(document.getElementById("txtOldPassword").type == "password"){
		document.getElementById("txtOldPassword").type="text";
		document.getElementById("hide_show_password_old").src='images/hide_password.png';
		document.getElementById("hide_show_password_old").title='Hide Password';
	}
	else{
		document.getElementById("txtPassword").type="password";
		document.getElementById("hide_show_password").src='images/show_password.png';
		document.getElementById("hide_show_password").title='Show Password';
	}
}

function showPWD(){
	if(document.getElementById("txtPassword").type == "password"){
		document.getElementById("txtPassword").type="text";
		document.getElementById("hide_show_password").src='images/hide_password.png';
		document.getElementById("hide_show_password").title='Hide Password';
	}
	else{
		document.getElementById("txtPassword").type="password";
		document.getElementById("hide_show_password").src='images/show_password.png';
		document.getElementById("hide_show_password").title='Show Password';
	}
}
function showPWDVerify(){
	if(document.getElementById("txtVerifyPassword").type == "password"){
		document.getElementById("txtVerifyPassword").type="text";
		document.getElementById("hide_show_password_verify").src='images/hide_password.png';
		document.getElementById("hide_show_password_verify").title='Hide Password';
	}
	else{
		document.getElementById("txtVerifyPassword").type="password";
		document.getElementById("hide_show_password_verify").src='images/show_password.png';
		document.getElementById("hide_show_password_verify").title='Show Password';
	}
}
</script>
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Admin</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Enter Password Detail</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						<form name="frmChangePW" id="frmChangePW" method="post" action="" onsubmit="return ValidateForm()">
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label>Email</label>
                                    <input type="text" id="username" name="username" class="form-control" value="<?=$obj_admin->getEmailById($_SESSION['memberid']);?>" disabled>
                                </div>
								<div class="form-group col-md-8"></div>
								<div class="clearfix"></div>
								
								<div class="form-group col-md-3">
                                    <label>Old Password</label>
                                    <input type="password" id="txtOldPassword" name="txtOldPassword" class="form-control" required>
                                </div>
                                <div class="form-group col-md-1">
                                    <label>&nbsp;</label><br/>
                                    <img src="images/show_password.png" width="25" style="cursor:pointer;" onClick="javascript:showPWDOld()" id="hide_show_password_old" title="Show Password" />
                                </div>
								<div class="form-group col-md-3">
                                    <label>New Password</label>
                                    <input type="password" id="txtPassword" name="txtPassword" class="form-control" required>
                                </div>
                                <div class="form-group col-md-1">
                                    <label>&nbsp;</label><br/>
                                    <img src="images/show_password.png" width="25" style="cursor:pointer;" onClick="javascript:showPWD()" id="hide_show_password" title="Show Password" />
                                </div>
								<div class="form-group col-md-3">
                                    <label>Confirm New Password</label>
                                    <input type="password" id="txtVerifyPassword" name="txtVerifyPassword" class="form-control" required>
                                </div>
                                <div class="form-group col-md-1">
                                    <label>&nbsp;</label><br/>
                                    <img src="images/show_password.png" width="25" style="cursor:pointer;" onClick="javascript:showPWDVerify()" id="hide_show_password" title="Show Password" />
                                </div>
								<div class="clearfix"></div>
                                <div id="error_msg" class="err_msg">
									<? if(isset($_GET['msg']) && $_GET['msg']=="npw") 
                                            echo "Wrong password..."; 
                                        elseif(isset($_GET['msg']) && $_GET['msg']=="chp")
                                            echo "Password has been changed";?>
                               	</div>
                                <div class="clearfix"></div>
                            </div>
                            <button type="submit" name="btnAdminChangePassword" id="btnAdminChangePassword" class="btn btn-success">Change Password</button>
                            <button type="reset" class="btn btn-success">Cancel</button>
                        </form>
						
                    </div>
                </div>
            </div>
        </div>
		
	</div>
</div>
