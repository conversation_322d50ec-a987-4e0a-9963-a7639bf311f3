<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Client ";
	$pageName="codeManageParty.php";
	
	// ==== SMS details =========== 
	$apiKey = "687dde5ae7ab3";
    $sender = "MNAGLD";
    $route = "promo_dnd";
	
    include("inc/clsObj.php");	
	extract($_POST);	

	$objParty->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objParty->customer_name=$Customer_Name;
	
	$Party_Address=str_replace("\&quot;","", $Party_Address);		
		$Party_Address=str_replace('\\', '', $Party_Address);				
		$Party_Address=str_replace("\'","'", $Party_Address);		
		$Party_Address=str_replace("'","\'", $Party_Address);	

		$objParty->address=''; // $Party_Address;
		$objParty->contact_number=$Contact_Number;
		$objParty->alternate_number=$Alternative_Number;
		$objParty->email=''; // $Email_Address;
		$objParty->area=$Area;
		$objParty->city=$City;
		$objParty->gold_weight=$Gold_Weight;
		$objParty->loan_amount=$Loan_Amount;
		$objParty->bank_name=$Bank_Name;
		$objParty->source=$Source;
		$objParty->other_source=$Other_Source;
		
        // $objParty->gross_weight=$Gross_Weight;
        // $objParty->net_weight=$Net_Weight;
        // $objParty->bank_outstanding_amt=$Bank_Outstanding_Amt;
        // $objParty->difference_value=$Difference_Value;
        // $objParty->total_value=$Total_Value;

        $objParty->gross_weight = (is_numeric($Gross_Weight) && $Gross_Weight !== '') ? floatval($Gross_Weight) : 0;
        $objParty->net_weight = (is_numeric($Net_Weight) && $Net_Weight !== '') ? floatval($Net_Weight) : 0;
        $objParty->bank_outstanding_amt = (is_numeric($Bank_Outstanding_Amt) && $Bank_Outstanding_Amt !== '') ? floatval($Bank_Outstanding_Amt) : 0;
        $objParty->difference_value = (is_numeric($Difference_Value) && $Difference_Value !== '') ? floatval($Difference_Value) : 0;
        $objParty->total_value = (is_numeric($Total_Value) && $Total_Value !== '') ? floatval($Total_Value) : 0;
		
		$objParty->sales_person=$Sales_Person;
		$objParty->tele_caller =$_SESSION['memberid'];
		$objParty->additional_notes=$Additional_Notes;
		$objParty->star_ranking=$Star_Ranking;
		$objParty->inquiry_status=$Inquiry_Status;
		$objParty->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
		for ($i = 1; $i <= 4; $i++) {
	    $imageField = 'Image' . $i;
	    $hiddenField = 'Image' . $i . 'hidden';

	    if (!empty($_FILES[$imageField]['name'])) {
	        $objParty->{'image' . $i} = uploadImage($imageField, $rnd . $_FILES[$imageField]['name'], IMAGE_PATH, "", "");
	    } else {
	        if (!empty($$hiddenField)) {
	            $objParty->{'image' . $i} = $$hiddenField;
	        } else {
	            $objParty->{'image' . $i} = NULL;
	        }
	    }
	}
	
	if(isset($_POST['btnAdd'])){
		$objParty->insert();
		// =============== Send WhatsApp SMS to the customer ==================
		$obj_admin->id=$Sales_Person;
        $salesPerDet = $obj_admin->selectRecById();
        $sales_contact_no = "91" . $salesPerDet[0]['adminContact'];
        $sales_person = $salesPerDet[0]['adminName'];
        // echo "Sales person contact : " . $sales_contact_no;
        
        $obj_admin->id=$_SESSION['memberid'];
        $teleCallerDet = $obj_admin->selectRecById();
        
        $telecaller_contact_no = "91" . $teleCallerDet[0]['adminContact'];
        // echo "Telecaller No. : " . $telecaller_contact_no . "<br/>";
        $telecaller_name = $teleCallerDet[0]['adminName'];
        // echo "Telecaller Name : " . $telecaller_name;
		
		$contact_no = "91" . $Contact_Number;

		$curl = curl_init();
		curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS =>'{
			"to": "' . $contact_no . '",
			"recipient_type": "individual",
			"type": "template",
			"template": {
				"language": {
					"policy": "deterministic",
					"code": "en"
				},
				"name": "appreciate",
				"components": [
					{
						"type": "body",
						"parameters": [
							{
								"type": "text",
								"text": "' . $sales_person . '"
							},
							{
								"type": "text",
								"text": "' . $sales_contact_no . '"
							}
						]
					}
				]
			}
		}',
		CURLOPT_HTTPHEADER => array(
			'API-KEY: 64d0e43cfff6e0d950e3be05',
			'Content-Type: application/json',
			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
		),
		));

		$response = curl_exec($curl);
		curl_close($curl);
		// ===== End - Send Whatsapp SMS to the customer ===========
		
		//echo "<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>";
		
		// ============================== Test SMS =================
        // $mobile = $Contact_Number;
        // echo $mobile . "<br/><br/>";
        // $message = "Thank%20you%20for%20connecting%20Meena%20gold%20buyer.%20We%20appreciate%20your%20time%20and%20trust%20in%20our%20service.%20Our authorized executive%20%0AExecutive%20name%20$sales_person%0AMobile%20Number%20$sales_contact_no%0Awill%20connect%20soon.%20if%20any%20urgency%20pls%20call%20this%20number.%20Our%20executive%20will%20help%20you%20sell%20your%20gold";
        // $message = "Dear%20Meena%20Gold%20buyer%20team%20please%20contact%20this%20customer%20%0ACustomer%20name%20123%0AMobile%20number%20$mobile%0A%0AThanks";
        // $encodedMessage = urlencode($message);
        // echo $encodedMessage; exit;
        // Build the full API URL
        // $url = "http://sms.mobileadz.in/api/push.json?apikey=$apiKey&route=$route&sender=$sender&mobileno=$mobile&text=$encodedMessage";
        // $url = "http://sms.mobileadz.in/api/push.json?apikey=687dde5ae7ab3&route=promo_dnd&sender=MNAGLD&mobileno=$mobile&text=$message";

        // Initialize cURL
        // $ch = curl_init();
        
        // // Set cURL options
        // curl_setopt($ch, CURLOPT_URL, $url);
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // // Execute request
        // $response = curl_exec($ch);
        
        // // Check for cURL errors
        // if (curl_errno($ch)) {
        //     echo 'Curl error: ' . curl_error($ch);
        // } else {
        //     echo 'Response: ' . $response;
        // }
        
        // // Close cURL
        // curl_close($ch);
        // ================= End of Test SMS =====================================

		// =============== Send whatsapp SMS to the Sales Executive ==================
        // $salesContact = $obj_admin->getContactById($Sales_Person);
		// $sales_contact_no = "91" . $salesContact;

		$curl = curl_init();
		curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS =>'{
			"to": "' . $sales_contact_no . '",
			"recipient_type": "individual",
			"type": "template",
			 "template": {
                    "language": {
                        "policy": "deterministic",
                        "code": "en"
                    },
                    "name": "customer_detail",
                    "components": [
                        {
                            "type": "body",
                            "parameters": [
                                {
                                    "type": "text",
                                    "text":  "' . $Customer_Name . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Contact_Number . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Alternative_Number . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Area . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $City . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Gold_Weight . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Loan_Amount . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Bank_Name . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Additional_Notes . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Source . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $telecaller_name . '"
                                }
                            ]
                        }
                    ]
                }
		}',
		CURLOPT_HTTPHEADER => array(
			'API-KEY: 64d0e43cfff6e0d950e3be05',
			'Content-Type: application/json',
			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
		),
		));
        // "text": "Name : ' . $Customer_Name . ', Contact No. : ' . $Contact_Number . ', Email : ' . $Email_Address . '"
		$response = curl_exec($curl);
 		curl_close($curl);
        // echo $response; exit;
		// ===== End - whatsap Send SMS to the Sales Person ===========
		
		// ============================== Test SMS =================
        // $mobile =$salesPerDet[0]['adminContact'];
        // $message = "Dear%20Meena%20Gold%20buyer%20Executive / Customer details shared you / please contact client as soon as possible.\nCustomer Name $Customer_Name\nPhone number $Contact_Number\nAlternative Number $Alternative_Number\nArea $Area\nCity\nGold Weight $Gold_Weight\nLoan Amount $Loan_Amount\nBank / Pedhi Name $Bank_Name\nAdditional Notes $Additional_Notes\nSource $Source\nFrom Telecaller $telecaller_name\nThanks";
        // $encodedMessage = urlencode($message);
        
        // // Build the full API URL
        // // $url = "http://sms.mobileadz.in/api/push.json?apikey=$apiKey&route=$route&sender=$sender&mobileno=$mobile&text=$encodedMessage";
        // $url = "http://sms.mobileadz.in/api/push.json?apikey=687dde5ae7ab3&route=promo_dnd&sender=MNAGLD&mobileno=$mobile&text=Dear%20Meena%20Gold%20buyer%20team%20please%20contact%20this%20customer%20%0ACustomer%20name%20123%0AMobile%20number%20123%0A%0AThanks";

        // $message = "Dear%20Meena%20Gold%20buyer%20Executive%20/%20Customer%20details%20shared%20you%20/%20please%20contact%20client%20as%20soon%20as%20possible.%20Customer%20Name$Customer_Name%20Phone%20number[broad]Alternative%20Number[broad]Area[broad]City[broad]Gold%20Weight[broad]Loan%20Amount[broad]Bank%20/%20Pedhi%20Name[broad]Additional%20Notes[broad]Source[broad]From%20Telecaller[broad]Thanks";

        // // // Initialize cURL
        // $ch = curl_init();
        
        // // Set cURL options
        // curl_setopt($ch, CURLOPT_URL, $url);
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // // Execute request
        // $response = curl_exec($ch);
        
        // // Check for cURL errors
        // if (curl_errno($ch)) {
        //     echo 'Curl error: ' . curl_error($ch);
        // } else {
        //     echo 'Response: ' . $response;
        // }
        
        // // Close cURL
        // curl_close($ch);
        // ================= End of Test SMS =====================================
        // exit;
		redirect("codeManageParty.php?msg=add");			
	}

	if(isset($_POST['btnUpdate'])){

		if($_SESSION['memberrole']=="Tele Caller"){
			$objParty->updateByTelecaller();
		}elseif($_SESSION['memberrole']=="Sales Person"){
			$objParty->updateBySalesperson();
			
    		if($Inquiry_Status == "Close"){
    		    // =============== Send SMS to the customer ==================
        		$contact_no = "91" . $Contact_Number;
        
        		$curl = curl_init();
        		curl_setopt_array($curl, array(
        		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
        		CURLOPT_RETURNTRANSFER => true,
        		CURLOPT_ENCODING => '',
        		CURLOPT_MAXREDIRS => 10,
        		CURLOPT_TIMEOUT => 0,
        		CURLOPT_FOLLOWLOCATION => true,
        		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        		CURLOPT_CUSTOMREQUEST => 'POST',
        		CURLOPT_POSTFIELDS =>'{
        			"to": "' . $contact_no . '",
        			"recipient_type": "individual",
        			"type": "template",
        			"template": {
        				"language": {
        					"policy": "deterministic",
        					"code": "en"
        				},
        				"name": "thank_you_message",
        				"components": []
        			}
        		}',
        		CURLOPT_HTTPHEADER => array(
        			'API-KEY: 64d0e43cfff6e0d950e3be05',
        			'Content-Type: application/json',
        			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
        		),
        		));
        
        		$response = curl_exec($curl);
        		echo $response;
        		curl_close($curl);
        		// =============== End - Send SMS to the customer ===========
        		
        		// ============================== Test SMS =================
                // $mobile =$Contact_Number;
                // $message = "Thank you for the successful deal. We appreciate your trust in us!\nPls follow us on our instagram account \nhttps://www.instagram.com/meenagoldbuyer_official?igsh=MWdmd3VjMmU0aG1ncQ==\nHeartly Thanks\nTeam :  meena gold buyer";
                // $encodedMessage = urlencode($message);
                
                // // Build the full API URL
                // // $url = "http://sms.mobileadz.in/api/push.json?apikey=$apiKey&route=$route&sender=$sender&mobileno=$mobile&text=$encodedMessage";
                // $url = "http://sms.mobileadz.in/api/push.json?apikey=687dde5ae7ab3&route=promo_dnd&sender=MNAGLD&mobileno=$mobile&text=$encodedMessage";
        
                // // // Initialize cURL
                // $ch = curl_init();
                
                // // Set cURL options
                // curl_setopt($ch, CURLOPT_URL, $url);
                // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                
                // // Execute request
                // $response = curl_exec($ch);
                
                // // Check for cURL errors
                // if (curl_errno($ch)) {
                //     echo 'Curl error: ' . curl_error($ch);
                // } else {
                //     echo 'Response: ' . $response;
                // }
                
                // // Close cURL
                // curl_close($ch);
                // ================= End of Test SMS =====================================

        		// =============== Send SMS to Perticular Number==================
                $obj_admin->id=$Sales_Person;
                $salesPerDet = $obj_admin->selectRecById();
                $sales_contact_no = $salesPerDet[0]['adminContact'];
                $sales_person = $salesPerDet[0]['adminName'];
                
                // $to_contact_no = "919737622522"; // original number
                $to_contact_no = "919879537100"; // temporary number
                
                // $salesContact = $obj_admin->getContactById($Sales_Person);
        		// $sales_contact_no = "91" . $salesContact;
        
        		$curl = curl_init();
        		curl_setopt_array($curl, array(
        		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
        		CURLOPT_RETURNTRANSFER => true,
        		CURLOPT_ENCODING => '',
        		CURLOPT_MAXREDIRS => 10,
        		CURLOPT_TIMEOUT => 0,
        		CURLOPT_FOLLOWLOCATION => true,
        		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        		CURLOPT_CUSTOMREQUEST => 'POST',
        		CURLOPT_POSTFIELDS =>'{
        			"to": "' . $to_contact_no . '",
        			"recipient_type": "individual",
        			"type": "template",
        			 "template": {
                        "language": {
                            "policy": "deterministic",
                            "code": "en"
                        },
                        "name": "21_june_contact_details",
                        "components": [
                            {
                                "type": "body",
                                "parameters": [
                                    {
                                        "type": "text",
                                        "text": "' . $Customer_Name . '"
                                    },
                                    {
                                        "type": "text",
                                        "text": "' . $Contact_Number . '"
                                    }
                                ]
                            }
                        ]
                    }
        		}',
        		CURLOPT_HTTPHEADER => array(
        			'API-KEY: 64d0e43cfff6e0d950e3be05',
        			'Content-Type: application/json',
        			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
        		),
        		));
                // "text": "Name : ' . $Customer_Name . ', Contact No. : ' . $Contact_Number . ', Email : ' . $Email_Address . '"
        		$response = curl_exec($curl);
        		curl_close($curl);
        		// echo $response; exit;
        		// ===== End - Send SMS to the Sales Person ===========
        		
        		
        		// ============================== Test SMS =================
                // $mobile =$to_contact_no;
                // $message = "Dear Meena Gold buyer team please contact this customer\nCustomer name $Customer_Name\nMobile number $Contact_Number\nThanks";
                // $encodedMessage = urlencode($message);
                
                // // Build the full API URL
                // // $url = "http://sms.mobileadz.in/api/push.json?apikey=$apiKey&route=$route&sender=$sender&mobileno=$mobile&text=$encodedMessage";
                // $url = "http://sms.mobileadz.in/api/push.json?apikey=687dde5ae7ab3&route=promo_dnd&sender=MNAGLD&mobileno=$mobile&text=$encodedMessage";
        
                // // // Initialize cURL
                // $ch = curl_init();
                
                // // Set cURL options
                // curl_setopt($ch, CURLOPT_URL, $url);
                // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                
                // // Execute request
                // $response = curl_exec($ch);
                
                // // Check for cURL errors
                // if (curl_errno($ch)) {
                //     echo 'Curl error: ' . curl_error($ch);
                // } else {
                //     echo 'Response: ' . $response;
                // }
                
                // // Close cURL
                // curl_close($ch);
                // ================= End of Test SMS =====================================
        		
        		
    		}
		}
	    elseif($_SESSION['memberrole']=="Admin"){
	        $objParty->updateBySalesperson();
	    }else{
			$objParty->update();
		}
		
		redirect("codeManageParty.php?msg=edit");
	}

	if(isset($_POST['btnAction'])){
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objParty->deleteSelect($chkAction);
					break;
			case 1:
					$objParty->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objParty->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageParty.php?msg=edit");
	}	

	if(isset($_GET['delete'])){			 
		$objParty->delete();
		redirect("codeManageParty.php?msg=del");
	}	

	if(isset($_GET['status'])){			 
		$objParty->status();
		redirect("codeManageParty.php?msg=status");		
	}

	if(isset($_GET['id'])){			
		$listEdit=$objParty->selectRecById();						
	}	
	
	if(isset($_GET['pid'])){	
		$objParty->id=$_GET['pid'];
		$listEdit=$objParty->selectRecById();						
	}

    $query = " where id > 0 ";
	$from_date = '';
	$to_date = '';
	if(!empty($_GET['from_date']) && !empty($_GET['to_date'])) {
        $from_date = $_GET['from_date'];
        $to_date = $_GET['to_date'];
        $query .= " AND  DATE(`created_at`) BETWEEN '$from_date' AND '$to_date'";
    }
    
    if(!empty($_GET['Search_Status']) && !empty($_GET['Search_Status'])) {
        $query .= " AND inquiry_status = '" . $_GET['Search_Status'] . "'";
    }

    if($_SESSION['memberrole']=="Admin"){
        if($_GET['search_type']=="Sales Person"){
    		$query .= " AND sales_person = " . $_GET['SearchPerson'];
    	}elseif($_GET['search_type']=="Tele Caller"){
    		$query .= " AND tele_caller = " . $_GET['SearchPerson'];
    	}
    }elseif($_SESSION['memberrole']=="Sales Person"){
 		$query .= " AND sales_person = " . $_SESSION['memberid'];
 	}elseif($_SESSION['memberrole']=="Tele Caller"){
 		$query .= " AND tele_caller = " . $_SESSION['memberid'];
 	}
    $listRec=$objParty->selectByQuery($query);


	include("html/frmManageParty.php");
} 
?>