<?	
	$heading="Manage Chitthibook ";
	include("inc/fileInclude.php");
    include("inc/clsObj.php");	

	$cid = explode(",",$_GET['cid']);

for($i=0;$i<count($cid);$i++)
{
	$objChitthi->id = $cid[$i];
	$listEdit=$objChitthi->selectRecById();

	$objChitthi->cid = $listEdit[0]['id'];
	$listItems=$objChitthi->selectItemsByChitthiNo();	
?>
<div id="form_container" style="width:800px;">
<fieldset>
<!--	<legend>&nbsp;&nbsp;&nbsp;Chitthibook&nbsp;&nbsp;&nbsp;</legend>-->
<div style="margin:10px;">
<link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<table width="600" border="0" cellspacing="5" cellpadding="0">
  <tr class="toprow">
  	<td align="left" valign="top" style="font-size:12px;"><strong>Date :</strong></td>
    <td colspan="2" align="left"><table width="100%" cellpadding="0" cellspacing="0">
    <tr><td width="21%" style="text-align:left; font-size:12px; font-weight:bold;"><?=$listEdit[0]['bdt'];?></td>
      <td width="79%" style="text-align:right; font-size:12px;"><strong>No. : <? echo $listEdit[0]['id']; ?></strong></td>
    </tr></table></td>
    </tr>  
  <tr style="text-align:left; font-size:12px;">
  	<td width="18%" align="left" valign="top"><strong>Party Name :</strong></td>
    <td width="20%" align="left" valign="top">
	<strong><? echo $listEdit[0]['party']; ?></strong>
	</td>
    <td width="62%" align="left" valign="top">&nbsp;</td>
    </tr>
</table>
<br/>
<table width="600" border="1" cellspacing="0" cellpadding="0" class="maintable">
  <tr><td colspan="2">
	<div>
  <table width="100%" cellpadding="5" cellspacing="0" border="1" style="border-collapse:collapse;" bordercolor="#999999">
      <tr class="toprow">
      	<td width="3%" style="font-size:12px;"><strong>No.</strong></td> 	<td width="17%" style="font-size:12px;"><strong>Particulars</strong></td>
	   <td width="10%" style="font-size:12px;"><strong>Sale Qty/Unit</strong></td>
        <td width="7%" style="font-size:12px;"><strong>KG</strong></td>
        <td width="7%" style="font-size:12px;"><strong>Total KG</strong></td>
        <td width="7%" style="font-size:12px;"><strong>Your Price</strong></td>
		<td width="7%" style="font-size:12px;"><strong>Amount</strong></td>     
        </tr>
<? 
// $cart =$_SESSION['cart'];
$total_amt = 0;
for($c=0;$c<count($listItems);$c++)
{ ?>
<tr class="innerrow">  
  	<td><?=$c+1;?>&nbsp;</td>
	<td style="text-align:left;">&nbsp;
    <? 	$objItem->id = $listItems[$c]['item'];
		$item_name=$objItem->selectRecById(); 
		echo $item_name[0]['item_name'];
	?>    </td>
	<td width="10%" style="text-align:right;"><?=$listItems[$c]['sale_qty'];?>&nbsp;&nbsp;&nbsp;</td>
    <td style="text-align:right;"><?=$listItems[$c]['weight'];?>&nbsp;&nbsp;&nbsp;</td>
    <td style="text-align:right;"><?=number_format(($listItems[$c]['sale_qty'] * $listItems[$c]['weight']),2,'.','');?>&nbsp;</td>
    <td style="text-align:right;"><?=$listItems[$c]['amount'];?>&nbsp;</td>
	<td style="text-align:right;"><?=number_format((($listItems[$c]['sale_qty'] * $listItems[$c]['weight']) * $listItems[$c]['amount']),2,'.','');?>&nbsp;</td>
    </tr>
<? $total_amt+=(($listItems[$c]['sale_qty'] * $listItems[$c]['weight']) * $listItems[$c]['amount']);
} 
$total_amt+=$listEdit[0]['extra_amount'];
?>          
<tr><td width="86%" height="30" style="text-align:right; font-size:12px; text-align:right;" colspan="6"><strong>Remarks : </strong>&nbsp;<?=$listEdit[0]['remarks'];?>
</td>
<td style="text-align:right; font-size:12px; text-align:right;"><?=number_format($listEdit[0]['extra_amount'],2,'.','');?></td>
</tr>
<tr><td height="30" style="text-align:right; font-size:12px;" colspan="6"><strong>Total Amount :</strong></td>
<td style="text-align:right; font-size:12px; font-weight:bold;"><?=number_format($total_amt,2,'.','');?></td>
</tr>
    </table>
	</div>  
  </td>
  </tr>
</table>
</div>
</fieldset>
</div>
<? 
}
?>
<script language="javascript">
	window.print();
</script>