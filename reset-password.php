<?php session_start();  
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
if(isset($_POST['btnRetrivePassword']))	
	{
		$obj_admin->adminEmail=$_POST['Email_Address'];
		$rs=$obj_admin->forgotPassoword();
		if(count($rs)>0)
		{
			$username = $rs[0]['adminUsername'];
			$password = decryptPassword($rs[0]['adminPassword']); 
			//echo $username."<br/>".$password; exit;
			$to=$_POST['Email_Address'];
			$headers  = "MIME-Version: 1.0\r\n";
			$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
			$headers .= "To:$to\r\n";
			$headers .= "From:ERP Demo<<EMAIL>>\r\n";
			$subject="Reset password";
			$message="Now you can login in ERP Demo ERP admin panel using following login details.";
			$message.="<br>Password : $password";
			//echo $message; exit;
			mail($to, $subject, $message, $headers);
			
			/* $to = "<EMAIL>";
			$headers  = "MIME-Version: 1.0\r\n";
			$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
			$headers .= "To:$to\r\n";
			$headers .= "From:ERP Demo<<EMAIL>>\r\n";
			$subject="Admin Forgot password";
			$message="Now you can login in ERP Demo ERP admin panel using following login details.";
			$message.="<br><br>Username : $username<br/>Password : $password";
			mail($to, $subject, $message, $headers); */
		?>
			<script type="text/javascript">
				alert("The login details has been sent to <?=$_POST['Email_Address'];?>");
				document.location = "index.php?sent=true";
			</script>
	<? }
		else
		{ ?>
			<script type="text/javascript">
				//alert("Invalid Email address, this kind of address not found in the record.");
				document.location = "reset-password.php?error=true";
			</script>
		<? }
} ?>
<!DOCTYPE html>
<html lang="en">
<head>
	<title>Reset Password</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	
	<!-- Favicon icon -->
	<link rel="icon" href="assets/images/logo/favicon.png" type="image/x-icon">

	<!-- vendor css -->
	<link rel="stylesheet" href="assets/css/style.css">
	
	<!-- custom css -->
    <link rel="stylesheet" href="assets/css/custom.css">
</head>
<body>	
	<!--========== Reset Password Section ==========-->
	<div class="auth-wrapper">
		<div class="auth-content">
			<div class="card">
				<div class="row align-items-center">
					<div class="col-md-12">
                    	<form action="" method="post" name="frmFgtPassword" id="frmFgtPassword">
                            <div class="card-body">
                                <div class="text-center">
                                    <img src="assets/images/logo/logo.png" alt="logo-login" class="img-fluid mb-1">
                                </div>
                                <h4 class="mb-3 f-w-400 text-center">FORGOT LOGIN DETAILS ?</h4>
                                <div style="clear:both;"></div>
                                <div style="text-align:center; color:#CC0000; font-weight:bold;" id="replaceme">
								<?	if(isset($_GET['error']) && $_GET['error']=='true')
                                        echo "<div id='logout'>Invalid Email Id.</div>";	?>
                                </div>
                                <div style="clear:both;"></div>
                                <div class="form-group mb-4">
                                    <label>Enter Register Email To Get Password</label>
                                    <input type="email" class="form-control" name="Email_Address" id="Email_Address" required>
                                </div>
                                <button type="submit" name="btnRetrivePassword" id="btnRetrivePassword" class="btn btn-block btn-success mb-4">Send Password</button>
                                <p class="mb-0 text-muted">
                                    <a href="index.php" class="f-w-400"><i class="fa fa-chevron-left mr-5"></i>Back to Login</a>
                                </p>
                            </div>
						</form>
                    </div>
				</div>
			</div>
		</div>
	</div>	
	<!--========== Reset Password Section ==========-->
	<!-- Required Js -->
	<script src="assets/js/vendor-all.min.js"></script>
	<script src="assets/js/plugins/bootstrap.min.js"></script>
	<script src="assets/js/ripple.js"></script>
	<script src="assets/js/pcoded.min.js"></script>
</body>
</html>