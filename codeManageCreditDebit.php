<?php 
include("template.php");
function main()
{
	$heading="Manage Cashbook ";
    include("inc/clsObj.php");	
	
	extract($_POST);	
	$cdate = explode("-",$Transaction_Date);
	$transaction_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

	$objCash->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	
	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objCash->deleteSelect($chkAction);
				break;
			case 1:
				$objCash->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objCash->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
					
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManageCreditDebit.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objCash->party=$_GET['delete'];
		$objCash->deleteByParty();
		redirect("codeManageCreditDebit.php?msg=del");
	}	

	if(isset($_GET['status']))
	{
		$objCash->status();
		redirect("codeManageCreditDebit.php?msg=status");
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objCash->selectRecById();

		$objCash->cid = $listEdit[0]['id'];
		$listItems=$objCash->selectRecById();	
	}
	$listRec=$objCash->pagingAccountStatus();
	$listOtherRec=$objChitthi->noCashbookRecords();

    include("html/frmManageCreditDebit.php");
} 
?>
