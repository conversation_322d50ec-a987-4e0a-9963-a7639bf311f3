<?php 
include("template.php");
function main()
{
	$pageName="codeInvoiceReport.php";	
    include("inc/clsObj.php");	

	extract($_POST);	
	$cdate = explode("-",$Transaction_Date);
	$transaction_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

	$objCash->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objCash->party=$Party_Name;
	if($Credit_Debit==0)
	{
		$objCash->credit_amount=$Amount;
		$objCash->debit_amount=0;
	}
	elseif($Credit_Debit==1)
	{
		$objCash->credit_amount=0;
		$objCash->debit_amount=$Amount;
	}
	$objCash->cheque_cash=$Cash_Cheque;
	$objCash->cheque_no=$Cheque_No;
	$objCash->cheque_detail=$Cheque_Detail;
	$objCash->transaction_detail=$Transaction_Detail;
	$objCash->transaction_date=$transaction_date;
	
	if(isset($_POST['btnAdd']))
	{
		$objCash->insert();
		redirect("codeManagePartyReport.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		// echo "test"; die;
		
		$objCash->update();
		redirect("codeManagePartyReport.php?msg=edit");
	}

	if(isset($_GET['btnReport']))
	{
		redirect("print_party_details.php?pid=".$_GET['Party_Name']);
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objCash->deleteSelect($chkAction);
				break;
			case 1:
				$objCash->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objCash->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
					
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManagePartyReport.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objCash->deleteById();
		$objCash->deleteItemsById();
		redirect("codeManagePartyReport.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objCash->status();
		redirect("codeManagePartyReport.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objCash->selectRecById();

		$objCash->cid = $listEdit[0]['id'];
		$listItems=$objCash->selectRecById();
	}
	elseif(isset($_GET['search']) && $_GET['search']!="")
	{
		$query = "";
		/* if($_GET['Party_Name']!="")
				$query.=" and party='".$_GET['Party_Name']."'"; */
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fd = explode("-",$_GET['From_Date']);
			$fdate = $fd[2]."-".$fd[1]."-".$fd[0];
			$td = explode("-",$_GET['To_Date']);
			$tdate = $td[2]."-".$td[1]."-".$td[0];
			//$query.=" and date_format(`bill_date`, '%d-%m-%Y') between '".$_GET['From_Date']."' and '".$_GET['To_Date']."'";
			$query.=" and `bill_date` between '".$fdate."' and '".$tdate."'";
		}
		
		if(!empty($_GET['chkServiceTax']))
			$query_tt[] = " stax_amt > 0 ";
		if(!empty($_GET['chkSBKKTax']))
			$query_tt[] = " (sbtax_amt > 0 || kkctax_amt > 0) ";
		if(!empty($_GET['chkEduTax']))
			$query_tt[] = " (edu_amt > 0 || hsedu_amt > 0) ";
		if(!empty($_GET['chkGST']))
			$query_tt[] = " (cgst_amt > 0 || sgst_amt > 0) ";
		if(!empty($_GET['chkIGST']))
			$query_tt[] = " (igst_amt > 0) ";
		
		if(count($query_tt)>0)
			$query.=" and (".implode(" or ",$query_tt).")";
		
		$query.=" and financial_year = '".$_SESSION['fyear']."'";
		
		// $objCash->party = $_GET['party'];
		//echo $query; die;
		$listRec=$objBill->selectServiceTax($query);
    }
	else
	{
	
		//$query.=" and financial_year ='".$_SESSION['fyear']."'";
		// $listRec=$objCash->paging();
		//$listRec=$objBill->selectServiceTax($query);
		$listRec="";
	}
	include("html/frmInvoiceReport.php");
} 
?>