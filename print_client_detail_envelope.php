<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();
?>

<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}

td
{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>
<?	if($_GET['Party_Name']!=""){
			$query.=" and id='".$_GET['Party_Name']."'";
			$listRec=$objParty->selectRecBySearch($query);
	}
	else
		$listRec=$objParty->select(); ?>

<div style="width:100%;" id="print_header">
    <div style="width:300px; margin:230px auto; border:#999999 0px solid; transform:rotate(90deg); padding:150px 0 120px 0;">
        <strong>To,<br/><?=$listRec[0]['party_name'];?></strong><br/><br/>
        <?=$listRec[0]['party_address'];?><br/>
        <?=$listRec[0]['city'];?> - <?=$listRec[0]['zipcode'];?><br/>
        <?=$objState->getNameById($listRec[0]['state']);?>, <?=$listRec[0]['country'];?> <br/>
        <?=$listRec[0]['contact_name1'];?> <em>(<?=$listRec[0]['contact_no1'];?>)</em><br/>
    </div>
</div>
<script language="javascript">
	window.print();
</script>
<? // } ?>