<?php
include("template.php");
function main()
{
	$heading="Manage Category ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$objCat->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objCat->cat_name=$Category_Name;	
	$objCat->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objCat->insert();	
		redirect("codeManageCategory.php?msg=add");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objCat->update();
		 redirect("codeManageCategory.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objCat->deleteSelect($chkAction);
					break;
			case 1:
					$objCat->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objCat->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageCategory.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objCat->delete();
		redirect("codeManageCategory.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objCat->status();
		redirect("codeManageCategory.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objCat->selectRecById();						
	}	
	$listRec=$objCat->paging();
    include("html/frmManageCategory.php");
 } 
?>