<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Area ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$objArea->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objArea->area_name=$Area_Name;	
		
	$objArea->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objArea->insert();	
		redirect("codeManageArea.php?msg=add");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objArea->update();
		 redirect("codeManageArea.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objArea->deleteSelect($chkAction);
					break;
			case 1:
					$objArea->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objArea->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageArea.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objArea->delete();
		redirect("codeManageArea.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objArea->status();
		redirect("codeManageArea.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objArea->selectRecById();						
	}	
	elseif($_GET['Area_Name']!="")
	{
		$objArea->id = $_GET['Area_Name'];
		$listRec=$objArea->selectRecById();
    }
	else
		$listRec=$objArea->paging();
		
    include("html/frmManageArea.php");
 } 
?>