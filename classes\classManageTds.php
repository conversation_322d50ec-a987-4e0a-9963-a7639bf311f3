<?php
global $db;
class tdsMaster
{
   /* Variable Declaration */		
	var $tablename='tds';
	var $id;
	var $party;
	var $credit_amount;
	var $debit_amount;
	var $cheque_cash;
	var $cheque_detail;
	var $transaction_detail;
	var $transaction_date;
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function tdsMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
				'$this->company',
				'$this->financial_year',
				'$this->party',
				'$this->deduction_type',
				'$this->currency',
				'$this->currency_inr',
				'$this->credit_amount',
				'$this->debit_amount',
				'$this->cheque_cash',
				'$this->cheque_no',
				'$this->cheque_detail',
				'$this->transaction_detail',
				'$this->transaction_date')";
		// echo $sql; die();
		$id=$this->db->insert($sql);
		//mysql_insert_id();
		return($id);
	} 			

	/* update the record in database */		
	function update()
	{
		$sql = "update `$this->tablename` set
						`party`='$this->party',
						`deduction_type`='$this->deduction_type',
						`currency`='$this->currency',
						`currency_inr`='$this->currency_inr',
						`credit_amount`='$this->credit_amount',
						`debit_amount`='$this->debit_amount',
						`cheque_cash`='$this->cheque_cash',
						`cheque_no`='$this->cheque_no',
						`cheque_detail`='$this->cheque_detail',
						`transaction_detail`='$this->transaction_detail',
						`transaction_date`='$this->transaction_date' 
						where `id`=$this->id";
		// echo $sql;die();
		$this->db->edit($sql);		
		return true;
	}	

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select *, date_format(`transaction_date`, '%d-%m-%Y') as trn_dt from `$this->tablename` where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectDateByParty()
	{
		$sql ="select date_format(`transaction_date`, '%d-%m-%Y') as tdate from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and financial_year = '".$_SESSION['fyear']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectSearchDateByParty($query)
	{
		$sql ="select date_format(`transaction_date`, '%d-%m-%Y') as tdate from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' ".$query;
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}


	/*Fectch record by id from Database*/		
	function selectRecByPartyDate()
	{
		$sql ="select *, date_format(`transaction_date`, '%d-%m-%Y') as bdt from `$this->tablename` where party = '$this->party' and `transaction_date` like '%$this->tdt%' and company = '".$_SESSION['company']."'";
		// echo $sql."<br/>"; //die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	
	
	/*Fectch Details by party from Database*/		
	function selectDetByParty()
	{
		$sql ="select chb.id as chid, chb.party as party, cd.id as cdid, cd.cid as cid, (cd.sale_qty * cd.weight) * cd.amount as total_amount, cd.amount as amount from chitthibook_details cd  left join  chitthibook chb on chb.id = cd.cid where chb.party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch Details by party from Database*/		
	function selectRecByParty()
	{
		$sql ="select * from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch Details by party from Database*/		
	function selectSumByParty()
	{
		$sql ="select *, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount, SUM(credit_amount * currency_inr) as tot_credit, SUM(debit_amount * currency_inr) as tot_debit  from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	
	/*Fectch Details by party from Database*/		
	function selectSumByPartyExp()
	{
		// $sql ="select *, Sum(credit_amount * currency_inr) as credit_amount, Sum(debit_amount * currency_inr) as debit_amount  from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		$sql ="select *, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount  from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch Details by party from Database*/		
	function selectSumByPartyAll()
	{
		$sql ="select *, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount  from `$this->tablename` where party='$this->party'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	
	
		/*Fectch Details by party from Database*/		
	function selectSumByPartyBeforeDate()
	{
		$sql ="select *, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount  from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and transaction_date < '$this->bdate'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}		

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";
		$this->db->sql_query($sql);
	}	

	/*delete a record from database*/	
	function deleteByParty()
	{
		$sql="delete from `$this->tablename` 
			  where `party`='".$this->party."'";
		$this->db->sql_query($sql);
	}			

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql="delete from `$this->tablename` where `id` = '$id'";
			$this->db->sql_query($sql);
		}
		return true;
	}	
					
	/*...paging...*/
	function paging()
	{	
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."'";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	
	/*...paging...*/
	function pagingExp()
	{	
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where deduction_type = 'Exp' and company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."'";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}

		/*...paging...*/
	function paging_report()
	{	
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."'";
		// echo $pages->sql; die;
		$result=$this->db->select($pages->sql);
		return($result);
	}

	/*...report...*/
	function selectSumTds()
	{	
		$pages->sql ="select *, Sum(credit_amount) as credit_sum, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."'";
		// echo $pages->sql; die;
		$result=$this->db->select($pages->sql);
		return($result);
	}


	/*...paging...*/
	function selectRecBySearch($query)
	{
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where company = '".$_SESSION['company']."' and deduction_type != 'Exp' ".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*...paging...*/
	function selectRecBySearchExp($query)
	{
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where company = '".$_SESSION['company']."' and deduction_type = 'Exp' ".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*...paging...*/
	function pagingAccountStatus()
	{
		$pages = new Paging();
		$pages->sql ="select party, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount from `cashbook` where company = '".$_SESSION['company']."' group by party";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}	
}		
?>   