<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>Manage Bill</h5>
							</div>
							<div class="col-md-6">
								
								<? if(isset($_REQUEST['btnAddUser'])){ ?>
								
									<form action="<?=$pageName;?>" method="post">
										<button type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack">
											<i class="fas fa-angle-left"></i> Back
										</button>
									</form>
								
								<? }
								else{?>
								
									<form action="<?=$pageName;?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add Bill
										</button>
									</form>									
								
								<? } ?>
								
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<? if(isset($_REQUEST['btnAddUser'])){ ?>
						
						<!--========== Add Bill ==========-->
						<form action="codeManageBillGST.php" method="post" name="frmBill" id="frmBill" onsubmit="MM_validateForm();return document.MM_returnValue">
                        	<input type="text" name="hid" id="hid" value="<?=$_GET['id'];?>" />
                            <input type="text" name="Total_Items" id="Total_Items" value="<?=(isset($_GET['id'])) ? (count($listItems)==0) ? 1 : count($listItems) : 1;?>" />
<input type="hidden" name="Total_Default_Items" id="Total_Default_Items" value="<?=(isset($_GET['id'])) ? (count($listItems)==0) ? 1 : count($listItems) : 1;?>" />
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                	<? 
										$objBill->company=$_SESSION['company'];
										$objBill->fyear=$_SESSION['fyear'];
										$billRec=$objBill->selectRecByCompanyYear(); 
										//============generate bill no.=========
										$objYear->company=$_SESSION['company'];
										$objYear->fyear=$_SESSION['fyear'];
										$billId=$objYear->selectBidByCompanyYear(); 
										$new_bill_no = sprintf('%03d',$billId[0]['last_bill_id']+1);	
									
										
										$invoice_no="TS/".$_SESSION['fyear']."/";
										$invoice_no.=$new_bill_no;	
									?>
                                    <label>Invoice No</label>
                                    <input type="text" name="Invoice_No" id="Invoice_No" size="30"  value="<?=isset($listEdit) ? $listEdit[0]['invoice_no'] : $invoice_no;?>"  readonly  class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                <? $listParty=$objParty->selectStatus();?>
                                    <label>Name of the Client</label>
                                    <select name="Party_Name" id="Party_Name" class="js-example-basic-single form-control col-sm-12">
                                    	<option value="">--Select Party--</option>
										<? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
                                    </select>
                                     <input type="hidden" name="hidden_party" id="hidden_party" value="<?=($listEdit[0]['party']!="") ? $listEdit[0]['party'] : "";?>">
                                </div>
								<div class="form-group col-md-4">
                                    <label>PO/PI Number</label>
                                    <input type="text" name="PO_No" id="PO_No" size="30"  value="<?=isset($listEdit) ? $listEdit[0]['po_no'] : "";?>"  class="form-control">
                                </div>	
								<div class="form-group col-md-4">
                                    <label>Date</label>
                                    <input type="date" name="Bill_Date" id="Bill_Date" size="10" value="<?=($listEdit[0]['bdt']) ? $listEdit[0]['bdt'] : date("d-m-Y");?>"  class="form-control">
                                </div>	
								<div class="form-group col-md-8">
                                    <label>Address</label>
                                    <textarea type="text" id="" name="" class="form-control"></textarea>
                                </div>	
                            </div>
							<hr>
							<? //==================Item #1 ============== ?>
							<div class="form-row" id="item1" style="display:block;">
								<div class="form-group col-md-4">
                                	<? $ItemsRec=$objItem->selectStatus(); ?>
                                    <label>Particulars</label>
									<select name="Item_Name1" id="Item_Name1" onchange="getitemprice(this,1)" class="form-control">
										<option value="">--Select Item--</option>
										<?	for($i=0;$i<count($ItemsRec);$i++){ ?>
                                                <option value="<?=$ItemsRec[$i]['id'];?>" <?=($listItems[0]['item']==$ItemsRec[$i]['id']) ? "selected" : ""; ?>><?=$ItemsRec[$i]['item_name'];?></option>
                                        <?	} ?>
									</select>
                                    <textarea type="text" name="Item_Detail1" id="Item_Detail1" class="form-control mt-5"><?=$listItems[0]['description'];?></textarea>
                                </div>
								<div class="form-group col-md-2">
                                    <label>Rate</label>
                                    <input type="text" name="Rate1" id="Rate1" onblur="billcharges(1)" value="<?=$listItems[0]['rate_per_qty'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-2">
                                    <label>Qty</label>
                                    <input type="text" name="No_of_Items1" id="No_of_Items1" onblur="billcharges(1)" size="5" value="<?=($listItems!=0) ? $listItems[0]['no_of_items'] : 1;?>" class="form-control">
                                </div>
								<div class="form-group col-md-2">
                                    <label>Amount</label>
                                    <input type="text" name="Amount1" id="Amount1" size="10" value="<?=$listItems[0]['amount'];?>" class="form-control">
                                </div>
                                <? if($_SESSION['act_del']==1){ ?>
								<div class="form-group col-md-2 text-center" onclick="delItemGST(1,'<?=($listItems[0]['bid']!="") ? $listItems[0]['bid'] : "";?>','Item','codeManageBillGST.php')" style="cursor:pointer;">
                                    <label>Remove</label><br>
                                    <i class="fas fa-trash-alt clr-red"></i>
                                </div>
                                <? } ?>
							</div>
                            <? //===========End Item #1 ============== ?>
                            <div style="clear:both;"></div>
							<? //===========Item #2 to #10 ============== ?>
                            <? for($i=2;$i<=10;$i++) { ?>
                            <div class="form-row" id="item<?=$i;?>" style="display:<?=(isset($_GET['id']) && count($listItems)>=$i) ? "block" : "none";?>;">
								<div class="form-group col-md-4" style="float:left;">
									<select name="Item_Name<?=$i;?>" id="Item_Name<?=$i;?>" onchange="getitemprice(this,<?=$i;?>)" class="form-control">
										<option value="">--Select Item--</option>
										<?	for($i=0;$i<count($ItemsRec);$i++){ ?>
                                                <option value="<?=$ItemsRec[$i-1]['id'];?>" <?=($listItems[$i-1]['item']==$ItemsRec[$i-1]['id']) ? "selected" : ""; ?>><?=$ItemsRec[$i-1]['item_name'];?></option>
                                        <?	} ?>
									</select>
                                    <textarea type="text" name="Item_Detail<?=$i;?>" id="Item_Detail<?=$i;?>" class="form-control mt-5"><?=$listItems[$i-1]['description'];?></textarea>
                                </div>
								<div class="form-group col-md-2" style="float:left;">
                                    <input type="text" name="Rate<?=$i;?>" id="Rate<?=$i;?>" onblur="billcharges(<?=$i;?>)" value="<?=$listItems[$i-1]['rate_per_qty'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-2" style="float:left;">
                                    <input type="text" name="No_of_Items1" id="No_of_Items1" onblur="billcharges(1)" size="5" value="<?=($listItems!=0) ? $listItems[$i-1]['no_of_items'] : 1;?>" class="form-control">
                                </div>
								<div class="form-group col-md-2" style="float:left;">
                                    <input type="text" name="Amount1" id="Amount1" size="10" value="<?=$listItems[$i-1]['amount'];?>" class="form-control">
                                </div>
                                <? if($_SESSION['act_del']==1){ ?>
								<div class="form-group col-md-2 text-center" onclick="delItemGST(1,'<?=($listItems[$i-1]['bid']!="") ? $listItems[$i-1]['bid'] : "";?>','Item','codeManageBillGST.php')" style="cursor:pointer; float:left;">                                    <i class="fas fa-trash-alt clr-red"></i>
                                </div>
                                <? } ?>
							</div>
                            <div style="clear:both;"></div>
                            <? } ?>
                            <? //=====End of Item #2 to #10 ============== ?>
                            
                            <div class="form-group col-md-12 text-right mb-0">
									<button type="button" class="btn btn-success" onclick="show()">Add More</button>
								</div>
							<hr>

							<div class="form-row">
								<div class="form-group col-md-12">
									<label>Terms & Conditions :</label>
                                    <textarea name="content" id="classic-editor6">
										<ul>
											<li>Cheque/Draft/Pay Order should be drawn in favor  of <strong>"ERP Demo"</strong> payable at Ahmedabad.</li>
											<li>Service void if payment commitment failed, bounced cheque is also failed commitment.</li>
											<li>Bounced cheque will attract charge of Rs. 400/-</li>
											<li>Bulk Mailing is restricted</li>
											<li>Domain, Web Hosting and other services renewal charges may change after one year.</li>
											<li>By using  the services of "ERP Demo" you agree to be bound by the term &amp;  policies listed at <a href="http://www.erpdemocompany123.com/terms.php">www.erpdemocompany123.com/terms.php</a> </li>
											<li>Interest @ 18 % per annum will be charged for delayed payment.</li>
											<li>All disputes are subject to Ahmedabad Jurisdiction</li>
										</ul>
									</textarea>
                                </div>
								<div class="form-group col-md-4">
                                    <label>Taxable Amount : </label>
                                    <input type="text" id="" name="" class="form-control">
                                </div>
								<div class="col-md-8"></div>
								<div class="form-group col-md-1">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" id="">
										<label class="form-check-label">GST ?</label>
									</div>
								</div>
								<div class="col-md-11"></div>
								<div class="form-group col-md-1">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" id="">
										<label class="form-check-label">IGST ?</label>
									</div>
								</div>
								<div class="col-md-11"></div>
								
								<div class="form-group col-md-4">
                                    <label><strong>Total Amount : </strong></label>
                                    <input type="text" id="" name="" class="form-control">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success">Add Bill Details</button>
                        </form>
						<!--========== Add Bill ==========-->
						
						
						<? }
						else {
						?>
						
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form action="#">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
									<label>Search Client Name :</label>
									<select class="js-example-basic-single form-control col-sm-12">
										<option value="AL">Alabama</option>
										<option value="WY">Wyoming</option>
										<option value="WY">Coming</option>
										<option value="WY">Hanry Die</option>
										<option value="WY">PNEUCON PROCESS TECHNOLOGIES</option>
									</select>
								</div>
								<div class="form-group col-md-2">
                                    <label>Invoice No</label>
                                    <input type="text" id="" name="" class="form-control">
                                </div>
								<div class="form-group col-md-3">
									<label>From Date</label>
									<input type="date" class="form-control">
                                </div>
								<div class="form-group col-md-3">
									<label>To Date</label>
									<input type="date" class="form-control">
                                </div>
								<div class="form-group text-center col-md-12">
									<a href="#" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</a>
									<a href="#" class="btn btn-info btn-sm has-ripple">Print</a>
									<a href="#" class="btn btn-info btn-sm has-ripple">Reset</a>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
							<table id="simpletable" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th colspan="6" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													<option>-- Select Action --</option>
													<option value="Delete">Delete</option>
													<option value="Published">Published</option>
													<option value="Unpublished">Unpublished</option>
													<option value="Send Email to Client">Send Email to Client</option>
												</select>
											</label>
											<button class="btn btn-success btn-sm btn-round has-ripple f-right">
												Submit
											</button>
										</th>			
									</tr>
									<tr>
										<th>Sr. No.</th>
										<th>Bill No.</th>
										<th>Client Name</th>
										<th>Bill Date</th>
										<th>Bill Amount</th>
										<th>
											<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"> Action
										</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>1</td>
										<td>TS/2019-2020/001</td>
										<td>PNEUCON PROCESS TECHNOLOGIES</td>
										<td>06-08-2019</td>
										<td>₹ 10620.00</td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">

											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt clr-red mr-10"></i>
											</a>
											<a href="viewcodeManageBillGST.php" target="_blank" class="btn btn-info btn-sm has-ripple">
												View <span class="ripple ripple-animate"></span>
											</a>
										</td>
									</tr>
									<tr>
										<td>2</td>
										<td>TS/2019-2020/034</td>
										<td>GUJARAT ELASTOMERS AND POLYMER TECHNOLOGIES LTD</td>
										<td>01-10-2019</td>
										<td>₹ 1770.00</td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">
											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt clr-red mr-10"></i>
											</a>
											<a href="viewcodeManageBillGST.php" target="_blank" class="btn btn-info btn-sm has-ripple">
												View <span class="ripple ripple-animate"></span>
											</a>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<!-- Table -->	
						<!--========== List View ==========-->
						
						<? } ?>
						
                    </div>
                </div>
            </div>
        </div>
		
		
		
	</div>
</div>