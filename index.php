<?php session_start();  
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
if(isset($_POST['btnRetrivePassword']))	
{
	$obj_admin->adminEmail=$_POST['Email_Address'];
	$rs=$obj_admin->forgotPassoword();
	if(count($rs)>0)
	{
		$username = $rs[0]['adminUsername'];
		$password = decryptPassword($rs[0]['adminPassword']); 
		$to=$_POST['Email_Address'];
		$headers  = "MIME-Version: 1.0\r\n";
		$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
		$headers .= "To:$to\r\n";
		$headers .= "From:ERP Demo<<EMAIL>>\r\n";
		$subject="Admin Forgot password";
		$message="Now you can login in ERP Demo ERP admin panel using following login details.";
		$message.="<br><br>Username : $username<br/>Password : $password";
		mail($to, $subject, $message, $headers);
		
		$to = "<EMAIL>";
		$headers  = "MIME-Version: 1.0\r\n";
		$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
		$headers .= "To:$to\r\n";
		$headers .= "From:ERP Demo<<EMAIL>>\r\n";
		$subject="Admin Forgot password";
		$message="Now you can login in ERP Demo ERP admin panel using following login details.";
		$message.="<br><br>Username : $username<br/>Password : $password";
		mail($to, $subject, $message, $headers);
	?>
		<script type="text/javascript">
			alert("The login details has been sent to <?=$_POST['Email_Address'];?>");
			document.location = "index.php?sent=true";
		</script>
<? }
	else
	{ ?>
		<script type="text/javascript">
			alert("Invalid Email address, this kind of address not found in the record.");
			document.location = "index.php?error=true"
		</script>
	<? }
} ?>
<!DOCTYPE html>
<html lang="en">
<head>

	<title>Welcome to ERP MEENA GOLD BUYER</title>
	
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	
	<!-- Favicon icon -->
	<link rel="icon" href="assets/images/logo/favicon.png" type="image/x-icon">

	<!-- vendor css -->
	<link rel="stylesheet" href="assets/css/style.css">
	
	<!-- custom css -->
    <link rel="stylesheet" href="assets/css/custom.css">
	
    <script type="text/javascript" src="js/ajax.js"></script>
<script type="text/javascript">
var ajax = new sack();

function whenLoading(){
	var e = document.getElementById('replaceme'); 
	e.innerHTML = '<b id="veryfying">Verifying email and password...</b>';
}

function whenLoaded(){
	var e = document.getElementById('replaceme'); 
	e.innerHTML = '<b id="veryfying">Verifying email and password...</b>';
}

function whenInteractive(){
	var e = document.getElementById('replaceme'); 
	e.innerHTML = '<b id="veryfying">Verifying email and password...</b>';
}

function whenCompleted(){
	//alert(ajax.response);
	var e = document.getElementById('replaceme'); 
	var return_value = ajax.response;	
	if(return_value.indexOf("Please wait,redirecting...")!=-1)
		document.location="dashboard.php";
	else
		e.innerHTML = ajax.response;	
}

function doit(){
	var form = document.getElementById('frmAdminLogin');
	if(form.txtUserName.value=="" || form.txtPassword.value==""){
		var e = document.getElementById('replaceme'); 
		e.innerHTML = "<div id='logout'><b>Email and Password required.</b></div>";
		return false	
	}else{
		ajax.setVar("txtUserName", form.txtUserName.value); 
		ajax.setVar("txtPassword", form.txtPassword.value);
		ajax.setVar("txtFinancialYear", form.txtFinancialYear.value);
		ajax.setVar("txtCompany", form.txtCompany.value);
		ajax.requestFile = "codeCheckLogin.php";
		ajax.method = "post";
		ajax.element = 'replaceme';
		ajax.onLoading = whenLoading;
		ajax.onLoaded = whenLoaded; 
		ajax.onInteractive = whenInteractive;
		ajax.onCompletion = whenCompleted;
		ajax.runAJAX();
	}
}
function MM_validateForm() { //v4.0
  if (document.getElementById){
    var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
    for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=document.getElementById(args[i]);
      if (val) { nm=val.name; if ((val=val.value)!="") {
        if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
          if (p<1 || p==(val.length-1)) errors+='- '+nm+' must contain an e-mail address.\n';
        } else if (test!='R') { num = parseFloat(val);
          if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
          if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
            min=test.substring(8,p); max=test.substring(p+1);
            if (num<min || max<num) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
      } } } else if (test.charAt(0) == 'R') errors += '- '+nm+' is required.\n'; }
    } if (errors) alert('The following error(s) occurred:\n'+errors);
    document.MM_returnValue = (errors == '');
} }
</script>
<script>
	function showPWD(){
		if(document.getElementById("txtPassword").type == "password"){
			document.getElementById("txtPassword").type="text";
			document.getElementById("hide_show_password").src='images/hide_password.png';
			document.getElementById("hide_show_password").title='Hide Password';
		}
		else{
			document.getElementById("txtPassword").type="password";
			document.getElementById("hide_show_password").src='images/show_password.png';
			document.getElementById("hide_show_password").title='Show Password';
		}
	}
</script>    
</head>
<body>	

	<!--========== Login Section ==========-->
	<div class="auth-wrapper">
		<div class="auth-content">
			<div class="card">
				<div class="row align-items-center">
					<div class="col-md-12">
						<div class="card-body">
							<div class="text-center">
								<img src="assets/images/logo/logo.png" alt="logo-login" class="img-fluid mb-1 text-center" width="150">
							</div>	
							<h4 class="mb-3 f-w-400 text-center">MEMBER'S LOGIN</h4>
							<form name="frmAdminLogin" method="POST" id="frmAdminLogin" action="codeCheckLogin.php" onSubmit="return sendForm(this);">
								<div class="form-group mb-2">
									<label>Email Id</label>
									<input type="text" class="form-control"  name="txtUserName" id="txtUserName" autocomplete="off">
								</div>
								<div class="form-group mb-3">
									<label>Password</label>
									<input type="password" class="form-control" name="txtPassword" id="txtPassword"  autocomplete="off" style="width:90%; float:left;">
                        <img src="images/show_password.png" width="25" style="cursor:pointer; float:left;" onClick="javascript:showPWD()" id="hide_show_password" title="Show Password" />
                        <div style="clear:both;"></div>
								</div>
                                <input type="hidden" name="txtCompany" id="txtCompany" value="ERP Demo" />
                                <input type="hidden" name="txtFinancialYear" id="txtFinancialYear" value="2025-2026" />
								<?php /* <div class="text-left mb-4 mt-2">
									<label class="floating-label" for="Password">Year</label>
									<? $yearList = $objYear->selectStatus(); ?>
                                        <select name="txtFinancialYear" id="txtFinancialYear" class="form-control">
                                        <? for($y=0;$y<count($yearList);$y++){ ?>
                                            <option value="<?=$yearList[$y]['year_name'];?>" <?=($yearList[$y]['current_year']==1) ? "selected" : "";?>><?=$yearList[$y]['year_name'];?></option>
                                        <? } ?>
                                        </select>
								</div> */ ?>
								<div style="clear:both;"></div>
                                <div style="text-align:center;" id="replaceme">
								<?	if(isset($_GET['logout']))
                                        echo "<div id='logout'>You are successfully logout</div>";	?>
                                </div>
                                <div style="clear:both;"></div>
								<input type="submit" value="Submit" name="btnPassword" id="btnPassword" style="cursor: pointer;"  class="btn btn-block btn-success mb-4"  onClick="doit(); return false;" onDblClick="doit(); return false;"  />
							</form>
							<p class="mb-2 text-muted">Forgot Login Details ? 
								<a href="reset-password.php" class="f-w-400">Reset</a>
							</p>
						
							<!--<p class="mb-0 text-muted">Don’t have an account? <a href="auth-signup.html" class="f-w-400">Signup</a></p>-->
						
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--========== Login Section ==========-->
	<!-- Required Js -->
	<script src="assets/js/vendor-all.min.js"></script>
	<script src="assets/js/plugins/bootstrap.min.js"></script>
	<script src="assets/js/ripple.js"></script>
	<script src="assets/js/pcoded.min.js"></script>
</body>
</html>