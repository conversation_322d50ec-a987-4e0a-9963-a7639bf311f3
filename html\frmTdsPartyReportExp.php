<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Export</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Partywise Deduction Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form name="frmsearch" id="frmsearch" action="" method="get" >
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
                                	<? $listParty=$objParty->selectStatusExp();?>
									<label>Search Party Name :</label>
									<select class="js-example-basic-single form-control col-sm-12">
										<? for($i=0;$i<count($listParty);$i++){ ?>
										<option value="<?=$listParty[$i]['id'];?>" <?=($_GET['Client_Name']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
										<? } ?>
									</select>
								</div>								
								<div class="form-group col-md-3">
									<label>From Date</label>
									<input type="text" name="From_Date" id="From_Date" size="10" value="<?=$_REQUEST['From_Date'];?>" class="form-control dd">
								</div>
								<div class="form-group col-md-3">
									<label>To Date</label>
									<input type="text" name="To_Date" id="To_Date" size="10" value="<?=$_REQUEST['To_Date'];?>" class="form-control dd">
								</div>
								<div class="form-group col-md-2">
									<label>&nbsp;</label><br>
									<button type="submit" name="btnSearch" id="btnSearch" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
									<button type="button" name="btnreset" id="btnreset" class="btn btn-info btn-sm has-ripple"  onclick="javascript:document.location='codeTdsPartyReport.php'">Reset</button>
                                </div>
								<div class="clearfix"></div>
							</div>
                            <script language="javascript">
							function printtds()
							{
								var pname = document.getElementById("Client_Name").value; 
								var fdate = document.getElementById("From_Date").value;
								var tdate = document.getElementById("To_Date").value; 
								
								window.open(
							'<?=BASE_URL;?>print_tds_partydetails.php?Party_Name='+pname+'&From_Date='+fdate+'&To_Date='+tdate,'_blank'
								  // http://erp.erpdemocompany123.com/demo 
								  // <- This is what makes it open in a new window.
								);
							}
							</script>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
							<table id="simpletable" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Party</th>
										<th>Date</th>
										<th>Amount</th>
									</tr>
								</thead>
								<tbody>
                                 <? 	$total_inr = 0; 
										if(count($listRec)>0)
										 {
											$colorflg=0;
											for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
										<td><?
												$objParty->id = $listRec[$e]['party'];
												$partynm = $objParty->selectRecById();
												echo $partynm[0]['party_name'];
											?></td>
										<td><?=$listRec[$e]['tdt'];?></td>
										<td><? $total+=$listRec[$e]['credit_amount'];
$total_inr+=($listRec[$e]['credit_amount']*$listRec[$e]['currency_inr']);
?>
<?=$objCrn->getCurrencySymbol($listRec[$e]['currency']);?>
<?=number_format($listRec[$e]['credit_amount'],2);?></td>
									</tr>
                                    <? }
									} ?>
								</tbody>
							</table>
						</div>
						<!-- Table -->		
						<!--========== List View ==========-->
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>