<? include("template.php");
function main() { 
	include("inc/clsObj.php");
	$obj_admin->id = $_SESSION['memberid'];
	$md=$obj_admin->selectRecById(); //======memeber details
	$up = explode(",",$md[0]['adminRights']); //=====user permissions
?>
<div class="pcoded-main-container">
    <div class="pcoded-content mt-0">		
        <div class="row">
            <? if(in_array($objAdminMenu->getIdByName("codeManageAdminUser.php"),$_SESSION['uper'])){ ?>
            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageAdminUser.php">
									<img src="assets/images/icon/m.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageAdminUser.php">Manage User</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeAdminChangePassword.php">
                                	<img src="assets/images/icon/changepassword1.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeAdminChangePassword.php">Change Password</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
			<?php /*?><div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageAdminMenu.php">
                                	<img src="assets/images/icon/adminmenu.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageAdminMenu.php">Admin Menu</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div><?php */?>
            <? if(in_array($objAdminMenu->getIdByName("codeManageParty.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageParty.php">
                                	<img src="assets/images/icon/assignedlead.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageParty.php">Customer Master</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } ?>
            <?  /* if(in_array($objAdminMenu->getIdByName("codeManageParty.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageItem.php">
                                	<img src="assets/images/icon/particularmaster.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageItem.php">Particular Master</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } 
			if(in_array($objAdminMenu->getIdByName("codeManageCompany.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageCompany.php">
                                	<img src="assets/images/icon/company.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageCompany.php">Company Master</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } 
			if(in_array($objAdminMenu->getIdByName("codeManageState.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageState.php">
                                	<img src="assets/images/icon/state.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageState.php">State Master</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageYear.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageYear.php">
                                	<img src="assets/images/icon/financialyear.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageYear.php">Financial Year Master</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageCurrency.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageCurrency.php">
                                	<img src="assets/images/icon/currency.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageCurrency.php">Currency Master</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageBillGST.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageBillGST.php">
                                	<img src="assets/images/icon/invoice.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageBillGST.php">Invoice</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageCashbook.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageCashbook.php">
                                	<img src="assets/images/icon/payment1.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageCashbook.php">Payment</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageTds.php"),$_SESSION['uper'])){ ?>            
            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageTds.php">
                                	<img src="assets/images/icon/tdsentry.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageTds.php">TDS Entry</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageBillExp.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageBillExp.php">
                                	<img src="assets/images/icon/exportinvoice.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageBillExp.php">Export Invoice</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageCashbookExp.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageCashbookExp.php">
                                	<img src="assets/images/icon/payment.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageCashbookExp.php">Export Payment</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } 
			if(in_array($objAdminMenu->getIdByName("codeManageDeduction.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageDeduction.php">
                                	<img src="assets/images/icon/taxdeduction.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageDeduction.php">Export Deduction</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } 
			if(in_array($objAdminMenu->getIdByName("codeManagePartyReportExp.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManagePartyReportExp.php">
                                	<img src="assets/images/icon/partywisereport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManagePartyReportExp.php">Partywise Ledger Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } 
			if(in_array($objAdminMenu->getIdByName("codePartyStatusReportExp.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codePartyStatusReportExp.php">
                                	<img src="assets/images/icon/pendingpat.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codePartyStatusReportExp.php">Pending Payment Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageCollectionExp.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageCollectionExp.php">
                                	<img src="assets/images/icon/collectionreport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageCollectionExp.php">Collection Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } 
			if(in_array($objAdminMenu->getIdByName("codeTdsReportExp.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeTdsReportExp.php">
                                	<img src="assets/images/icon/deduction.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeTdsReportExp.php">Deduction Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeTdsPartyReportExp.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeTdsPartyReportExp.php">
                                	<img src="assets/images/icon/partywisededuction.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeTdsPartyReportExp.php">Partywise Deduction Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codePartyFollowup.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codePartyFollowup.php">
                                	<img src="assets/images/icon/followup.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codePartyFollowup.php">Inquiries</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageCollection.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageCollection.php">
                                	<img src="assets/images/icon/collectionreport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageCollection.php">Collection Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codePartyStatusReport.php"),$_SESSION['uper'])){ ?>	
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codePartyStatusReport.php">
                                	<img src="assets/images/icon/pendingpat.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codePartyStatusReport.php">Pending Payment Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? } 
			if(in_array($objAdminMenu->getIdByName("codeInvoiceReport.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeInvoiceReport.php">
                                	<img src="assets/images/icon/invoice1.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeInvoiceReport.php">Invoice Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageClientPrintReport.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageClientPrintReport.php">
                                	<img src="assets/images/icon/envelope.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageClientPrintReport.php">Envelope Print</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageClientReport.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageClientReport.php">
                                	<img src="assets/images/icon/customerreport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageClientReport.php">Customer Detail Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManagePartyReport.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManagePartyReport.php">
                                	<img src="assets/images/icon/partywisereport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManagePartyReport.php">Partywise Ledger Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManagePartyReportAll.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManagePartyReportAll.php">
                                	<img src="assets/images/icon/historicalreport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManagePartyReportAll.php">Partywise Historic Ledger Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeManageCollectionTax.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeManageCollectionTax.php">
                                	<img src="assets/images/icon/taxpayment.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeManageCollectionTax.php">Tax Payment Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeTdsReport.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeTdsReport.php">
                                	<img src="assets/images/icon/tdsdeductionreport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeTdsReport.php">TDS Deduction Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("codeTdsPartyReport.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="codeTdsPartyReport.php">
                                	<img src="assets/images/icon/partywisetdsdeductionreport.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="codeTdsPartyReport.php">Partywise TDS Report</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <? }
			if(in_array($objAdminMenu->getIdByName("database_backup.php"),$_SESSION['uper'])){ ?>
			<div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 col-auto text-center">
								<a href="#">
                                	<img src="assets/images/icon/database.png" alt="">
								</a>	
                            </div>
                            <div class="col-md-12 col-auto text-center">
                                <h5 class="m-b-0"><a href="#">Database Backup</a></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
			<? } */ ?>
    	</div>		
	</div>
</div>
    

<? } ?>