<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Tax Payment Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form action="#">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-2">
									<label>Month</label>
									<select name="month" id="month" class="form-control">
										<option value="">--Select Month--</option>
                                        <option value="1" <?=($_GET['month']==1 || ($_GET['month']=="" && date("m")==1)) ? "selected" : "";?>>January</option>
                                        <option value="2" <?=($_GET['month']==2 || ($_GET['month']=="" && date("m")==2)) ? "selected" : "";?>>February</option>
                                        <option value="3" <?=($_GET['month']==3 || ($_GET['month']=="" && date("m")==3)) ? "selected" : "";?>>March</option>
                                        <option value="4" <?=($_GET['month']==4 || ($_GET['month']=="" && date("m")==4)) ? "selected" : "";?>>April</option>
                                        <option value="5" <?=($_GET['month']==5 || ($_GET['month']=="" && date("m")==5)) ? "selected" : "";?>>May</option>
                                        <option value="6" <?=($_GET['month']==6 || ($_GET['month']=="" && date("m")==6)) ? "selected" : "";?>>June</option>
                                        <option value="7" <?=($_GET['month']==7 || ($_GET['month']=="" && date("m")==7)) ? "selected" : "";?>>July</option>
                                        <option value="8" <?=($_GET['month']==8 || ($_GET['month']=="" && date("m")==8)) ? "selected" : "";?>>August</option>
                                        <option value="9" <?=($_GET['month']==9 || ($_GET['month']=="" && date("m")==9)) ? "selected" : "";?>>September</option>
                                        <option value="10" <?=($_GET['month']==10 || ($_GET['month']=="" && date("m")==10)) ? "selected" : "";?>>October</option>
                                        <option value="11" <?=($_GET['month']==11 || ($_GET['month']=="" && date("m")==11)) ? "selected" : "";?>>November</option>
                                        <option value="12" <?=($_GET['month']==12 || ($_GET['month']=="" && date("m")==12)) ? "selected" : "";?>>December</option>
									</select>
								</div>
								<div class="col-sm-12 col-md-3">
									<label>From Date</label>
									<input type="text" class="form-control dd" name="From_Date" id="From_Date" size="10" value="<?=$_REQUEST['From_Date'];?>" >
								</div>
								<div class="col-sm-12 col-md-3">
									<label>To Date</label>
									<input type="text" class="form-control dd" name="To_Date" id="To_Date" size="10" value="<?=$_REQUEST['To_Date'];?>">
								</div>
                                <script language="javascript">
								function printparty()
								{
								
								/*	var pname = document.getElementById("Party_Name").value; */
									var fdate = document.getElementById("From_Date").value;
									var tdate = document.getElementById("To_Date").value;
									var month =  document.getElementById("month").value;
									
									window.open(
								'<?=BASE_URL;?>print_collection_tax_details.php?month='+month+'&From_Date='+fdate+'&To_Date='+tdate,'_blank'
									  // http://erp.erpdemocompany123.com/demo 
									  // <- This is what makes it open in a new window.
									);
								}
								</script>
								<div class="form-group col-md-4">
									<label>&nbsp;</label><br>
									<button name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a onclick="javascript:printparty()" class="btn btn-info btn-sm has-ripple">Print</a><? } ?>
									<a onclick="javascript:document.location='codeManageCollectionTax.php'"  class="btn btn-info btn-sm has-ripple">Reset</a>
									<? if($_SESSION['act_prn']==1){ ?>
                                    <a id="download" class="btn btn-info btn-sm has-ripple">Export to Excel</a><? } ?>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
							<table id="tblCollectionTax" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Date</th>
										<th>Party</th>
										<th>Credit Amount</th>
										<th>Tax Amount</th>
									</tr>
								</thead>
								<tbody>
                                <?  
									if(count($listRec)>0)
									 {
										$colorflg=0;
										$total=0;
										$total_tax=0;
										for($e=0;$e<count($listRec);$e++){
											$objParty->id = $listRec[$e]['party'];
											$partynm = $objParty->selectRecById();
											if($partynm[0]['country']=="India"){ ?>
									<tr <?=($listRec[$e]['transaction_type']=="Cheque" && $listRec[$e]['cheque_cleared']==0) ? "style='background:#f7d092'" : "";?>>
										<td><?=$e+1;?></td>
                                <td><?=$listRec[$e]['tdt'];?></td>

                <td class="left-align">
                <? echo $partynm[0]['party_name']; ?>
                </td>
                <td class="right-align"><?=number_format($listRec[$e]['credit_amount']);?></td>
                <td class="right-align"><?
				
				if($listRec[$e]['tax_type']==""){	
					if($listRec[$e]['applicable_tax']==0)
						$at="12.36";
					else
						$at=$listRec[$e]['applicable_tax'];
					$taxable_amount =  ($listRec[$e]['credit_amount']*$at)/(100+$at);
					echo number_format($taxable_amount)." (@ ".$at."%)";
					$total_tax+=$taxable_amount;
				}
				elseif($listRec[$e]['tax_type']=="GST"){
					$total_gst_rate = $listRec[$e]['cgst_per'] + $listRec[$e]['sgst_per'];
				
					$taxable_amount =  (($listRec[$e]['credit_amount']*$total_gst_rate)/(100+$total_gst_rate));
					echo number_format($taxable_amount)." (@ CGST ".$listRec[$e]['cgst_per']."% + SGST ".$listRec[$e]['sgst_per']."%)";
					$total_tax+=$taxable_amount;
				
				}
				elseif($listRec[$e]['tax_type']=="IGST"){
					$taxable_amount =  (($listRec[$e]['credit_amount']*$listRec[$e]['igst_per'])/(100+$listRec[$e]['igst_per']));
					echo number_format($taxable_amount)." (@ IGST ".$listRec[$e]['igst_per']."%)";
					$total_tax+=$taxable_amount;
				
				}
				
					?></td>
                <?php /*?><td align="right"><?=number_format($listRec[$e]['debit_amount'],2);?></td><?php */?>
                <? $total+=$listRec[$e]['credit_amount'];?>
                </tr>
                                    <? } 
									} ?>
								</tbody>
								<tfoot>
									<tr>
										<td colspan="3" class="text-right"><strong>Total</strong></td>
										<td class="text-right"><strong>₹ <? echo number_format($total); ?></strong></td>
										<td class="text-right"><strong>₹ <? echo number_format($total_tax); ?></strong></td>
									</tr>
								</tfoot>
                                <? } ?>
							</table>
						</div>
						<!-- Table -->			
						<!--========== List View ==========-->
						
                    </div>
                </div>
            </div>
        </div>		
		
	</div>
</div>