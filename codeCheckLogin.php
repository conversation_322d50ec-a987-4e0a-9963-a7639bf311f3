<? include("inc/fileInclude.php");
include("inc/clsObj.php");
$days     = 1;
$m 	      = 20;
$C        = 6;

//$obj_admin->adminUsername=$_POST['txtUserName'];
$obj_admin->adminEmail=$_POST['txtUserName'];
$obj_admin->adminPassword=encryptPassword($_POST['txtPassword']);
$obj_admin->company=$_POST['txtCompany'];
$adminRec=$obj_admin->loginCheckCompany();
$ip  = $_SERVER['REMOTE_ADDR']; 
$errors = "";
if(empty($txtUsername))
{
	$errors="Username is required";
}
if(empty($txtPassword))
{
	$errors.="\nPassword is required";
}
if(count($adminRec)==0)
{
	echo "<font color='#990000'><b>Invalid Email Id or Password</b></font>";
}
else
{
	$_SESSION['accessby']="admin";
	$_SESSION['memberid']=$adminRec[0]['id'];
	$_SESSION['membername']=$adminRec[0]['adminName'];
	$_SESSION['memberrole']=$adminRec[0]['adminRole'];
	$_SESSION['uid']=$adminRec[0]['id'];
//	$_SESSION['uper']=explode(",",trim($adminRec[0]['adminRights'])); //=========user rights
	$_SESSION['fyear']=$_POST['txtFinancialYear'];
	$_SESSION['company']=$_POST['txtCompany'];

	echo "Please wait,redirecting...";
}
?>