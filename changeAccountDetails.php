<?php 
	error_reporting(0);
	session_start();
	include("inc/fileInclude.php");
	include("inc/constant.php");
	include("inc/clsObj.php");
	checkLogin();

	$obj_admin->id = $_SESSION['uid'];
	//$obj_admin->company = $_GET['company'];
	$obj_admin->company = $_SESSION['company'];
	$adminRec=$obj_admin->loginCheckCompanyById();

	if(count($adminRec)>0){
		$_SESSION['memberid']=$adminRec[0]['id'];
		$_SESSION['membername']=$adminRec[0]['adminUsername'];
		$_SESSION['uid']=$adminRec[0]['id'];
		$_SESSION['fyear']=$_GET['fyear'];
		//$_SESSION['company']=$_GET['company'];
		$_SESSION['company']=$_SESSION['company']; ?>
        <script>document.location="dashboard.php";</script>
	<? }
	else{ ?>
		<script>
			alert("You have not access for this company.");
			document.location="dashboard.php";
        </script>
	<? } ?>
