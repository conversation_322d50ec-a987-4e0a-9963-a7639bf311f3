<?php
global $db;
class chitthiMaster
{
   /* Variable Declaration */		
	var $tablename='chitthibook';
	var $id;
	var $party;
	var $chitthi_date;
	var $remarks;
	var $extra_amount;
	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function chitthiMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
				'$this->party',
				'$this->chitthi_date',
				'$this->remarks',
				'$this->extra_amount',
				'$this->status')";
		// echo $sql; die();
		$this->db->insert($sql);
		$id=mysql_insert_id();
		return($id);
	} 			

	function insert_items() 
	{
		$sql = "insert into `chitthibook_details` values ('',
				'$this->cid',
				'$this->company',
				'$this->item_name',
				'$this->lrno',
				'$this->aqty',
				'$this->sqty',
				'$this->weight',
				'$this->yp')";
		// echo $sql; die();
		$this->db->insert($sql);
		$id=mysql_insert_id();
		return($id);
	} 			

	/* update the record in database*/		
	function update()
	{
		$sql = "update `$this->tablename` set
						`party`='$this->party',
						`chitthi_date`='$this->chitthi_date',
						`remarks`='$this->remarks',
						`extra_amount`='$this->extra_amount'
						 where `id`=$this->id";
		// echo $sql;die();
		$this->db->edit($sql);		
		return true;
	}	

	/* update the record in database*/		
	function update_amount()
	{
		$sql = "update `$this->tablename` set
						`net_amount`='$this->net_amount'											 where `id`=$this->bid";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}	
	
	/* update the record in database*/		
	function update_items()
	{
		$sql = "update `$this->tablename` set
						`bid`='$this->bid',
						`chitthi_no`='$this->chitthi_no',
						`no_of_bags`='$this->no_of_bags',
						`wt_per_bag`='$this->wt_per_bag',
						`ord_no`='$this->ord_no',
						`ord_date`='$this->ord_date',
						`item`='$this->item',
						`weight`='$this->weight',
						`rate_per_qtl`='$this->rate_per_qtl',
						`amount`='$this->amount'
						 where `id`=$this->id";
		//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}	

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select *, date_format(`chitthi_date`, '%d-%m-%Y') as bdt from `$this->tablename` 
			   where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectByParty()
	{
		$sql ="select * from `$this->tablename` where party='$this->party'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function sumExtraAmount()
	{
		$sql ="select Sum(`extra_amount`) as extra_amount from `$this->tablename` where party='".$this->party."' group by party";
		// echo $sql;	die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectByBillNo()
	{
		$sql ="select *, date_format(`chitthi_date`, '%d-%m-%Y') as bdt from `$this->tablename` where chitthi_no='$this->chitthi_no' and id!='$this->bid'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/	
	function selectItemsByChitthiNo()
	{
		$sql ="select * from `chitthibook_details` where cid='$this->cid'";
		// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";//echo $sql;die();
		mysql_query($sql);	  
	}		

	/*delete a record from database*/	
	function delete_item()
	{
		$sql="delete from `chitthi_items` 
			  where `id`=$this->id";
	  //echo $sql;die();
		mysql_query($sql);	  
	}	

	/*delete a record from database*/	
	function delete_items()
	{
		$sql="delete from `chitthibook_details` 
			  where `cid`=$this->id";
	    // echo $sql;die();
		mysql_query($sql);	  
	}
	
	/*delete a record from database*/	
	function deleteItemsById()
	{
		$sql="delete from `chitthibook_details` 
			  where `cid`=$this->id";
	  	// echo $sql; die();
		mysql_query($sql);	  
	}	

	/*delete a record from database*/	
	function deleteById()
	{
		$sql="delete from `$this->tablename`
			  where `id`=$this->id";
	  	// echo $sql; die();
		mysql_query($sql);	  
	}	

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
	{
		for($i=0;$i<count($chk);$i++)
				{
					$id = $chk[$i];
					$sql="delete from `$this->tablename` where `id` = '$id'";
					$res = mysql_query($sql);
				}
		return true;
	}	
					
	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select *, date_format(`chitthi_date`,'%d-%m-%Y') as bdt from `$this->tablename`";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 1000;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}

	function selectByCompanyItemBid()
	{
		$sql ="select * from chitthibook_details where company='$this->company' and item = '$this->item' and lrno='$this->lrno'";
		// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function noCashbookRecords()
	{
		$sql = "SELECT * FROM `$this->tablename` WHERE party not in(select party from cashbook) group by party";
		// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
}		
?>   