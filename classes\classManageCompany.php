<?php
global $db;
class companyMaster
{
   /* Variable Declaration*/		
	var $tablename='company_master';
	var $id;
	var $company_name;
	var $company_address;
	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function companyMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
					'$this->company_name',
					'$this->invoice_header',
					'$this->quotation_header',
					'$this->quotation_signature',
					'$this->gstin',
					'$this->pan_no',
					'$this->bank_details',
					'$this->terms_conditions_invoice',
					'$this->terms_conditions_quotation',
					'$this->status')";
					// echo $sql;die();							
		$id=$this->db->insert($sql);
		//$id=mysql_insert_id();
		return($id);
	} 			

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename` order by company_name";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/* update the record in database*/		
	function update()
	{
		$sql = "update `$this->tablename` set
						`company_name`='$this->company_name',
						`invoice_header`='$this->invoice_header',
						`quotation_header`='$this->quotation_header',
						`quotation_signature`='$this->quotation_signature',
						`gstin`='$this->gstin',
						`pan_no`='$this->pan_no',
						`bank_details`='$this->bank_details',
						`terms_conditions_invoice`='$this->terms_conditions_invoice',
						`terms_conditions_quotation`='$this->terms_conditions_quotation'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*Fectch record by id from Database*/		
	function getTermsByCompany($company)
	{
		$sql ="select id, terms_conditions_quotation from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['terms_conditions_quotation']);
	}
	
	function getInvoiceTermsByCompany($company)
	{
		$sql ="select id, terms_conditions_invoice from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['terms_conditions_invoice']);
	}
	
	/*Fectch record by id from Database*/		
	function getSignatureByCompany($company)
	{
		$sql ="select id, quotation_signature from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['quotation_signature']);
	}
	
	/*Fectch record by id from Database*/		
	function getQuotationHeaderByCompany($company)
	{
		$sql ="select id, quotation_header from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['quotation_header']);
	}
	
	/*Fectch record by id from Database*/		
	function getInvoiceHeaderByCompany($company)
	{
		$sql ="select id, invoice_header from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['invoice_header']);
	}
	
	function getGstinByCompany($company)
	{
		$sql ="select id, gstin from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['gstin']);
	}
	
	function getPannoByCompany($company)
	{
		$sql ="select id, pan_no from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['pan_no']);
	}
	
	function getBankdetByCompany($company)
	{
		$sql ="select id, bank_details from `$this->tablename` 
			   where company_name='$company'";
	   	// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['bank_details']);
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";//echo $sql;die();
		mysql_query($sql);	  
	}		

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
		{
			for($i=0;$i<count($chk);$i++)
					{
						$id = $chk[$i];
						$sql="delete from `$this->tablename` where `id` = '$id'";
						$res = mysql_query($sql);
					}
			return true;
		}	
					
	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` order by company_name";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   
}		
?>   