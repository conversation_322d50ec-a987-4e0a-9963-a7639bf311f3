<?php 
include("template.php");
function main()
{
	$heading="<span>Manage</span> Tds ";
	$pageName="codeTdsReport.php";
    include("inc/clsObj.php");	
	
	extract($_POST);
	$cdate = explode("-",$Transaction_Date);
	$transaction_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

	$objTds->id=isset($_GET['uid']) ? $_GET['uid'] : (isset($_GET['delete']) ? $_GET['delete'] : (isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objTds->company=$_SESSION['company'];
	$objTds->financial_year=$_SESSION['fyear'];
	$objTds->party=$Party_Name;
	/*if($Credit_Debit==0)
	{
		$objTds->credit_amount=$Amount;
		$objTds->debit_amount=0;
	}
	elseif($Credit_Debit==1)
	{
		$objTds->credit_amount=0;
		$objTds->debit_amount=$Amount;
	}*/
	$objTds->credit_amount=$Amount;
	$objTds->debit_amount=0;
	$objTds->cheque_cash=$Cash_Cheque;
	$objTds->cheque_no=$Cheque_No;
	$objTds->cheque_detail=$Cheque_Detail;
	$objTds->transaction_detail=$Transaction_Detail;
	$objTds->transaction_date=$transaction_date;
	
	if(isset($_POST['btnAdd']))
	{
		$objTds->insert();
		redirect("codeManageTds.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objTds->update();
		redirect("codeManageTds.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objTds->deleteSelect($chkAction);
				break;
			case 1:
				$objTds->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objTds->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManageTds.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objTds->deleteById();
		$objTds->deleteItemsById();
		redirect("codeManageTds.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objTds->status();
		redirect("codeManageTds.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objTds->selectRecById();

		$objTds->cid = $listEdit[0]['id'];
		$listItems=$objTds->selectRecById();	
	}
	elseif($_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "";
		/*if($_GET['Party_Name']!="")
			$query.=" and party='".$_GET['Party_Name']."'"; */
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
			$query.=" and `transaction_date` between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		
		//$objTds->party = $_GET['party'];
		$listRec=$objTds->selectRecBySearch($query);
    }
	//else
		//$listRec=$objTds->paging();
	include("html/frmTdsReport.php");
} 
?>
