<?php
include("template.php");
function main()
{
	$heading="Manage Bill ";
    include("inc/clsObj.php");

	$objCash->id=$_GET['id'];
	$listEdit=$objCash->selectRecById();

	$objCash->cid = $listEdit[0]['id'];
	$listItems=$objCash->selectRecById();
	//echo count($listItems);

function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }

?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px!important;
	font-family:Verdana, Geneva, sans-serif;
}
.tbl_invoice td{
	font-size:13px!important;
	padding:3px!important;
}

.tbl_invoice td ul, .tbl_invoice td ol{
	padding-left:10px!important;
}

.tbl_invoice td strong{
	font-size:13px!important;
}
</style>

<form action="codeManageBillExp.php" method="post" name="frmAddRec">
<h2 class="pagetitle"><span>Manange</span> Invoice</h2>
    <a href="codeManageCashbookExp.php">
    	<input name="btnBack" id="btnBack"  type="button" class="back button" style="float:right;" onMouseOver="this.className='back button_hover'" onMouseOut="this.className='back button'"  value="Back" /></a>
</form>

<div style="width:100%; margin-top:60px;" id="print_header">
<div style="width:600px; margin:0 auto;">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
<tr>
  <td>
<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;"  class="tbl_invoice">
  <tr class="toprow">
    <td align="center" valign="top">
    <strong style="font-size:20px;"><?=$_SESSION['company'];?></strong><br />
      304-A, Ashoka, Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
      Ahmedabad-380014<br />
      Ph: +91-79-40391397<br />
      <strong>E-Mail: </strong>
      <a href="mailto:<EMAIL>"><EMAIL></a>, 
      <strong>Web: </strong><a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a><br/>
      
      <strong>PAN No. : </strong><?=$objCompany->getPannoByCompany($listEdit[0]['company']);?>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>GSTIN : </strong><?=$objCompany->getGstinByCompany($listEdit[0]['company']);?>
      </td>  
</tr>
<tr><td style="text-align:center; font-size:25px; font-weight:bold; height:50px;" valign="middle">Payment Receipt</td></tr>
<tr><td>
    <table width="100%" style="margin-top:20px;">
    <tr>
    <td align="left"><strong>Receipt No. <?=$listEdit[0]['receipt_no'];?></strong></td>
    <td align="right"><strong>Date: - <?=$listEdit[0]['trn_dt'];?></strong></td>
    </tr>
    <tr>
    <td colspan="2" style="line-height:22px;">
	<? $objParty->id=$listEdit[0]['party'];
            $partydet=$objParty->selectRecById(); 
	
	if($listEdit[0]['credit_amount'] > 0)
	{
		$amount = $listEdit[0]['credit_amount'];
		$credit_debit = 0;
	}
	elseif($listEdit[0]['debit_amount'] > 0)
	{
		$amount = $listEdit[0]['debit_amount'];
		$credit_debit = 1;
	}
	
	if($listEdit[0]['cheque_cash']==0)
		$payment_msg = "Cash dated : ".$listEdit[0]['trn_dt'];
	elseif($listEdit[0]['cheque_cash']==1)
		$payment_msg = "Cheque No. : ".$listEdit[0]['cheque_no']." dated ".$listEdit[0]['chq_dt']; 
	elseif($listEdit[0]['cheque_cash']==2)
		$payment_msg = "NEFT dated : ".$listEdit[0]['trn_dt'];
?>		
    <p>&nbsp;</p>
    <p style="font-size:13px!important;">Received with thanks from <strong><u><?=$partydet[0]['party_name'];?></u></strong>� as sum of <?=$objCrn->getCurrencySymbol($listEdit[0]['currency']);?> <?=$amount;?> (<?=$objCrn->getCurrencyName($listEdit[0]['currency']);?> <?=ucwords(no_to_words($amount));?> Only) as an Payment for "<?=$listEdit[0]['transaction_detail'];?>" <?=($listEdit[0]['cheque_cash']==1) ? "through  ".$listEdit[0]['cheque_detail'] : "";?> vide <?=$payment_msg;?> favoring M/s. <?=$_SESSION['company'];?></p><br/>
    <p><strong><?=$objCrn->getCurrencySymbol($listEdit[0]['currency']);?> <?=$amount;?><br/>
    Amount in words : �<?=$objCrn->getCurrencyName($listEdit[0]['currency']);?> <?=ucwords(no_to_words($amount));?> Only
    </strong></p>
    </td>
    </tr>
    </table>
</td></tr>
  
  </table>
</td></tr>
<tr>
<td>
<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;"  class="tbl_invoice">
  <tr>
    <td width="226" valign="top" align="right";>
      <p align="right">For, <?=$_SESSION['company'];?></p>
      <br/><br/><br/><br/><br/>
      <p align="right">AUTHORISED    SIGNATORY</p></td>
  </tr>
</table>
</td>
</tr>
</table><br/><br/>
<a href="codeManageCashbook.php?id=<?=$_GET['id'];?>">
<input name="btnEdit" id="btnEdit"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Edit Invoice" style="margin:0 auto!important;" /></a>
&nbsp;&nbsp;&nbsp;
<a href="print_receipt_exp.php?id=<?=$_GET['id'];?>" target="_blank">
<input name="btnPrint" id="btnPrint"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Print Receipt" style="text-decoration:none;" /></a>
&nbsp;&nbsp;&nbsp;
<a href="print_receipt_letterhead_exp.php?id=<?=$_GET['id'];?>" target="_blank">
<input name="btnPrint" id="btnPrint"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Print Receipt Letterhead" style="text-decoration:none;" /></a>
<?php /*?>&nbsp;&nbsp;&nbsp;
<a href="codeManageCashbook.php?pid=<?=$_GET['id'];?>" target="_blank">
<input name="btnEmail" id="btnEmail"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Send Email to Client" style="text-decoration:none;" /></a><?php */?>

</div>

</div>
<? } ?>