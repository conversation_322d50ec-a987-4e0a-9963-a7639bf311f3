Calendar.LANG("pt", "Portuguese", {

        fdow: 1,                // primeiro dia da semana para esse local; 0 = Domingo, 1 = Segunda, etc.

        goToday: "Dia de Hoje",

        today: "Hoje",

        wk: "sm",

        weekend: "0,6",         // 0 = Domingo, 1 = Segunda, etc.

        AM: "am",

        PM: "pm",

        mn : [ "Janeiro",
               "Fevereiro",
               "Março",
               "Abril",
               "Maio",
               "Junho",
               "Julho",
               "Agosto",
               "Setembro",
               "Outubro",
               "Novembro",
               "Dezembro" ],

        smn : [ "<PERSON>",
                "Fev",
                "Mar",
                "Abr",
                "Mai",
                "Jun",
                "Jul",
                "Ago",
                "Set",
                "Out",
                "Nov",
                "Dez" ],

        dn : [ "Domingo",
               "Segunda",
               "Ter<PERSON>",
               "Quarta",
               "Quinta",
               "Sexta",
               "Sábado",
               "Domingo" ],

        sdn : [ "<PERSON>",
                "Se<PERSON>",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "<PERSON>",
                "<PERSON><PERSON>",
                "<PERSON>" ]

});
