<?php
global $db;
class currencyMaster
{
   /* Variable Declaration*/		
	var $tablename='currency_master';
	var $id;
	var $year_name;
	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function currencyMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
					'$this->currency_name',
					'$this->currency_inr',
					'$this->currency_symbol',
					'$this->status')";
		// echo $sql;die();							
		$id=$this->db->insert($sql);
		//$id=mysql_insert_id();
		return($id);
	} 			
	
	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/* Fetch all the records */	
	function selectStatus()
	{
		$sql ="select * from `$this->tablename` where status=1 order by currency_name";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select * from `$this->tablename` where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}


	/*Fectch record by id from Database*/		
	function selectByName($currency_name)
	{
		$sql ="select * from `$this->tablename` where currency_name='$currency_name'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function getCurrencyINR($currency)
	{
		$sql ="select * from `$this->tablename` 
			   where id='$currency'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['currency_inr']);
	}
	
	/*Fectch record by id from Database*/		
	function getCurrencySymbol($currency)
	{
		$sql ="select * from `$this->tablename` 
			   where id='$currency'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['currency_symbol']);
	}
	
	/*Fectch record by id from Database*/		
	function getCurrencyName($currency)
	{
		$sql ="select * from `$this->tablename` 
			   where id='$currency'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['currency_name']);
	}

	/* update the record in database*/		
	function update()
	{
		$sql = "update `$this->tablename` set
						`currency_name`='$this->currency_name',
						`currency_inr`='$this->currency_inr',
						`currency_symbol`='$this->currency_symbol'
						 where `id`=$this->id";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` where `id`=$this->id";
		$this->db->sql_query($sql);
	}		

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
		{
			for($i=0;$i<count($chk);$i++)
					{
						$id = $chk[$i];
						$sql="delete from `$this->tablename` where `id` = '$id'";
						$this->db->sql_query($sql);
					}
			return true;
		}	
					
	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` order by currency_name";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 100;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   
}		
?>   