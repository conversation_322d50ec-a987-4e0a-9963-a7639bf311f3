<?php
global $db;
class billMaster
{
   /* Variable Declaration*/		
	var $tablename='bill_master';
	var $id;
	var $invoice_no;
	var $invoice_type;
	var $party;
	var $bill_date;
	var $net_amount;
	var $discount;
	var $amount;
	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function billMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
				'$this->company',
				'$this->financial_year',
				'$this->po_no',
				'$this->invoice_no',
				'$this->invoice_type',
				'$this->bill_type',
				'$this->party',
				'$this->bill_date',
				'$this->renewal_date',
				'$this->invoice_terms',
				'$this->currency',
				'$this->currency_inr',
				'$this->net_amount',
				'$this->discount',
				'$this->cgst_rate',
				'$this->cgst_amt',
				'$this->sgst_rate',
				'$this->sgst_amt',
				'$this->igst_rate',
				'$this->igst_amt',
				'$this->amount',
				'$this->status')";
		// echo $sql."<br/>"; die();
		$id=$this->db->insert($sql);
		//$id=mysql_insert_id();
		return($id);
	} 			

	function insert_items() 
	{
		$sql = "insert into `bill_items` values ('',
				'$this->bid',
				'$this->description',
				'$this->no_of_items',
				'$this->item',
				'$this->rate_per_qty',
				'$this->amount',
				'$this->discount',
				'$this->taxable_amount',
				'$this->cgst',
				'$this->sgst',
				'$this->igst',
				'$this->total_amount')";
		// echo $sql."<br/>"; // die();
		$id=$this->db->insert($sql);
		//$id=mysql_insert_id();
		return($id);
	} 			

	/* update the record in database*/		
	function update()
	{
		$sql = "update `$this->tablename` set
						`po_no`='$this->po_no',
						`invoice_no`='$this->invoice_no',
						`invoice_type`='$this->invoice_type',
						`bill_type`='$this->bill_type',
						`party`='$this->party',
						`bill_date`='$this->bill_date',
						`renewal_date`='$this->renewal_date',
						`invoice_terms`='$this->invoice_terms',
						`currency`='$this->currency',
						`currency_inr`='$this->currency_inr',
						`net_amount`='$this->net_amount',
						`discount`='$this->discount',
						`cgst_rate`='$this->cgst_rate',
						`cgst_amt`='$this->cgst_amt',
						`sgst_rate`='$this->sgst_rate',
						`sgst_amt`='$this->sgst_amt',
						`igst_rate`='$this->igst_rate',
						`igst_amt`='$this->igst_amt',
						`amount`='$this->amount'
						 where `id`=$this->id";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}	

	/* update the record in database*/		
	function update_amount()
	{
		$sql = "update `$this->tablename` set
						`net_amount`='$this->net_amount',
						`amount`='$this->amount',
						`cgst_amt`='$this->cgst_amt',
						`sgst_amt`='$this->sgst_amt',
						`igst_amt`='$this->igst_amt',
						`stax_amt`='$this->stax_amt',
						`sbtax_amt`='$this->sbtax_amt',
						`edu_amt`='$this->edu_amt',
						`hsedu_amt`='$this->hsedu_amt'
						where `id`=$this->bid";
		//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}	
	/* update the record in database*/		
	function update_items()
	{
		$sql = "update `$this->tablename` set
						`bid`='$this->bid',
						`bill_no`='$this->bill_no',
						`no_of_bags`='$this->no_of_bags',
						`available_qty`='$this->available_qty',
						`wt_per_bag`='$this->wt_per_bag',
						`ord_no`='$this->ord_no',
						`ord_date`='$this->ord_date',
						`item`='$this->item',
						`weight`='$this->weight',
						`rate_per_qtl`='$this->rate_per_qtl',
						`amount`='$this->amount'
						 where `id`=$this->id";
		//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}	

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}
	
	/* Fetch all the records */	
	function selectByParty($party)
	{
		$sql ="select * from `$this->tablename` where party = $party";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	
	
	/* Fetch all the records */	
	function selectAmount()
	{
		$sql = "SELECT SUM(amount) FROM bill_master where financial_year = '".$_SESSION['fyear']."' and company = '".$_SESSION['company']."' and bill_type='' ";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}
	
	function selectAmountGST()
	{
		$sql = "SELECT SUM(amount) FROM bill_master where financial_year = '".$_SESSION['fyear']."' and company = '".$_SESSION['company']."' and bill_type='GST' ";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}
	
	function selectAmountSgst()
	{
		$sql = "SELECT SUM(amount) FROM bill_master where financial_year = '".$_SESSION['fyear']."' and company = '".$_SESSION['company']."' and bill_type='sgst' ";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}
	
	function selectAmountIgst()
	{
		$sql = "SELECT SUM(amount) FROM bill_master where financial_year = '".$_SESSION['fyear']."' and company = '".$_SESSION['company']."' and bill_type='igst' ";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}
	
	/* Fetch all the records */	
	function selectAmountExp()
	{
		$sql = "SELECT SUM(amount * currency_inr) as total_inr FROM bill_master where financial_year = '".$_SESSION['fyear']."' and company = '".$_SESSION['company']."' and bill_type='Exp'";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	
	
	
	/*Fectch record by id from Database*/		
	function selectRecByCompanyYear()
	{
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt, date_format(`renewal_date`, '%d-%m-%Y') as rdt from `$this->tablename` where company ='$this->company' and financial_year='$this->fyear'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt, date_format(`renewal_date`, '%d-%m-%Y') as rdt from `$this->tablename` where id='$this->id'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	

	/*Fectch record by id from Database*/		
	function selectItemsByRecords()
	{
		// $sql ="select *, date_format(`ord_date`, '%d-%m-%Y') as odt from `bill_items` where bid='$this->bid'";
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt, date_format(`renewal_date`, '%d-%m-%Y') as rdt from `$this->tablename` where financial_year = '".$_SESSION['fyear']."' and company = '".$_SESSION['company']."' order by id";
//		echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectItemsByQuery($query)
	{
		// $sql ="select *, date_format(`ord_date`, '%d-%m-%Y') as odt from `bill_items` where bid='$this->bid'";
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt, date_format(`renewal_date`, '%d-%m-%Y') as rdt from `$this->tablename` where company = '".$_SESSION['company']."' ".$query." order by id";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectCurrencyByParty()
	{
		$sql ="select * from `$this->tablename` where party = '$this->party' group by currency";
//		echo $sql."<br/>"; //die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectRecByPartyDate()
	{
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt from `$this->tablename` where party = '$this->party' and company = '".$_SESSION['company']."' and `bill_date` like '%$this->tdt%'";
//		echo $sql."<br/>"; //die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	function selectDateByParty()
	{
		$sql = "SELECT date_format(`bill_date`, '%d-%m-%Y') as tdate FROM `bill_master` where party = '$this->party' and company = '".$_SESSION['company']."' and financial_year = '".$_SESSION['fyear']."'";
		// echo $sql; die;
 	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectSearchDateByParty($query)
	{
		$sql = "SELECT date_format(`bill_date`, '%d-%m-%Y') as tdate FROM `bill_master` where party = '$this->party' and company = '".$_SESSION['company']."' ".$query;
		// echo $sql; die;
 	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectSumByParty()
	{
		$sql ="select *, SUM(amount) as totamt, SUM(amount * currency_inr) as tot_inr from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectSumByPartyExp()
	{
		//$sql ="select SUM(amount * currency_inr) as totamt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		$sql ="select SUM(amount) as totamt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectSumByPartyAll()
	{
		$sql ="select SUM(amount) as totamt from `$this->tablename` where party='$this->party'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectSumByPartyBeforeDate()
	{
		$sql ="select *, SUM(amount) as totamt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and bill_date < '$this->bdate'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	


	/*Fectch record by max bill type id from Database*/		
	function selectRecByBillType()
	{
		$sql ="select * from `$this->tablename` where `invoice_type`='$this->invoice_type'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectByBillNo()
	{
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt from `$this->tablename` where bill_no='$this->bill_no' and id!='$this->bid'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectItemsByBillNo()
	{
		// $sql ="select *, date_format(`ord_date`, '%d-%m-%Y') as odt from `bill_items` where bid='$this->bid'";
		$sql ="select * from `bill_items` where bid='$this->bid'";
		 // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectRecBySearch($query)
	{
		$sql ="select bill_master.*, date_format(`bill_master.bill_date`,'%d-%m-%Y') as bdt, party_master.* from `$this->tablename` bm left join party_master pm on bm.party=pm.id where bm.party!=''".$query." order by pm.party_name";
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

/*Fectch record by id from Database*/		
	function selectServiceTax($query)
	{
		// $sql ="select *, date_format(`ord_date`, '%d-%m-%Y') as odt from `bill_items` where bid='$this->bid'";
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt, date_format(`renewal_date`, '%d-%m-%Y') as rdt from `$this->tablename` where company = '".$_SESSION['company']."' ".$query." order by id";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	
	function selectInvoiceByQuery($query)
	{
		// $sql ="select *, date_format(`ord_date`, '%d-%m-%Y') as odt from `bill_items` where bid='$this->bid'";
		$sql ="select *, date_format(`bill_date`, '%d-%m-%Y') as bdt, date_format(`renewal_date`, '%d-%m-%Y') as rdt from `$this->tablename` where company = '".$_SESSION['company']."' ".$query." order by id";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}


	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";//echo $sql;die();
		$this->db->sql_query($sql);  
	}		

	/*delete a record from database*/	
	function delete_item()
	{
		$sql="delete from `bill_items` 
			  where `id`='$this->id'";
	    $this->db->sql_query($sql);
	}	

	/*delete a record from database*/	
	function delete_items()
	{
		$sql="delete from `bill_items` 
			  where `bid`='$this->id'";
	 	$this->db->sql_query($sql);
	}
	
	/*delete a record from database*/	
	function deleteItemsById()
	{
		$sql="delete from `bill_items` 
			  where `bid`=$this->id";
	  	// echo $sql;die();
		$this->db->sql_query($sql);
	}	

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";
							//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql="delete from `$this->tablename` where `id` = '$id'";
			$this->db->sql_query($sql);
		}
		return true;
	}	
	
	function selectAll($query)
	{			
		$sql = "select bm.id as bid, bm.financial_year as fyear, bm.company as company, date_format(bm.bill_date,'%d-%m-%Y') as bdt, bm.invoice_no as bmin, bm.invoice_type as bit, bm.party as bmp, bm.amount as bmamt, bm.status as status, pm.id as pid, pm.party_name as pmpn from `$this->tablename` bm left join party_master pm on bm.party=pm.id where bm.party!=''".$query." and bm.company='".$_SESSION['company']."' and bm.bill_type != 'Exp' order by bm.invoice_no";
		$result=$this->db->select($sql);
		return($result);
	} 
	
	function getTotalAll($query)
	{			
		$sql = "select sum(amount) as totalAmt, bm.id as bid, bm.financial_year as fyear, bm.company as company, date_format(bm.bill_date,'%d-%m-%Y') as bdt, bm.invoice_no as bmin, bm.invoice_type as bit, bm.party as bmp, bm.amount as bmamt, bm.status as status, pm.id as pid, pm.party_name as pmpn from `$this->tablename` bm left join party_master pm on bm.party=pm.id where bm.party!=''".$query." and bm.company='".$_SESSION['company']."' and bm.bill_type != 'Exp' order by bm.invoice_no";
		$result=$this->db->select($sql);
		return($result[0]['totalAmt']);
	} 
	
					
	/*...paging...*/
	function paging($query)
	{			
		$pages = new Paging();
//		$pages->sql ="select *, date_format(`bill_date`,'%d-%m-%Y') as bdt from `$this->tablename` order by invoice_no";
		$pages->sql = "select bm.id as bid, bm.financial_year as fyear, bm.company as company, date_format(bm.bill_date,'%d-%m-%Y') as bdt, bm.invoice_no as bmin, bm.invoice_type as bit, bm.party as bmp, bm.amount as bmamt, bm.status as status, pm.id as pid, pm.party_name as pmpn from `$this->tablename` bm left join party_master pm on bm.party=pm.id where bm.party!=''".$query." and bm.company='".$_SESSION['company']."' and bm.bill_type != 'Exp' order by bm.invoice_no";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->parameters = $this->parameters;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	} 
	
	/*...paging...*/
	function pagingExp($query)
	{			
		$pages = new Paging();
//		$pages->sql ="select *, date_format(`bill_date`,'%d-%m-%Y') as bdt from `$this->tablename` order by invoice_no";
		$pages->sql = "select bm.id as bid, bm.bill_type as btype, bm.financial_year as fyear, bm.company as company, date_format(bm.bill_date,'%d-%m-%Y') as bdt, bm.invoice_no as bmin, bm.invoice_type as bit, bm.party as bmp, bm.currency as bcrn, bm.currency_inr as crn_inr, bm.amount as bmamt, pm.id as pid, pm.party_name as pmpn from `$this->tablename` bm left join party_master pm on bm.party=pm.id where bm.party!=''".$query." and bm.company='".$_SESSION['company']."' and bm.bill_type = 'Exp' order by bm.invoice_no";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->parameters = $this->parameters;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   

	/*Fectch record by id from Database*/		
	function selectByCompanyItem()
	{
		$sql ="select bm.id as bid, bm.bill_no as bno, bm.company as bcom, bi.item as item, bi.available_qty as aqty from `$this->tablename` bm left join bill_items bi on bm.id=bi.bid where bm.company='$this->company' and bi.item = '$this->item' order by bi.ord_date asc";
//		echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectDistLrByCompanyItem()
	{
		$sql ="select DISTINCT bi.bill_no as bno, bi.bid as bid from bill_items bi left join `$this->tablename` bm on bm.id=bi.bid where bm.company='$this->company' and bi.item = '$this->item' order by bi.ord_date asc";
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	function selectByCompanyItemBid()
	{
		$sql ="select bm.id as bid, bm.bill_no as bno, bm.company as bcom, bi.item as item, bi.available_qty as stock_qty, bi.wt_per_bag as wpb from `$this->tablename` bm left join bill_items bi on bm.id=bi.bid where bm.company='$this->company' and bi.item = '$this->item' and bi.bid='$this->bid'";
		// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
}		
?>   