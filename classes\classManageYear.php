<?php
global $db;
class yearMaster
{
   /* Variable Declaration*/		
	var $tablename='year_master';
	var $id;
	var $year_name;
	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function yearMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
					'$this->year_name',
					'$this->current_year',
					'$this->status')";
		// echo $sql;die();							
		$id=$this->db->insert($sql);
		//$id=mysql_insert_id();
		return($id);
	} 			


//==================== Increment Table ==============//
	/*Insert Record into increment master*/		
	function insertIncrement() 
	{
		$sql = "insert into `increment_master` values (
					'$this->year_name',
					'$this->company_name',
					'$this->last_bill_id',
					'$this->last_bill_exp_id',
					'$this->last_cashbook_id',
					'$this->last_quote_id')";
		// echo $sql;die();							
		$this->db->insert($sql);
		
		//$id=mysql_insert_id();
		//return($id);
	} 			

	/*Fectch record by id from Database*/		
	function selectAllYears()
	{
		$sql ="select * from `$this->tablename` order by  year_name";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectBidByCompanyYear()
	{
		$sql ="select * from `increment_master` 
			   where company_name='$this->company' and financial_year='$this->fyear'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectCidByCompanyYear()
	{
		$sql ="select * from `increment_master` 
			   where company_name='$this->company' and financial_year='$this->fyear'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectByYear()
	{
		$sql ="select * from `$this->tablename` 
			   where year_name='$this->year_name'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	
	/* update the record in database*/		
	function updateCompanyBillNo()
	{
		$sql = "update `increment_master` set
						`last_bill_id`='$this->lbno' 
						where company_name='$this->company' and
						`financial_year`='$this->financial_year'";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}
	
	function updateCompanyQuoteNo()
	{
		$sql = "update `increment_master` set
						`last_quote_id`='$this->lqno' 
						where company_name='$this->company' and
						`financial_year`='$this->financial_year'";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}
	
	/* update the record in database*/		
	function updateCompanyBillNoExp()
	{
		$sql = "update `increment_master` set
						`last_bill_exp_id`='$this->lbno' 
						where company_name='$this->company' and
						`financial_year`='$this->financial_year'";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}
	
	/* update the record in database*/		
	function updateCompanyCashbookNo()
	{
		$sql = "update `increment_master` set
						`last_cashbook_id`='$this->lcno' 
						where company_name='$this->company' and
						`financial_year`='$this->fyear'";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}
	
	/* update the record in database*/		
	function updateCompanyCashbookNoExp()
	{
		$sql = "update `increment_master` set
						`last_cashbook_id_exp`='$this->lcno' 
						where company_name='$this->company' and
						`financial_year`='$this->financial_year'";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}	
//==================== End of Increment Table ==============//

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/* Fetch all the records */	
	function selectStatus()
	{
		$sql ="select * from `$this->tablename` where status=1 order by year_name";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectRecByCompany()
	{
		$sql ="select * from `$this->tablename` 
			   where company='$this->company'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/* update the record in database*/		
	function update()
	{
		$sql = "update `$this->tablename` set
						`year_name`='$this->year_name',
						`current_year`='$this->current_year'
						 where `id`=$this->id";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";//echo $sql;die();
		mysql_query($sql);	  
	}		

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
		{
			for($i=0;$i<count($chk);$i++)
					{
						$id = $chk[$i];
						$sql="delete from `$this->tablename` where `id` = '$id'";
						$res = mysql_query($sql);
					}
			return true;
		}	
					
	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` order by year_name";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 100;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   
}		
?>   