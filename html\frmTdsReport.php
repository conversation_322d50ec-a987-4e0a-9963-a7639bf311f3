<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage TDS Deduction Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
<script language="javascript">
function printtds()
{
	var fdate = document.getElementById("From_Date").value;
	var tdate = document.getElementById("To_Date").value; 
	window.open('<?=BASE_URL;?>print_tds_details.php?From_Date='+fdate+'&To_Date='+tdate,'_blank');
}
</script>
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form name="frmsearch" id="frmsearch" action="" method="get" >
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-3">
									<label>From Date</label>
									<input type="text" name="From_Date" id="From_Date" size="10" value="<?=$_REQUEST['From_Date'];?>" class="form-control dd">
								</div>
								<div class="col-sm-12 col-md-3">
									<label>To Date</label>
									<input type="text" name="To_Date" id="To_Date" size="10" value="<?=$_REQUEST['To_Date'];?>" class="form-control dd">
								</div>
								<div class="form-group col-md-4">
									<label>&nbsp;</label><br>
									<button name="btnSearch" id="btnSearch"  value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a onclick="javascript:printtds()" class="btn btn-info btn-sm has-ripple">Print</a>
                                    <? } ?>
									<a onclick="javascript:document.location='codeTdsReport.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a  id="download" class="btn btn-info btn-sm has-ripple">Export to Excel</a><? } ?>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
							<table id="tblTdsReport" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Party</th>
										<th>Date</th>
										<th>Amount</th>
									</tr>
								</thead>
								<tbody>
									
                                    <? if(count($listRec)>0)
										 {
											for($e=0;$e<count($listRec);$e++){ ?>
                                      <tr>
										<td><?=$e+1;?></td>
										<td><?
												$objParty->id = $listRec[$e]['party'];
												$partynm = $objParty->selectRecById();
												echo $partynm[0]['party_name'];
											?></td>
										<td><?=$listRec[$e]['tdt'];?></td>
										<td class="text-right">
<? $total+=$listRec[$e]['credit_amount'];?>
<?=number_format($listRec[$e]['credit_amount'],2);?></td>
									</tr>
									<? } ?>
								</tbody>
								<tfoot>
									<tr>
										<td colspan="3" class="text-right"><strong>Total Amount</strong></td>
										<td class="text-right"><strong>₹ <?=number_format($total,2);?></strong></td>
									</tr>
								</tfoot>
                                <? } ?>
							</table>
						</div>
						<!-- Table -->		
						<!--========== List View ==========-->
                    </div>
                </div>
            </div>
        </div>		
	</div>
</div>