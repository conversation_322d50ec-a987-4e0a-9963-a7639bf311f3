#!/usr/bin/php
<?	session_start();
error_reporting(0);
$_SESSION['filename']="";
//backup_tables('localhost','root','','erpamtec');
backup_tables('localhost','erpamtec_dbu','BOETWnB8z3hK','erpamtec_db');

/* backup the db OR just a table */
function backup_tables($host,$user,$pass,$name,$tables = '*')
{
  
  $link = mysql_connect($host,$user,$pass);
  mysql_select_db($name,$link);
  
  //get all of the tables
  if($tables == '*')
  {
    $tables = array();
    $result = mysql_query('SHOW TABLES');
    while($row = mysql_fetch_row($result))
    {
      $tables[] = $row[0];
    }
  }
  else
  {
    $tables = is_array($tables) ? $tables : explode(',',$tables);
  }
  
  //cycle through
  foreach($tables as $table)
  {
    $result = mysql_query('SELECT * FROM '.$table);
    $num_fields = mysql_num_fields($result);
    
    $return.= 'DROP TABLE '.$table.';';
    $row2 = mysql_fetch_row(mysql_query('SHOW CREATE TABLE '.$table));
    $return.= "\n\n".$row2[1].";\n\n";
    
    for ($i = 0; $i < $num_fields; $i++) 
    {
      while($row = mysql_fetch_row($result))
      {
        $return.= 'INSERT INTO '.$table.' VALUES(';
        for($j=0; $j<$num_fields; $j++) 
        {
          $row[$j] = addslashes($row[$j]);
          $row[$j] = ereg_replace("\n","\\n",$row[$j]);
          if (isset($row[$j])) { $return.= '"'.$row[$j].'"' ; } else { $return.= '""'; }
          if ($j<($num_fields-1)) { $return.= ','; }
        }
        $return.= ");\n";
      }
    }
    $return.="\n\n\n";
  }
  
  //save file
  $filename = 'erp-amtechno-db-'.date("d-m-Y").'.sql';
  $handle = fopen($filename,'w+');
  fwrite($handle,$return);
  fclose($handle);
//================add with zipfile============  
	$file=$filename;
	$zipname = "ERP-AMTechno-DB-".date("d-m-Y").'.zip';
	$zip = new ZipArchive;
	$zip->open($zipname, ZipArchive::CREATE);
	//foreach ($files as $file) {
	  $zip->addFile($file);
	//}
	$zip->close();
	/* header('Content-Type: application/zip');
	header('Content-disposition: attachment; filename='.$zipname);
	header('Content-Length: ' . filesize($zipname));
	readfile($zipname);
	unlink($zipname); */
//===============End of zip file==========
unlink($filename); //==== remove file after making zip file
$_SESSION['filename']=$zipname;

//===============send email===============
$from_email         = '<EMAIL>'; //from mail, it is mandatory with some hosts
$recipient_email    = '<EMAIL>'; //recipient email (most cases it is your personal email)
$sender_name    = "ERP Demo"; //sender name
$reply_to_email = ""; //sender email used in "reply-to" header
$subject        = "Database Backup"; //get subject from HTML form
$message        = "PFA - Database Backup Today"; //message

$handle = fopen($_SESSION['filename'], "r");
$content = fread($handle, filesize($_SESSION['filename']));
fclose($handle);
$encoded_content = chunk_split(base64_encode($content));

$boundary = md5("sanwebe");
//header
$headers = "MIME-Version: 1.0\r\n";
$headers .= "From:".$from_email."\r\n";
$headers .= "Reply-To: ".$reply_to_email."" . "\r\n";
$headers .= "Content-Type: multipart/mixed; boundary = $boundary\r\n\r\n";

//plain text
$body = "--$boundary\r\n";
$body .= "Content-Type: text/plain; charset=ISO-8859-1\r\n";
$body .= "Content-Transfer-Encoding: base64\r\n\r\n";
$body .= chunk_split(base64_encode($message));

//attachment
$body .= "--$boundary\r\n";
$body .="Content-Type: $file_type; name=".$_SESSION['filename']."\r\n";
$body .="Content-Disposition: attachment; filename=".$_SESSION['filename']."\r\n";
$body .="Content-Transfer-Encoding: base64\r\n";
$body .="X-Attachment-Id: ".rand(1000,99999)."\r\n\r\n";
$body .= $encoded_content;
   
$sentMail = @mail($recipient_email, $subject, $body, $headers);

/*if($sentMail) //output success or failure messages
    {      
        die('Thank you for your email');
    }else{
        die('Could not send mail! Please check your PHP mail configuration.');  
    }
*/
//===========end of sending email=========
unlink($_SESSION['filename']); //==== remove file after making zip file
} ?>