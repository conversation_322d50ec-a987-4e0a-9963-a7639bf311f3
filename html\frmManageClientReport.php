<script src="js/jquery.table2excel.js"></script>
<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Customer Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form action="#">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
									 <? $listParty=$objParty->selectStatus();?>
                                    <label>Party Name :</label>
									<select name="Client_Name" id="Client_Name"  class="js-example-basic-single form-control col-sm-12">
										<option value="">All</option>
                                        <? for($i=0;$i<count($listParty);$i++){ ?>
											<option value="<?=$listParty[$i]['id'];?>" <?=($_GET['Client_Name']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
										<? } ?>
									</select>
								</div>
													<?php /*?><div class="col-sm-12 col-md-4">
													<? $listArea=$objArea->selectStatus();?>
														<label>Area Name :</label>
														<select name="Area_Name" id="Area_Name"   class="js-example-basic-single form-control col-sm-12">
															<option value="">All</option>
															<? for($i=0;$i<count($listArea);$i++)
											{ ?>
											<option value="<?=$listArea[$i]['id'];?>" <?=($_GET['Area_Name']==$listArea[$i]['id']) ? "selected" : "";?>><?=$listArea[$i]['area_name'];?></option>
											<? } ?>
									</select>
								</div><?php */?>
                                <script language="javascript">
								function printclient()
								{
									var pname = document.getElementById("Client_Name").value; 
									
									window.open(
								'<?=BASE_URL;?>/print_client_details.php?Client_Name='+pname,'_blank'
									  // http://erp.erpdemocompany123.com/demo 
									  // <- This is what makes it open in a new window.
									);
								}
								
								function printclientenvelope()
								{
									if(document.getElementById("Client_Name").value!=""){
										var pname = document.getElementById("Client_Name").value; 
										window.open('<?=BASE_URL;?>/print_client_detail_envelope.php?Client_Name='+pname,'_blank');
									}
									else
										alert("Please select Party");
								}
								
								</script>
								<div class="form-group col-md-4">
									<label>&nbsp;</label><br>
									<button name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a onclick="javascript:printclient()" class="btn btn-info btn-sm has-ripple">Print</a><? } ?>
									<a onclick="javascript:document.location='codeManageClientReport.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
									<? if($_SESSION['act_prn']==1){ ?>
                                    <a id="download" class="btn btn-info btn-sm has-ripple">Export to Excel</a><? } ?>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive">
							<table id="tblClientReport" class="table table-striped table-bordered nowrap table-responsive table2excel tabular">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Party Name</th>
										<th>Address Details</th>
										<th>Office Number</th>
										<th>Email</th>
										<th>Website</th>
										<th>Contact Person</th>
										<th>Contact Number</th>
										<th>Contact Email</th>
									</tr>
								</thead>
								<tbody>
                                <? if(count($listRec)>0)
									 {
									   for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
                                        <td class="left-align"><?=$listRec[$e]['party_name'];?></td>
                                        <td class="left-align"><?=$listRec[$e]['party_address'];?><br/>
                                            <?=$listRec[$e]['city'];?> - <?=$listRec[$e]['zipcode'];?><br/>
                                            <?=$objState->getNameById($listRec[$e]['state']);?>, <?=$listRec[$e]['country'];?>                                        </td>
                                        <td class="left-align"><?=$listRec[$e]['office_no1'];?></td>
                                        <td class="left-align"><?=$listRec[$e]['email'];?></td>
                                        <td class="left-align"><?=$listRec[$e]['domain_name'];?></td>
                                        <td class="left-align"> <?=$listRec[$e]['contact_name1'];?></td>
                                        <td class="left-align"><?=$listRec[$e]['contact_no1'];?></td>
                                        <td class="left-align"><?=$listRec[$e]['email1'];?></td>
									</tr>
                                    <? } 
									} ?>
								</tbody>
							</table>
						</div>
						<!-- Table -->			
						<!--========== List View ==========-->
						
                    </div>
                </div>
            </div>
        </div>		
		
	</div>
</div>
<script>
var $j = jQuery.noConflict();
$j(document).ready(function () {
	$j('#download').click(function() {
		$j(".table2excel").table2excel({
			exclude: ".noExl",
			name: "Excel Document Name",
			filename: "Client_Detail_Report",
			fileext: ".xls",
			exclude_img: true,
			exclude_links: false,
			exclude_inputs: true
		});
	});
});
</script>
<script>
	setup();
</script>