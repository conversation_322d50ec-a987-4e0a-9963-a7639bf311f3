<? session_start();
include("inc/config.php");
include("editor/fckeditor.php");
include("inc/cpanel_functions.php");
checkLogin();
$con = new MySQLCN;

extract($_REQUEST);
include("inc/msg.inc.php");
$con=new MySQLCN;

$tablename='consignment';
$field_id='conId';
$field_status = 'adminStatus';
		
if(isset($_REQUEST['id'])) {
	$sql="select *, date_format(conDate, '%d-%m-%Y') as cdt from consignment where conId=".$_REQUEST['id'];
	$cmRecById=$con->select($sql); 
}

$sql="select *, date_format(conDate, '%d-%m-%Y') as cdt from consignment";
$cmRec=$con->select($sql);  // cm for consignment

$sql="SELECT * FROM `globalconfig`";
$rsconfig = $con->select($sql);
?>
<link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<link href="menu/menu.css" rel="stylesheet" type="text/css" />
<link href="css/pagingstyle1.css" rel="stylesheet" type="text/css" />

<? //================calendar js and css=================== ?>
<style>
.action{
	display:block;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight:100;
	text-align:left;
	padding:10px;
}
.action a{
	color:#6D6D6E;
	text-decoration:none;
}
.calendar{border:none}
.navigation{
 border:#999999 1px solid;
 background:#EBEBEB;
 color:#CCCCCC;
 vertical-align:middle;
 	
}
.calendarHeader{
 border:#999999 1px solid;
 background:#DBDBDB;
 color:#000000;
 font-family:Verdana, Arial, Helvetica, sans-serif;
 font-size:12px;
 font-weight:bold;
 padding:20px 10px;
 
 }
 
.cal_dates{
	border:#999999 1px solid;
	background:#EEEEEE;
	color:#000000;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:12px;
	font-weight:bold;
	height:55px;
	vertical-align:middle;
 }
.cal_dates_hover{
	border:#006699 1px solid;
	background-color:#EAD9CA;
	color:#000000;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:11px;
	height:55px;	
	vertical-align:middle;
 }
.event_dates{
	border:#006699 1px solid;
	background-color:#F3DFE2;
	color:#000000;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:11px;
	height:55px;	
	vertical-align:middle;
 }

.cal_dates_hover a{ color:#003366;text-decoration:none}
.cal_dates_hover a:hover{ color:#000000;text-decoration:none}

.cal_dates_empty{
	border:#999999 1px solid;
	background:#EFEFEF;
	color:#000000;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:12px;
	font-weight:bold;
 }
.holiday_name {
	text-align:left;
	background:url(images/arrowbullet.png) no-repeat left 3px;
	padding-left:15px;
	height:15px;
	}
</style>
<link type="text/css" rel="stylesheet" href="css/jscal2.css" />
<link type="text/css" rel="stylesheet" href="css/border-radius.css" />
<link id="skinhelper-compact" type="text/css" rel="alternate stylesheet" href="css/reduce-spacing.css" />
<script src="js/jscal2.js"></script>
<script src="js/lang/en.js"></script>
<script type="text/javascript">
<!--
function date_hover_on(obj,index){
	obj.className='cal_dates_hover';
}
function date_hover_off(obj,index){
	obj.className='cal_dates';
}
//-->
</script>

<div id="form_container" >
<form action="manage_consignments.php" method="post" name="frmAddConsignment" onsubmit="MM_validateForm('Sr_No','','R','Lorry_No','','R','NoOfArticle','','R','From','','R','To','','R','No_Of_Article','','R','Weight','','R','Rate','','R','Freight','','R','Service_Tax','','R','Total','','R','Consignor','','R','Consignee','','R','Nature_Of_Goods','','R');return document.MM_returnValue" >
 <input name="hidConsignId" type="hidden" value="<?=$_GET['id'];?>" />
	<fieldset>
    	<legend>&nbsp;&nbsp;&nbsp;GOODS CONSIGNMENT NOTE&nbsp;&nbsp;&nbsp;</legend>     
<div>        
<table width="90%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td width="16%"> No. : <?=$cmRecById[0]['conSrno'];?></td>
      </tr>
</table>
</div>
<div>        
<table width="90%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td width="50%">Lorry No. : <?=$cmRecById[0]['conLorryno'];?></td>
    <td align="right">Date : <?=$cmRecById[0]['cdt'];?>&nbsp;</td>
  </tr>
</table>
</div>

<div>
<table width="90%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td>Consignor:<?=$cmRecById[0]['conConsignor'];?></td>
    <td align="center"><strong>From :</strong>&nbsp;<?=$cmRecById[0]['conFrom'];?>&nbsp;To :&nbsp;<?=$cmRecById[0]['conTo'];?></td>
    <td align="right">Consignee :&nbsp;<?=$cmRecById[0]['conConsignee'];?></td>
  </tr>
 </table>
</div>

<div style="text-align:center;">
<table width="97%" border="1" cellspacing="0" cellpadding="0" class="maintable">
  <tr class="toprow">
    <td rowspan="3">No. of <br />
      Article</td>
    <td rowspan="3">NATURE OF GOODS SAID to CONTAIN<br />
  &#2730;&#2765;&#2736;&#2741;&#2750;&#2745;&#2752; &#2734;&#2750;&#2738;&#2728;&#2752; &#2738;&#2752;&#2709;&#2759;&#2716;&#2728;&#2752; &#2716;&#2741;&#2750;&#2732;&#2726;&#2750;&#2736;&#2752; &#2693;&#2734;&#2750;&#2736;&#2752; &#2728;&#2725;&#2752;</td>
    <td rowspan="3">WEIGHT <br />
      Actual <br />
      Kg.</td>
    <td rowspan="3">Rate <br />
      Per Kg.</td>
    <td colspan="2">FREIGHT</td>
    <td rowspan="3">Remarks</td>
  </tr>
  <tr class="toprow">
    <td valign="top">TO PAY </td>
    <td valign="top">PAID </td>
    </tr>
  <tr class="toprow">
    <td>Rs. . Ps.</td>
    
    <td>Rs. . Ps.</td>
  </tr>
  <tr class="datarow">
    <td rowspan="2"><?=$cmRecById[0]['conNo_of_article'];?></td>
    <td rowspan="2" style="text-align:left; padding-left:5px;"><?=$cmRecById[0]['conNature_of_Goods'];?></td>
    <td valign="top"><?=$cmRecById[0]['conWeight'];?></td>
    <td height="143"><?=$cmRecById[0]['conRate'];?></td>
    <?  $pl = 3;
	if($cmRecById[0]['conTo_pay_paid']==2) { ?> 
    <td>&nbsp;</td><? } ?>
    <td style="text-align:right;padding-right:<?=$pl;?>px;"><?=$cmRecById[0]['conFreight'];?></td>
    <? if($cmRecById[0]['conTo_pay_paid']==1) { ?> 
    <td>&nbsp;</td><? } ?>
    <td rowspan="6"><?=$cmRecById[0]['conRemark'];?></td>
  </tr>
  <tr class="datarow">
    <td valign="top" style="border-top:#CCCCCC 1px solid; border-bottom:#CCCCCC 1px solid;">Charged Rs.</td>
    <td style="text-align:left; padding-left:5px;" >Hamali</td>
	<? if($cmRecById[0]['conTo_pay_paid']==2) { ?> 
    <td>&nbsp;</td><? } ?>
    <td style="text-align:right;padding-right:<?=$pl;?>px;"><?=$cmRecById[0]['conHamali'];?></td>
	<? if($cmRecById[0]['conTo_pay_paid']==1) { ?> 
    <td>&nbsp;</td><? } ?>
  </tr>
  <tr class="datarow">
    <td colspan="2" align="center" valign="middle" style="font-weight:bold;">
<?  if($_REQUEST['co']=="Consignee") echo  "CONSIGNEE'S COPY";
	if($_REQUEST['co']=="Driver") echo "DRIVER'S COPY";
	if ($_REQUEST['co']=="Office") echo "OFFICE COPY"; 
	if ($_REQUEST['co']=="Consignor") echo "CONSIGNOR'S COPY"; ?>
</td>
    <td valign="top"><?=$cmRecById[0]['conCharged'];?></td>
    <td style="text-align:left; padding-left:5px;">Surcharge</td>
	<? if($cmRecById[0]['conTo_pay_paid']==2) { ?> 
    <td>&nbsp;</td><? } ?>
    <td style="text-align:right;padding-right:<?=$pl;?>px;"><?=$cmRecById[0]['conSurcharge'];?></td>
    <? if($cmRecById[0]['conTo_pay_paid']==1) { ?> 
    <td>&nbsp;</td><? } ?>
  </tr>
  <tr class="datarow">
    <td colspan="2" rowspan="3" align="center" valign="middle"><table width="100%" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td style="border-right:#CCCCCC 1px solid;line-height:20px;text-align:left;padding-left:10px;">Service Tax Payable By :<br />
	<? echo ($cmRecById[0]['conTax_payable_by']==1) ? "<img src='images/right.gif' border='0'>&nbsp;CONSIGNOR" : "&nbsp;&nbsp;&nbsp;&nbsp;CONSIGNOR"; ?><br />
	<? echo ($cmRecById[0]['conTax_payable_by']==2) ? "<img src='images/right.gif' border='0'>&nbsp;CONSIGNEE" : "&nbsp;&nbsp;&nbsp;&nbsp;CONSIGNEE"; ?><br />
	<? echo ($cmRecById[0]['conTax_payable_by']==3) ? "<img src='images/right.gif' border='0'>&nbsp;TRANSPORTER" :  "&nbsp;&nbsp;&nbsp;&nbsp;TRANSPORTER";?></td>
        <td align="center" style="line-height:20px;">Service Tax Reg. No.<br />
          <? echo $rsconfig[6]['configValue'];?><br />
          PAN No. <? echo $rsconfig[8]['configValue'];?></td>
      </tr>
    </table></td>
    <td style="border-top:#CCCCCC 1px solid; border-bottom:#CCCCCC 1px solid; ">O/R</td>
    <td style="text-align:left; padding-left:5px;">Insurance</td>
	<? if($cmRecById[0]['conTo_pay_paid']==2) { ?> 
    <td>&nbsp;</td><? } ?>    
    <td style="text-align:right;padding-right:<?=$pl;?>px;"><?=$cmRecById[0]['conInsurance'];?></td>
<? if($cmRecById[0]['conTo_pay_paid']==1) { ?> 
    <td>&nbsp;</td><? } ?>    
  </tr>
  <tr class="datarow">
    <td valign="top">Value Rs.</td>
    <td style="text-align:left; padding-left:5px;">Service Tax</td>
	<? if($cmRecById[0]['conTo_pay_paid']==2) { ?> 
    <td>&nbsp;</td><? } ?>    
    <td style="text-align:right;padding-right:<?=$pl;?>px;"><?=$cmRecById[0]['conService_tax'];?> </td>
<? if($cmRecById[0]['conTo_pay_paid']==1) { ?> 
    <td>&nbsp;</td><? } ?>  </tr>
  <tr class="datarow">
    <td valign="top"><?=$cmRecById[0]['conValue'];?></td>
    <td style="text-align:left; padding-left:5px;">TOTAL</td>
	<? if($cmRecById[0]['conTo_pay_paid']==2) { ?> 
    <td>&nbsp;</td><? } ?>    
    <td style="text-align:right;padding-right:<?=$pl;?>px;"><?=$cmRecById[0]['conTotal_amount'];?></td>
	<? if($cmRecById[0]['conTo_pay_paid']==1) { ?> 
    <td>&nbsp;</td><? } ?>  </tr>
<tr><td colspan="4" style="padding:5px;">
(1)&nbsp;The G.C. Note issued subject to the Terms &amp; Conditions Printed overleaf which shall be binding on <br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the Consignor as well as the Consignee.<br/>
(2)&nbsp;We are not responsible for any loss &amp; leakage of oil and liquid articles. </td>
  <td colspan="4" valign="middle" align="center" style="font-weight:bold;">For, Shree Kankeshwar Transport Co.</td>
  </tr>    
</table>
</div>
 </fieldset>
</form>
</div>
<script>
	window.print();
</script>