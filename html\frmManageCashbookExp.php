<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>Manage Payment</h5>
							</div>
							<div class="col-md-6">
								
								<? if(isset($_REQUEST['btnAddUser']) || isset($_GET['id'])){ ?>
								
									<form action="<?=$pageName;?>" method="post">
										<button type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack">
											<i class="fas fa-angle-left"></i> Back
										</button>
									</form>
								
								<? }
								else{?>
								
									<form action="<?=$pageName;?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add Payment
										</button>
									</form>									
								
								<? } ?>
								
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<? if(isset($_REQUEST['btnAddUser']) || isset($_GET['id'])){ ?>
						
						<!--========== Add Payment ==========-->
						<form action="<?=$pageName;?>" method="post" name="frmcb" id="frmcb">
                        <input name="hid" id="hid" type="hidden" value="<?=$_GET['id'];?>" />
						<input type="hidden" name="action" id="action" value="" />
                            <div class="form-row">
								<div class="form-group col-md-4">
                                    <label>Date</label>
                                    <input type="text" name="Transaction_Date" id="Transaction_Date" value="<?=($listEdit[0]['trn_dt']!="") ? $listEdit[0]['trn_dt'] : date("d-m-Y");?>"  class="form-control dd">
                                </div>
                                <div class="form-group col-md-4">
                                	<?	//============generate bill no.=========
										$objYear->company=$_SESSION['company'];
										$objYear->fyear=$_SESSION['fyear'];
										$cashId=$objYear->selectCidByCompanyYear(); 
										$new_receipt_no = sprintf('%03d',$cashId[0]['last_cashbook_id_exp']+1);	
									
										$receipt_no="TS/".$_SESSION['fyear']."/EXPRC/";
										$receipt_no.=$new_receipt_no; ?>
                                    <label>Receipt No.</label>
                                    <input type="text" name="Receipt_No" id="Receipt_No" value="<?=isset($listEdit) ? $listEdit[0]['receipt_no'] : $receipt_no;?>" class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                	 <? $listParty=$objParty->selectStatusExp();?>
                                    <label>Party Name</label>
                                    <select name="Party_Name" id="Party_Name" class="js-example-basic-single form-control" onchange="getpartybalance()">
                                        <option value="">--Select Party--</option>
                                        <? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
                                    </select>
                                    <b id="party_balance"></b>
                                </div>									
								<div class="form-group col-md-4">
                                    <label>Details</label>
                                    <textarea type="text" name="Transaction_Detail" id="Transaction_Detail" class="form-control"><?=$listEdit[0]['transaction_detail'];?></textarea>
                                </div>
								<div class="form-group col-md-4">
                                    <?	if($listEdit[0]['credit_amount'] > 0){
											$amount = $listEdit[0]['credit_amount'];
											$credit_debit = 0;
										}
										elseif($listEdit[0]['debit_amount'] > 0){
											$amount = $listEdit[0]['debit_amount'];
											$credit_debit = 1;
										}
									?>
                                    <label>Amount</label>
                                    <input type="text" name="Amount" id="Amount" value="<?=$amount;?>" class="form-control" onblur="chk_taxtype()" />
                                </div>	
								
                                <div class="form-group col-md-2">
									<label>Currency</label>
                                    <? $listCrn = $objCrn->selectStatus(); ?>
									<select name="Currency" id="Currency" onchange="getcurrencyinr(this)"  required class="form-control">
										<option>-- Select --</option>
										<? for($c=1;$c<count($listCrn);$c++){ ?>
                                        <option value="<?=$listCrn[$c]['id'];?>" <?=($listEdit[0]['currency']==$listCrn[$c]['id']) ? "selected" : "";?>><?=$listCrn[$c]['currency_name'];?></option>
                                    <? } ?>
									</select>
								</div>
								<div class="form-group col-md-2">
									<label>&nbsp;</label>
									<input type="text" name="currency_inr" id="currency_inr" value="<?=($_GET['id']!="") ? $listEdit[0]['currency_inr'] : "";?>" class="form-control">
								</div>
                                
                                
                                
                                <div class="form-group col-md-4">
                                    <label>Transaction Type</label>
                                    <select class="form-control" name="Transaction_Type" id="Transaction_Type" onchange="checkPaytype(this)">
										<option value="Cash" <?=($listEdit[0]['transaction_type']=="Cash") ? "selected" : "";?>>Cash</option>
                                        <option value="Cheque" <?=($listEdit[0]['transaction_type']=="Cheque") ? "selected" : "";?>>Cheque</option>
                                        <option value="NEFT" <?=($listEdit[0]['transaction_type']=="NEFT") ? "selected" : "";?>>NEFT</option>
                                        <option value="IMPS" <?=($listEdit[0]['transaction_type']=="IMPS") ? "selected" : "";?>>IMPS</option>
                                        <option value="RTGS" <?=($listEdit[0]['transaction_type']=="RTGS") ? "selected" : "";?>>RTGS</option>
                                        <option value="UPI" <?=($listEdit[0]['transaction_type']=="UPI") ? "selected" : "";?>>UPI</option>
                                        <option value="Paytm" <?=($listEdit[0]['transaction_type']=="Paytm") ? "selected" : "";?>>Paytm</option>
                                        <option value="PhonePe" <?=($listEdit[0]['transaction_type']=="PhonePe") ? "selected" : "";?>>PhonePe</option>
                                        <option value="PayPal" <?=($listEdit[0]['transaction_type']=="PayPal") ? "selected" : "";?>>PayPal</option>
									</select>
                                </div>	
								
								
								
								<div class="col-md-8"></div>
								<div class="clearfix"></div>
								
                                <div id="chk_det" style="display:<?=($listEdit[0]['transaction_type']=="Cheque") ? "block" : "none";?>; width:100%;">
                                    <div class="form-group col-md-4" style="float:left;">
                                        <label><strong>Cheque Number : </strong></label>
                                        <input type="text" name="Cheque_No" id="Cheque_No" value="<?=$listEdit[0]['cheque_no'];?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-4" style="float:left;">
                                        <label><strong>Cheque Date : </strong></label>
                                        <input  type="text" name="Cheque_Date" id="Cheque_Date" value="<?=($listEdit[0]['chq_dt']!="") ? $listEdit[0]['chq_dt'] : date("d-m-Y");?>"  class="form-control dd">
                                    </div>
                                    <div class="form-group col-md-4" style="float:left;">
                                        <label><strong>Cheque Detail : </strong></label>
                                        <textarea name="Cheque_Detail" id="Cheque_Detail"  class="form-control"><?=$listEdit[0]['cheque_detail'];?></textarea>
                                    </div>
                                 </div>
                                 
                                 <div id="neft_det" style="display:<?=(isset($_GET['id']) && $listEdit[0]['transaction_type']!="Cash" and $listEdit[0]['transaction_type']!="Cheque") ? "block" : "none";?>;  width:100%;">
                                    <div class="form-group col-md-4" style="float:left;">
                                        <label><strong>Reference Number : </strong></label>
                                        <input type="text" name="Ref_No" id="Ref_No" value="<?=$listEdit[0]['ref_no'];?>" class="form-control">
                                    </div>
                                 </div>
                                 <div class="clearfix"></div>
                            </div>
                            <?php /*?><button type="submit" name="btnAdd" value="Add Payment Details" class="btn btn-success">Add Payment Details</button><?php */?>
                            <?php if(isset($_GET['id']) && $_GET['id']!=""){?>
<input type="submit" name="btnUpdate" value="Update Payment Details" class="btn btn-success" />
<?php } else { ?>
<input type="submit" name="btnAdd" value="Add Payment Details" class="btn btn-success" />
<?php } ?>  
                            
                        </form>
						<!--========== Add Payment ==========-->
						
						
						<? }
						else {
						?>
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form action="#">
                        <script language="javascript">
						function printInvoice()
						{
							window.open('<?=BASE_URL;?>print_invoice_all_exp.php?Party_Name=<?=$_GET['Client_Name'];?>&Invoice_Number=<?=$_GET['Invoice_Number'];?>&From_Date=<?=$_GET['From_Date'];?>&To_Date=<?=$_GET['To_Date'];?>','_blank');
						}
						
						
						function printparty()
						{
							var pname = document.getElementById("Client_Name").value;
							var ino = document.getElementById("Invoice_Number").value;
							if(document.getElementById("From_Date").value=="")
								var fdate = "";
							else
								var fdate = document.getElementById("From_Date").value;
								
							if(document.getElementById("To_Date").value=="")
								var tdate = "";
							else
								var tdate = document.getElementById("To_Date").value;
								
							if(pname!="" || (fdate!="" && tdate!="") || ino!=""){	
								window.open('<?=BASE_URL;?>print_invoice_all_exp.php?Party_Name='+pname+'&Invoice_Number='+ino+'&From_Date='+fdate+'&To_Date='+tdate,'_blank');
							}
							else{
								alert("Please select party name or enter invoice number");
							}
						}
						</script>
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
                                	<? $listParty=$objParty->selectStatusExp();?>
									<label>Search Party Name :</label>
									<select name="Client_Name" id="Client_Name" class="js-example-basic-single form-control" onchange="getpartybalance()">
                                        <option value="">--Select Party--</option>
                                        <? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
                                    </select>
								</div>
								<div class="form-group col-md-4">
                                    <label>Cheque No.:</label>
                                    <input type="text" id="" name="" placeholder="Enter multiple entry with ( , )" class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                    <label>Reference No.</label>
                                    <input type="text" id="" name="" class="form-control">
                                </div>
								<div class="form-group col-md-3">
									<label>From Date</label>
									<input type="text" name="From_Date" id="From_Date" size="10" value="<?=$_REQUEST['From_Date'];?>" onblur="chkDateRange()" class="form-control dd">
                                </div>
								<div class="form-group col-md-3">
									<label>To Date</label>
									<input type="text" class="form-control dd" name="To_Date" id="To_Date" size="10" value="<?=$_REQUEST['To_Date'];?>" >
                                </div>
								<div class="form-group col-md-6">
									<label>&nbsp;</label><br>
									<button type="submit" name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple"><i class="fas fa-search" aria-hidden="true"></i></button>
                                        <button type="button" name="print" id="print" class="btn btn-info btn-sm has-ripple" value="Print" onclick="javascript:printInvoice()">Print</button>
										<a href="<?=$pageName;?>" class="btn btn-info btn-sm has-ripple">Reset</a>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive">
                        <form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                         <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
						<div id="error_frmvalidate" class="err_msg" style="float:right; font-weight:bold; font-style:italic; padding:3px;"></div><div style="clear:both;"></div>
							<table id="simpletable" class="table table-striped table-bordered nowrap table-responsive" style="width:100%!important;">
								<thead>
									<tr>
										<th colspan="8" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select name="optAction" id="optAction" class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													<option value="">-- Select Action --</option>
													<option value="Delete">Delete</option>
												</select>
											</label>
											<input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" value="Submit" name="btnAction" id="btnAction" />
										</th>			
									</tr>
									<tr>
										<th>Sr. No.</th>
										<th>Party</th>
										<th>Receipt No.</th>
										<th>Credit Amount</th>
										<th>Debit Amount</th>
										<th>Date</th>
										<th>Payment Type</th>
										<th>
											<? if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
                                            <input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"/>
                                            <? } ?>
                                            Action
										</th>
									</tr>
								</thead>
								<tbody>
                                <?  if(count($listRec)>0)
									{	
										for($e=0;$e<count($listRec);$e++){ ?>
                                        <tr>
                                            <td><?=$e+1;?></td>
                                            <td><?
                                                    $objParty->id = $listRec[$e]['party'];
                                                    $partynm = $objParty->selectRecById();
                                                    echo $partynm[0]['party_name'];
                                                ?>
                                            </td>
                                            <td><?=$listRec[$e]['receipt_no'];?></td>
                                            <td><?=number_format($listRec[$e]['credit_amount'],2);?></td>
                                            <td><?=number_format($listRec[$e]['debit_amount'],2);?></td>
                                            <td><?=$listRec[$e]['tdt'];?></td>
                                            <td><?=$listRec[$e]['transaction_type'];?>
                    							<?=($listRec[$e]['transaction_type']=="Cheque") ? "<br/>".$listRec[$e]['cheque_no'] : "";?></td>
                                            <td class="table-action">
                                                <? if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
                                                    <input type="checkbox" name="chkAction[]" id="chkAction[]"  value="<? echo $listRec[$e]['id'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10 chkboxcontent"/>&nbsp;<? } ?>
											<?php
                                            if($_SESSION['act_edit']==1){ ?>
                                                 <a href="<?=$pageName;?>?id=<?=$listRec[$e]['id'];?>"><i class="fas fa-pencil-alt mr-10"></i></a><? } 
                                                 if($_SESSION['act_del']==1){ ?><a onclick="deleteAll(<?php echo $listRec[$e]['id'];?>,'','Cashbook','<?=$pageName;?>')"><i class="fas fa-trash-alt clr-red mr-10"></i></a><? } ?>
                                            
                                            <?php /*?><a href="view_payment_receipt_exp.php?id=<?=$listRec[$e]['id'];?>" target="_blank" class="btn btn-info btn-sm has-ripple">View <span class="ripple ripple-animate"></span></a><?php */?>
                                            </td>
                                        </tr>
                                    <? }
									} ?>
								</tbody>
							</table>
                            </form>
						</div>
						<!-- Table -->
						<!--========== List View ==========-->
						
						<? } ?>
						
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>
<script>
function checkPaytype(obj)
{
	document.getElementById("chk_det").style.display = "none";
	document.getElementById("neft_det").style.display = "none";

	if(document.getElementById("Transaction_Type").value=="Cheque")
		 document.getElementById("chk_det").style.display = "block";
	else if(document.getElementById("Transaction_Type").value!="Cash")
		 document.getElementById("neft_det").style.display = "block";
	/* else
		document.getElementById("chk_det").style.display = "none"; */
}

function chk_taxtype(){
	document.getElementById("gst").style.display="none";
	document.getElementById("igst").style.display="none";
	if(document.getElementById("chkTaxType1").checked){
		document.getElementById("gst").style.display="block";
		document.getElementById("cgst_amt").value=document.getElementById("Amount").value*9/100;
		document.getElementById("sgst_amt").value=document.getElementById("Amount").value*9/100;
	}
	else if(document.getElementById("chkTaxType2").checked){
		document.getElementById("igst").style.display="block";
		document.getElementById("igst_amt").value=document.getElementById("Amount").value*18/100;
	}
}
</script>