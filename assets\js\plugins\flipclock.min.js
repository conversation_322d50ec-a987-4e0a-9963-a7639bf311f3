!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.FlipClock=t()}(this,function(){"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&_setPrototypeOf(e,t)}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(e,t){return e.__proto__=t,e})(e,t)}function isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}function _construct(e,t,n){return(_construct=isNativeReflectConstruct()?Reflect.construct:function _construct(e,t,n){var i=[null];i.push.apply(i,t);var r=new(Function.bind.apply(e,i));return n&&_setPrototypeOf(r,n.prototype),r}).apply(null,arguments)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _possibleConstructorReturn(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?_assertThisInitialized(e):t}function _superPropBase(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=_getPrototypeOf(e)););return e}function _get(e,t,n){return(_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function _get(e,t,n){var i=_superPropBase(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(n):r.value}})(e,t,n||e)}function error(e){throw Error(e)}function callback(e){if(isFunction(e)){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return e.call.apply(e,[this].concat(n))}}function round(e){return isNegativeZero(e=isNegative(e)?Math.ceil(e):Math.floor(e))?("-"+e).toString():e}function noop(e){return!isUndefined(e)&&!isNull(e)}function chain(e,t){return function(){return t(e())}}function concatMap(e){return function(t){return t.map(e).reduce(function(e,t){return e.concat(t)},[])}}function flatten(e){return concatMap(function(e){return e})(e)}function deepFlatten(e){return concatMap(function(e){return Array.isArray(e)?deepFlatten(e):e})(e)}function length(e){return deepFlatten(e).length}function isNegativeZero(e){return 1/Math.round(e)==-1/0}function isNegative(e){return isNegativeZero(e)||e<0}function isNull(e){return null===e}function isUndefined(e){return void 0===e}function isConstructor(e){return e instanceof Function&&!!e.name}function isString(e){return"string"==typeof e}function isArray(e){return e instanceof Array}function isObject(e){var t=_typeof(e);return null!=e&&!isArray(e)&&("object"==t||"function"==t)}function isFunction(e){return e instanceof Function}function isNumber(e){return!isNaN(e)}function kebabCase(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\s+/g,"-").toLowerCase()}function digitize(e,t){function prepend(e){return(t.prependLeadingZero&&1===e.toString().split("").length?"0":"").concat(e)}return t=Object.assign({minimumDigits:0,prependLeadingZero:!0},t),function digits(e,t){var n=deepFlatten(e).length;if(n<t)for(var i=0;i<t-n;i++)e[0].unshift("0");return e}(flatten([e]).map(function(e){return flatten(deepFlatten([e]).map(function(e){return prepend(e).split("")}))}),t.minimumDigits||0)}function format(e,t){switch(t){case"number":return parseFloat(e)}return e}function findRange(e){for(var n in t){var i=e.toString().charCodeAt(0);if(t[n].min<=i&&t[n].max>=i)return t[n]}return null}function stringFromCharCodeBy(e,t){return String.fromCharCode(t(findRange(e),e.charCodeAt(0)))}function next(e){return format(e.toString().split("").map(function(e){return stringFromCharCodeBy(e,function(e,t){return!e||t<e.max?t+1:e.min})}).join(""),_typeof(e))}function prev(e){return format(e.toString().split("").map(function(e){return stringFromCharCodeBy(e,function(e,t){return!e||t>e.min?t-1:e.max})}).join(""),_typeof(e))}function validate(e){for(var t=!1,n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];return flatten(i).forEach(function(n){(isNull(e)&&isNull(n)||isObject(n)&&e instanceof n||isFunction(n)&&!isConstructor(n)&&!0===n(e)||isString(n)&&_typeof(e)===n)&&(t=!0)}),t}function language(e){return e?Ge[e.toLowerCase()]||Object.values(Ge).find(function(t){return-1!==t.aliases.indexOf(e)}):null}function translate(e,t){var n=isString(t)?language(t):t;return(n.dictionary||n)[e]||e}function swap(e,t){return t.parentNode?(t.parentNode.replaceChild(e,t),e):t}function setAttributes(e,t){if(isObject(t))for(var n in t)e.setAttribute(n,t[n]);return e}function appendChildren(e,t){return isArray(t)&&t.filter(noop).forEach(function(t){t instanceof HTMLElement&&e.appendChild(t)}),e}function createElement(e,t,n){return e instanceof HTMLElement||(e=document.createElement(e)),setAttributes(e,isObject(t)?t:n),isObject(t)||isArray(t)?appendChildren(e,t):e.innerHTML=t,e}function Divider$1(e,t){appendChildren(e,[createElement("div",{class:"flip-clock-dot top"}),createElement("div",{class:"flip-clock-dot bottom"})])}function child(e,t){return e?e.childNodes?e.childNodes[t]:e[t]:null}function _char(e){return e?e.querySelector(".flip-clock-list-item:first-child .top").innerHTML:null}function FlipClock(e,t){appendChildren(e,t.value.digits.map(function(e,n){var i=child(t.el?t.el.querySelectorAll(".flip-clock-group"):null,n),r=e.map(function(e,n){var r=_char(child(i?i.querySelectorAll(".flip-clock-list"):null,n));return t.createList(e,{domValue:r,countdown:t.countdown,animationRate:t.face.animationRate||t.face.delay})});return t.createGroup(r)}).map(function(e){return e.render()}))}function Group$1(e,t){appendChildren(e,t.items.map(function(e){return e.render()}))}function Label$1(e,t){e.innerHTML=t.t(t.label)}function List$1(e,t){var n=t.domValue||(t.countdown?next(t.value):prev(t.value));t.domValue&&t.domValue!==t.value&&e.classList.add("flip"),e.style.animationDelay="".concat(t.animationRate/2,"ms"),e.style.animationDuration="".concat(t.animationRate/2,"ms"),t.items=[t.createListItem(t.value,{active:!0}),t.createListItem(n,{active:!1})],appendChildren(e,t.items.map(function(e){return e.render()}))}function ListItem$1(e,t){var n=!0===t.active?"active":!1===t.active?"before":null;e.classList.add(n),appendChildren(e,[createElement("div",[createElement("div",t.value,{class:"top"}),createElement("div",t.value,{class:"bottom"})],{class:"flip-clock-list-item-inner"})])}function DayCounter$1(e,t){t.createDivider().mount(e,e.childNodes[1]),t.createDivider().mount(e,e.childNodes[3]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[5]),t.face.showLabels&&(t.createLabel("days").mount(e.childNodes[0]),t.createLabel("hours").mount(e.childNodes[2]),t.createLabel("minutes").mount(e.childNodes[4]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[6]))}function HourCounter$1(e,t){t.createDivider().mount(e,e.childNodes[1]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[3]),t.face.showLabels&&(t.createLabel("hours").mount(e.childNodes[0]),t.createLabel("minutes").mount(e.childNodes[2]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[4]))}function MinuteCounter$1(e,t){t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[1]),t.face.showLabels&&(t.createLabel("minutes").mount(e.childNodes[0]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[2]))}function TwentyFourHourClock$1(e,t){t.createDivider().mount(e,e.childNodes[1]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[3]),t.face.showLabels&&(t.createLabel("hours").mount(e.childNodes[0]),t.createLabel("minutes").mount(e.childNodes[2]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[4]))}function TwelveHourClock$1(e,t){if(TwentyFourHourClock$1(e,t),t.face.showMeridium&&t.face.meridium){var n=t.createLabel(t.face.meridium),i=e.childNodes[e.childNodes.length-1];n.mount(i).classList.add("flip-clock-meridium")}}function WeekCounter$1(e,t){t.createDivider().mount(e,e.childNodes[1]),t.createDivider().mount(e,e.childNodes[3]),t.createDivider().mount(e,e.childNodes[5]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[7]),t.face.showLabels&&(t.createLabel("weeks").mount(e.childNodes[0]),t.createLabel("days").mount(e.childNodes[2]),t.createLabel("hours").mount(e.childNodes[4]),t.createLabel("minutes").mount(e.childNodes[6]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[8]))}function YearCounter$1(e,t){t.createDivider().mount(e,e.childNodes[1]),t.createDivider().mount(e,e.childNodes[3]),t.createDivider().mount(e,e.childNodes[5]),t.createDivider().mount(e,e.childNodes[7]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[9]),t.face.showLabels&&(t.createLabel("years").mount(e.childNodes[0]),t.createLabel("weeks").mount(e.childNodes[2]),t.createLabel("days").mount(e.childNodes[4]),t.createLabel("hours").mount(e.childNodes[6]),t.createLabel("minutes").mount(e.childNodes[8]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[10]))}var e=function(){function Component(e){_classCallCheck(this,Component),this.setAttribute(Object.assign({events:{}},e))}return _createClass(Component,[{key:"emit",value:function emit(e){for(var t=this,n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];return this.events[e]&&this.events[e].forEach(function(e){e.apply(t,i)}),this}},{key:"on",value:function on(e,t){return this.events[e]||(this.events[e]=[]),this.events[e].push(t),this}},{key:"off",value:function off(e,t){return this.events[e]&&t?this.events[e]=this.events[e].filter(function(e){return e!==t}):this.events[e]=[],this}},{key:"once",value:function once(e,t){var n=this;return t=chain(t,function(){return n.off(e,t)}),this.on(e,t,!0)}},{key:"getAttribute",value:function getAttribute(e){return this.hasOwnProperty(e)?this[e]:null}},{key:"getAttributes",value:function getAttributes(){var e=this,t={};return Object.getOwnPropertyNames(this).forEach(function(n){t[n]=e.getAttribute(n)}),t}},{key:"getPublicAttributes",value:function getPublicAttributes(){var e=this;return Object.keys(this.getAttributes()).filter(function(e){return!e.match(/^\$/)}).reduce(function(t,n){return t[n]=e.getAttribute(n),t},{})}},{key:"setAttribute",value:function setAttribute(e,t){isObject(e)?this.setAttributes(e):this[e]=t}},{key:"setAttributes",value:function setAttributes(e){for(var t in e)this.setAttribute(t,e[t])}},{key:"callback",value:function callback$$1(e){return callback.call(this,e)}},{key:"name",get:function get(){return this.constructor.defineName instanceof Function||error("Every class must define its name."),this.constructor.defineName()}},{key:"events",get:function get(){return this.$events||{}},set:function set(e){this.$events=e}}],[{key:"make",value:function make(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return _construct(this,t)}}]),Component}(),t=[{min:48,max:57},{min:65,max:90},{min:97,max:122}],n=function(e){function FaceValue(e,t){var n;return _classCallCheck(this,FaceValue),(n=_possibleConstructorReturn(this,_getPrototypeOf(FaceValue).call(this,Object.assign({format:function format(e){return e},prependLeadingZero:!0,minimumDigits:0},t)))).value||(n.value=e),n}return _inherits(FaceValue,e),_createClass(FaceValue,[{key:"isNaN",value:function(e){function isNaN(){return e.apply(this,arguments)}return isNaN.toString=function(){return e.toString()},isNaN}(function(){return isNaN(this.value)})},{key:"isNumber",value:function isNumber$$1(){return isNumber()}},{key:"clone",value:function clone(e,t){return new this.constructor(e,Object.assign(this.getPublicAttributes(),t))}},{key:"digits",get:function get(){return this.$digits},set:function set(e){this.$digits=e,this.minimumDigits=Math.max(this.minimumDigits,length(e))}},{key:"value",get:function get(){return this.$value},set:function set(e){this.$value=e,this.digits=digitize(this.format(e),{minimumDigits:this.minimumDigits,prependLeadingZero:this.prependLeadingZero})}}],[{key:"defineName",value:function defineName(){return"FaceValue"}}]),FaceValue}(e),i={className:"The className() is not defined.",items:"The items property must be an array.",theme:"The theme property must be an object.",language:"The language must be an object.",date:"The value must be an instance of a Date.",face:"The face must be an instance of a Face class.",element:"The element must be an instance of an HTMLElement",faceValue:"The face must be an instance of a FaceValue class.",timer:"The timer property must be an instance of a Timer class."},r=function(e){function Face(e,t){var i;return _classCallCheck(this,Face),e instanceof n||!isObject(e)||(t=e,e=void 0),(i=_possibleConstructorReturn(this,_getPrototypeOf(Face).call(this))).setAttributes(Object.assign({autoStart:!0,countdown:!1,animationRate:500},i.defaultAttributes(),t||{})),(isNull(e)||isUndefined(e))&&(e=i.defaultValue()),e&&(i.value=e),i}return _inherits(Face,e),_createClass(Face,[{key:"interval",value:function interval(e,t){return this.countdown?this.decrement(e):this.increment(e),callback.call(this,t),this.shouldStop(e)&&e.stop(),this.emit("interval")}},{key:"shouldStop",value:function shouldStop(e){return!isUndefined(this.stopAt)&&this.stopAt===e.value.value}},{key:"format",value:function format(e,t){return t}},{key:"defaultValue",value:function defaultValue(){}},{key:"defaultAttributes",value:function defaultAttributes(){}},{key:"defaultDataType",value:function defaultDataType(){}},{key:"increment",value:function increment(e,t){}},{key:"decrement",value:function decrement(e,t){}},{key:"started",value:function started(e){}},{key:"stopped",value:function stopped(e){}},{key:"reset",value:function reset(e){}},{key:"initialized",value:function initialized(e){}},{key:"rendered",value:function rendered(e){}},{key:"mounted",value:function mounted(e){this.autoStart&&e.timer.isStopped&&window.requestAnimationFrame(function(){return e.start(e)})}},{key:"createFaceValue",value:function createFaceValue(e,t){var i=this;return n.make(isFunction(t)&&!t.name?t():t,{minimumDigits:this.minimumDigits,format:function format(t){return i.format(e,t)}})}},{key:"dataType",get:function get(){return this.defaultDataType()}},{key:"value",get:function get(){return this.$value},set:function set(e){e instanceof n||(e=this.createFaceValue(e)),this.$value=e}},{key:"stopAt",get:function get(){return this.$stopAt},set:function set(e){this.$stopAt=e}},{key:"originalValue",get:function get(){return this.$originalValue},set:function set(e){this.$originalValue=e}}]),Face}(e),s={years:"سنوات",months:"شهور",days:"أيام",hours:"ساعات",minutes:"دقائق",seconds:"ثواني"},a=["ar","ar-ar","arabic"],o=Object.freeze({dictionary:s,aliases:a}),u={years:"Anys",months:"Mesos",days:"Dies",hours:"Hores",minutes:"Minuts",seconds:"Segons"},c=["ca","ca-es","catalan"],l=Object.freeze({dictionary:u,aliases:c}),h={years:"Roky",months:"Měsíce",days:"Dny",hours:"Hodiny",minutes:"Minuty",seconds:"Sekundy"},f=["cs","cs-cz","cz","cz-cs","czech"],d=Object.freeze({dictionary:h,aliases:f}),m={years:"År",months:"Måneder",days:"Dage",hours:"Timer",minutes:"Minutter",seconds:"Sekunder"},y=["da","da-dk","danish"],g=Object.freeze({dictionary:m,aliases:y}),p={years:"Jahre",months:"Monate",days:"Tage",hours:"Stunden",minutes:"Minuten",seconds:"Sekunden"},v=["de","de-de","german"],k=Object.freeze({dictionary:p,aliases:v}),b={years:"Years",months:"Months",days:"Days",hours:"Hours",minutes:"Minutes",seconds:"Seconds"},C=["en","en-us","english"],_=Object.freeze({dictionary:b,aliases:C}),w={years:"Años",months:"Meses",days:"Días",hours:"Horas",minutes:"Minutos",seconds:"Segundos"},N=["es","es-es","spanish"],D=Object.freeze({dictionary:w,aliases:N}),O={years:"سال",months:"ماه",days:"روز",hours:"ساعت",minutes:"دقیقه",seconds:"ثانیه"},L=["fa","fa-ir","persian"],T=Object.freeze({dictionary:O,aliases:L}),S={years:"Vuotta",months:"Kuukautta",days:"Päivää",hours:"Tuntia",minutes:"Minuuttia",seconds:"Sekuntia"},j=["fi","fi-fi","finnish"],M=Object.freeze({dictionary:S,aliases:j}),$={years:"Ans",months:"Mois",days:"Jours",hours:"Heures",minutes:"Minutes",seconds:"Secondes"},A=["fr","fr-ca","french"],F=Object.freeze({dictionary:$,aliases:A}),H={years:"שנים",months:"חודש",days:"ימים",hours:"שעות",minutes:"דקות",seconds:"שניות"},P=["il","he-il","hebrew"],V=Object.freeze({dictionary:H,aliases:P}),z={years:"Év",months:"Hónap",days:"Nap",hours:"Óra",minutes:"Perc",seconds:"Másodperc"},R=["hu","hu-hu","hungarian"],E=Object.freeze({dictionary:z,aliases:R}),G={years:"Anni",months:"Mesi",days:"Giorni",hours:"Ore",minutes:"Minuti",seconds:"Secondi"},W=["da","da-dk","danish"],I=Object.freeze({dictionary:G,aliases:W}),Y={years:"年",months:"月",days:"日",hours:"時",minutes:"分",seconds:"秒"},x=["jp","ja-jp","japanese"],U=Object.freeze({dictionary:Y,aliases:x}),Z={years:"년",months:"월",days:"일",hours:"시",minutes:"분",seconds:"초"},B=["ko","ko-kr","korean"],q=Object.freeze({dictionary:Z,aliases:B}),J={years:"Gadi",months:"Mēneši",days:"Dienas",hours:"Stundas",minutes:"Minūtes",seconds:"Sekundes"},K=["lv","lv-lv","latvian"],Q=Object.freeze({dictionary:J,aliases:K}),X={years:"Jaren",months:"Maanden",days:"Dagen",hours:"Uren",minutes:"Minuten",seconds:"Seconden"},ee=["nl","nl-be","dutch"],te=Object.freeze({dictionary:X,aliases:ee}),ne={years:"År",months:"Måneder",days:"Dager",hours:"Timer",minutes:"Minutter",seconds:"Sekunder"},ie=["no","nb","no-nb","norwegian"],re=Object.freeze({dictionary:ne,aliases:ie}),se={years:"Lat",months:"Miesięcy",days:"Dni",hours:"Godziny",minutes:"Minuty",seconds:"Sekundy"},ae=["pl","pl-pl","polish"],oe=Object.freeze({dictionary:se,aliases:ae}),ue={years:"Anos",months:"Meses",days:"Dias",hours:"Horas",minutes:"Minutos",seconds:"Segundos"},ce=["pt","pt-br","portuguese"],le=Object.freeze({dictionary:ue,aliases:ce}),he={years:"Ani",months:"Luni",days:"Zile",hours:"Ore",minutes:"Minute",seconds:"sSecunde"},fe=["ro","ro-ro","romana"],de=Object.freeze({dictionary:he,aliases:fe}),me={years:"лет",months:"месяцев",days:"дней",hours:"часов",minutes:"минут",seconds:"секунд"},ye=["ru","ru-ru","russian"],ge=Object.freeze({dictionary:me,aliases:ye}),pe={years:"Roky",months:"Mesiace",days:"Dni",hours:"Hodiny",minutes:"Minúty",seconds:"Sekundy"},ve=["sk","sk-sk","slovak"],ke=Object.freeze({dictionary:pe,aliases:ve}),be={years:"År",months:"Månader",days:"Dagar",hours:"Timmar",minutes:"Minuter",seconds:"Sekunder"},Ce=["sv","sv-se","swedish"],_e=Object.freeze({dictionary:be,aliases:Ce}),we={years:"ปี",months:"เดือน",days:"วัน",hours:"ชั่วโมง",minutes:"นาที",seconds:"วินาที"},Ne=["th","th-th","thai"],De=Object.freeze({dictionary:we,aliases:Ne}),Oe={years:"Yıl",months:"Ay",days:"Gün",hours:"Saat",minutes:"Dakika",seconds:"Saniye"},Le=["tr","tr-tr","turkish"],Te=Object.freeze({dictionary:Oe,aliases:Le}),Se={years:"роки",months:"місяці",days:"дні",hours:"години",minutes:"хвилини",seconds:"секунди"},je=["ua","ua-ua","ukraine"],Me=Object.freeze({dictionary:Se,aliases:je}),$e={years:"Năm",months:"Tháng",days:"Ngày",hours:"Giờ",minutes:"Phút",seconds:"Giây"},Ae=["vn","vn-vn","vietnamese"],Fe=Object.freeze({dictionary:$e,aliases:Ae}),He={years:"年",months:"月",days:"日",hours:"时",minutes:"分",seconds:"秒"},Pe=["zh","zh-cn","chinese"],Ve=Object.freeze({dictionary:He,aliases:Pe}),ze={years:"年",months:"月",days:"日",hours:"時",minutes:"分",seconds:"秒"},Re=["zh-tw"],Ee=Object.freeze({dictionary:ze,aliases:Re}),Ge=Object.freeze({Arabic:o,Catalan:l,Czech:d,Danish:g,German:k,English:_,Spanish:D,Persian:T,Finnish:M,French:F,Hebrew:V,Hungarian:E,Italian:I,Japanese:U,Korean:q,Latvian:Q,Dutch:te,Norwegian:re,Polish:oe,Portuguese:le,Romanian:de,Russian:ge,Slovak:ke,Swedish:_e,Thai:De,Turkish:Te,Ukrainian:Me,Vietnamese:Fe,Chinese:Ve,TraditionalChinese:Ee}),We=function(e){function DomComponent(e){var t;if(_classCallCheck(this,DomComponent),(t=_possibleConstructorReturn(this,_getPrototypeOf(DomComponent).call(this,Object.assign({parent:null},e)))).theme||error("".concat(t.name," does not have a theme defined.")),t.language||error("".concat(t.name," does not have a language defined.")),!t.theme[t.name])throw new Error("".concat(t.name," cannot be rendered because it has no template."));return t}return _inherits(DomComponent,e),_createClass(DomComponent,[{key:"translate",value:function translate$$1(e){return translate(e,this.language)}},{key:"t",value:function t(e){return this.translate(e)}},{key:"render",value:function render(){var e=createElement("div",{class:"flip-clock"===this.className?this.className:"flip-clock-"+this.className});return this.theme[this.name](e,this),this.el?this.el.innerHTML!==e.innerHTML&&(this.el=swap(e,this.el)):this.el=e,this.el}},{key:"mount",value:function mount(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.render(),this.parent=e,t?this.parent.insertBefore(this.el,t):this.parent.appendChild(this.el),this.el}},{key:"className",get:function get(){return kebabCase(this.constructor.defineName())}},{key:"el",get:function get(){return this.$el},set:function set(e){validate(e,null,HTMLElement)||error(i.element),this.$el=e}},{key:"parent",get:function get(){return this.$parent},set:function set(e){this.$parent=e}},{key:"theme",get:function get(){return this.$theme},set:function set(e){validate(e,"object")||error(i.value),this.$theme=e}},{key:"language",get:function get(){return this.$language},set:function set(e){isString(e)&&(e=language(e)),validate(e,"object")||error(i.language),this.$language=e}}]),DomComponent}(e),Ie=function(e){function Divider(){return _classCallCheck(this,Divider),_possibleConstructorReturn(this,_getPrototypeOf(Divider).apply(this,arguments))}return _inherits(Divider,e),_createClass(Divider,null,[{key:"defineName",value:function defineName(){return"Divider"}}]),Divider}(We),Ye=function(e){function ListItem(e,t){return _classCallCheck(this,ListItem),_possibleConstructorReturn(this,_getPrototypeOf(ListItem).call(this,Object.assign({value:e},isObject(e)?e:null,t)))}return _inherits(ListItem,e),_createClass(ListItem,null,[{key:"defineName",value:function defineName(){return"ListItem"}}]),ListItem}(We),xe=function(e){function List(e,t){return _classCallCheck(this,List),_possibleConstructorReturn(this,_getPrototypeOf(List).call(this,Object.assign({value:e,items:[]},isObject(e)?e:null,t)))}return _inherits(List,e),_createClass(List,[{key:"createListItem",value:function createListItem(e,t){var n=new Ye(e,Object.assign({theme:this.theme,language:this.language},t));return this.$items.push(n),n}},{key:"value",get:function get(){return this.$value},set:function set(e){this.$value=e}},{key:"items",get:function get(){return this.$items},set:function set(e){this.$items=e}}],[{key:"defineName",value:function defineName(){return"List"}}]),List}(We),Ue=function(e){function Group(e,t){return _classCallCheck(this,Group),_possibleConstructorReturn(this,_getPrototypeOf(Group).call(this,Object.assign({items:isArray(e)?e:[]},isObject(e)?e:null,t)))}return _inherits(Group,e),_createClass(Group,null,[{key:"defineName",value:function defineName(){return"Group"}}]),Group}(We),Ze=function(e){function Label(e,t){return _classCallCheck(this,Label),_possibleConstructorReturn(this,_getPrototypeOf(Label).call(this,Object.assign({label:e},isObject(e)?e:null,t)))}return _inherits(Label,e),_createClass(Label,null,[{key:"defineName",value:function defineName(){return"Label"}}]),Label}(We),Be=function(e){function Timer(e){return _classCallCheck(this,Timer),_possibleConstructorReturn(this,_getPrototypeOf(Timer).call(this,Object.assign({count:0,handle:null,started:null,running:!1,interval:isNumber(e)?e:null},isObject(e)?e:null)))}return _inherits(Timer,e),_createClass(Timer,[{key:"reset",value:function reset(e){var t=this;return this.stop(function(){t.count=0,t.start(function(){return callback.call(t,e)}),t.emit("reset")}),this}},{key:"start",value:function start(e){var t=this;this.started=new Date,this.lastLoop=Date.now(),this.running=!0,this.emit("start");return function loop(){return Date.now()-t.lastLoop>=t.interval&&(callback.call(t,e),t.lastLoop=Date.now(),t.emit("interval"),t.count++),t.handle=window.requestAnimationFrame(loop),t}()}},{key:"stop",value:function stop(e){var t=this;return this.isRunning&&setTimeout(function(){window.cancelAnimationFrame(t.handle),t.running=!1,callback.call(t,e),t.emit("stop")}),this}},{key:"elapsed",get:function get(){return this.lastLoop?this.lastLoop-(this.started?this.started.getTime():(new Date).getTime()):0}},{key:"isRunning",get:function get(){return!0===this.running}},{key:"isStopped",get:function get(){return!1===this.running}}],[{key:"defineName",value:function defineName(){return"Timer"}}]),Timer}(e),qe=function(e){function Counter(){return _classCallCheck(this,Counter),_possibleConstructorReturn(this,_getPrototypeOf(Counter).apply(this,arguments))}return _inherits(Counter,e),_createClass(Counter,[{key:"increment",value:function increment(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;e.value=this.value.value+t}},{key:"decrement",value:function decrement(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;e.value=this.value.value-t}}],[{key:"defineName",value:function defineName(){return"Counter"}}]),Counter}(r),Je=function(e){function MinuteCounter(){return _classCallCheck(this,MinuteCounter),_possibleConstructorReturn(this,_getPrototypeOf(MinuteCounter).apply(this,arguments))}return _inherits(MinuteCounter,e),_createClass(MinuteCounter,[{key:"defaultDataType",value:function defaultDataType(){return Date}},{key:"defaultAttributes",value:function defaultAttributes(){return{showSeconds:!0,showLabels:!0}}},{key:"shouldStop",value:function shouldStop(e){if(isNull(e.stopAt)||isUndefined(e.stopAt))return!1;if(this.stopAt instanceof Date)return this.countdown?this.stopAt.getTime()>=this.value.value.getTime():this.stopAt.getTime()<=this.value.value.getTime();if(isNumber(this.stopAt)){var t=Math.floor((this.value.value.getTime()-this.originalValue.getTime())/1e3);return this.countdown?this.stopAt>=t:this.stopAt<=t}throw new Error("the stopAt property must be an instance of Date or Number.")}},{key:"increment",value:function increment(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()+t+((new Date).getTime()-e.timer.lastLoop))}},{key:"decrement",value:function decrement(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()-t-((new Date).getTime()-e.timer.lastLoop))}},{key:"format",value:function format(e,t){var n=e.timer.isRunning?e.timer.started:new Date(Date.now()-50);return[[this.getMinutes(t,n)],this.showSeconds?[this.getSeconds(t,n)]:null].filter(noop)}},{key:"getMinutes",value:function getMinutes(e,t){return round(this.getTotalSeconds(e,t)/60)}},{key:"getSeconds",value:function getSeconds(e,t){var n=this.getTotalSeconds(e,t);return Math.abs(Math.ceil(60===n?0:n%60))}},{key:"getTotalSeconds",value:function getTotalSeconds(e,t){return e.getTime()===t.getTime()?0:Math.round((e.getTime()-t.getTime())/1e3)}}],[{key:"defineName",value:function defineName(){return"MinuteCounter"}}]),MinuteCounter}(r),Ke=function(e){function HourCounter(){return _classCallCheck(this,HourCounter),_possibleConstructorReturn(this,_getPrototypeOf(HourCounter).apply(this,arguments))}return _inherits(HourCounter,e),_createClass(HourCounter,[{key:"format",value:function format(e,t){var n=e.timer.started?t:new Date,i=e.originalValue||t,r=this.countdown?i:n,s=this.countdown?n:i,a=[[this.getHours(r,s)],[this.getMinutes(r,s)]];return this.showSeconds&&a.push([this.getSeconds(r,s)]),a}},{key:"getMinutes",value:function getMinutes(e,t){return Math.abs(_get(_getPrototypeOf(HourCounter.prototype),"getMinutes",this).call(this,e,t)%60)}},{key:"getHours",value:function getHours(e,t){return Math.floor(this.getTotalSeconds(e,t)/60/60)}}],[{key:"defineName",value:function defineName(){return"HourCounter"}}]),HourCounter}(Je),Qe=function(e){function DayCounter(){return _classCallCheck(this,DayCounter),_possibleConstructorReturn(this,_getPrototypeOf(DayCounter).apply(this,arguments))}return _inherits(DayCounter,e),_createClass(DayCounter,[{key:"format",value:function format(e,t){var n=e.started?t:new Date,i=e.originalValue||t,r=this.countdown?i:n,s=this.countdown?n:i,a=[[this.getDays(r,s)],[this.getHours(r,s)],[this.getMinutes(r,s)]];return this.showSeconds&&a.push([this.getSeconds(r,s)]),a}},{key:"getDays",value:function getDays(e,t){return Math.floor(this.getTotalSeconds(e,t)/60/60/24)}},{key:"getHours",value:function getHours(e,t){return Math.abs(_get(_getPrototypeOf(DayCounter.prototype),"getHours",this).call(this,e,t)%24)}}],[{key:"defineName",value:function defineName(){return"DayCounter"}}]),DayCounter}(Ke),Xe=function(e){function TwentyFourHourClock(){return _classCallCheck(this,TwentyFourHourClock),_possibleConstructorReturn(this,_getPrototypeOf(TwentyFourHourClock).apply(this,arguments))}return _inherits(TwentyFourHourClock,e),_createClass(TwentyFourHourClock,[{key:"defaultDataType",value:function defaultDataType(){return Date}},{key:"defaultValue",value:function defaultValue(){return new Date}},{key:"defaultAttributes",value:function defaultAttributes(){return{showSeconds:!0,showLabels:!1}}},{key:"format",value:function format(e,t){t||(t=new Date);var n=[[t.getHours()],[t.getMinutes()]];return this.showSeconds&&n.push([t.getSeconds()]),n}},{key:"increment",value:function increment(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()+t+((new Date).getTime()-e.timer.lastLoop))}},{key:"decrement",value:function decrement(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()-t-((new Date).getTime()-e.timer.lastLoop))}}],[{key:"defineName",value:function defineName(){return"TwentyFourHourClock"}}]),TwentyFourHourClock}(r),et=function(e){function TwelveHourClock(){return _classCallCheck(this,TwelveHourClock),_possibleConstructorReturn(this,_getPrototypeOf(TwelveHourClock).apply(this,arguments))}return _inherits(TwelveHourClock,e),_createClass(TwelveHourClock,[{key:"defaultAttributes",value:function defaultAttributes(){return{showLabels:!1,showSeconds:!0,showMeridium:!0}}},{key:"format",value:function format(e,t){t||(t=new Date);var n=t.getHours(),i=[n>12?n-12:0===n?12:n,t.getMinutes()];return this.meridium=n>12?"pm":"am",this.showSeconds&&i.push(t.getSeconds()),i}}],[{key:"defineName",value:function defineName(){return"TwelveHourClock"}}]),TwelveHourClock}(Xe),tt=function(e){function WeekCounter(){return _classCallCheck(this,WeekCounter),_possibleConstructorReturn(this,_getPrototypeOf(WeekCounter).apply(this,arguments))}return _inherits(WeekCounter,e),_createClass(WeekCounter,[{key:"format",value:function format(e,t){var n=e.timer.started?t:new Date,i=e.originalValue||t,r=this.countdown?i:n,s=this.countdown?n:i,a=[[this.getWeeks(r,s)],[this.getDays(r,s)],[this.getHours(r,s)],[this.getMinutes(r,s)]];return this.showSeconds&&a.push([this.getSeconds(r,s)]),a}},{key:"getWeeks",value:function getWeeks(e,t){return Math.floor(this.getTotalSeconds(e,t)/60/60/24/7)}},{key:"getDays",value:function getDays(e,t){return Math.abs(_get(_getPrototypeOf(WeekCounter.prototype),"getDays",this).call(this,e,t)%7)}}],[{key:"defineName",value:function defineName(){return"WeekCounter"}}]),WeekCounter}(Qe),nt=function(e){function YearCounter(){return _classCallCheck(this,YearCounter),_possibleConstructorReturn(this,_getPrototypeOf(YearCounter).apply(this,arguments))}return _inherits(YearCounter,e),_createClass(YearCounter,[{key:"format",value:function format(e,t){var n=e.timer.started?t:new Date,i=e.originalValue||t,r=this.countdown?i:n,s=this.countdown?n:i,a=[[this.getYears(r,s)],[this.getWeeks(r,s)],[this.getDays(r,s)],[this.getHours(r,s)],[this.getMinutes(r,s)]];return this.showSeconds&&a.push([this.getSeconds(r,s)]),a}},{key:"getYears",value:function getYears(e,t){return Math.floor(Math.max(0,this.getTotalSeconds(e,t)/60/60/24/7/52))}},{key:"getWeeks",value:function getWeeks(e,t){return Math.abs(_get(_getPrototypeOf(YearCounter.prototype),"getWeeks",this).call(this,e,t)%52)}}],[{key:"defineName",value:function defineName(){return"YearCounter"}}]),YearCounter}(tt),it=Object.freeze({Counter:qe,DayCounter:Qe,MinuteCounter:Je,HourCounter:Ke,TwelveHourClock:et,TwentyFourHourClock:Xe,WeekCounter:tt,YearCounter:nt}),rt={face:qe,theme:{Divider:Divider$1,FlipClock:FlipClock,Group:Group$1,Label:Label$1,List:List$1,ListItem:ListItem$1,faces:Object.freeze({DayCounter:DayCounter$1,HourCounter:HourCounter$1,MinuteCounter:MinuteCounter$1,TwelveHourClock:TwelveHourClock$1,TwentyFourHourClock:TwentyFourHourClock$1,WeekCounter:WeekCounter$1,YearCounter:YearCounter$1})},language:_};return function(e){function FlipClock(e,t,n){var r;_classCallCheck(this,FlipClock),validate(e,HTMLElement)||error(i.element),isObject(t)&&!n&&(n=t,t=void 0);var s=n.face||rt.face;return delete n.face,(r=_possibleConstructorReturn(this,_getPrototypeOf(FlipClock).call(this,Object.assign({originalValue:t,theme:rt.theme,language:rt.language,timer:Be.make(n.interval||1e3)},n)))).face||(r.face=s),r.mount(e),r}return _inherits(FlipClock,e),_createClass(FlipClock,[{key:"mount",value:function mount(e){return _get(_getPrototypeOf(FlipClock.prototype),"mount",this).call(this,e),this.face.mounted(this),this}},{key:"render",value:function render(){return _get(_getPrototypeOf(FlipClock.prototype),"render",this).call(this),this.theme.faces[this.face.name]&&this.theme.faces[this.face.name](this.el,this),this.face.rendered(this),this.el}},{key:"start",value:function start(e){var t=this;return this.timer.started||(this.value=this.originalValue),isUndefined(this.face.stopAt)&&(this.face.stopAt=this.stopAt),isUndefined(this.face.originalValue)&&(this.face.originalValue=this.originalValue),this.timer.start(function(){t.face.interval(t,e)}),this.face.started(this),this.emit("start")}},{key:"stop",value:function stop(e){return this.timer.stop(e),this.face.stopped(this),this.emit("stop")}},{key:"reset",value:function reset(e){var t=this;return this.value=this.originalValue,this.timer.reset(function(){return t.interval(t,e)}),this.face.reset(this),this.emit("reset")}},{key:"increment",value:function increment(e){return this.face.increment(this,e),this}},{key:"decrement",value:function decrement(e){return this.face.decrement(this,e),this}},{key:"createDivider",value:function createDivider(e){return Ie.make(Object.assign({theme:this.theme,language:this.language},e))}},{key:"createList",value:function createList(e,t){return xe.make(e,Object.assign({theme:this.theme,language:this.language},t))}},{key:"createLabel",value:function createLabel(e,t){return Ze.make(e,Object.assign({theme:this.theme,language:this.language},t))}},{key:"createGroup",value:function createGroup(e,t){return Ue.make(e,Object.assign({theme:this.theme,language:this.language},t))}},{key:"face",get:function get$$1(){return this.$face},set:function set(e){validate(e,[r,"string","function"])||error(i.face),this.$face=(it[e]||e).make(Object.assign(this.getPublicAttributes(),{originalValue:this.face?this.face.originalValue:void 0})),this.$face.initialized(this),this.value?this.$face.value=this.face.createFaceValue(this,this.value.value):this.value||(this.value=this.originalValue),this.el&&this.render()}},{key:"stopAt",get:function get$$1(){return isFunction(this.$stopAt)?this.$stopAt(this):this.$stopAt},set:function set(e){this.$stopAt=e}},{key:"timer",get:function get$$1(){return this.$timer},set:function set(e){validate(e,Be)||error(i.timer),this.$timer=e}},{key:"value",get:function get$$1(){return this.face?this.face.value:null},set:function set(e){if(!this.face)throw new Error("A face must be set before setting a value.");e instanceof n?this.face.value=e:this.value?this.face.value=this.face.value.clone(e):this.face.value=this.face.createFaceValue(this,e),this.el&&this.render()}},{key:"originalValue",get:function get$$1(){return isFunction(this.$originalValue)&&!this.$originalValue.name?this.$originalValue():isUndefined(this.$originalValue)||isNull(this.$originalValue)?this.face?this.face.defaultValue():void 0:this.$originalValue},set:function set(e){this.$originalValue=e}}],[{key:"defineName",value:function defineName(){return"FlipClock"}},{key:"setDefaultFace",value:function setDefaultFace(e){validate(e,r)||error(i.face),rt.face=e}},{key:"setDefaultTheme",value:function setDefaultTheme(e){validate(e,"object")||error(i.theme),rt.theme=e}},{key:"setDefaultLanguage",value:function setDefaultLanguage(e){validate(e,"object")||error(i.language),rt.language=e}},{key:"defaults",get:function get$$1(){return rt}}]),FlipClock}(We)});
//# sourceMappingURL=flipclock.min.js.map
