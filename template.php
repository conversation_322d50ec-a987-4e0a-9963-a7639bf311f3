<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/constant.php");
	include("inc/clsObj.php");
	checkLogin();
	include("chkpermissions.php");	
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- HTML5 Shim and Respond.js IE11 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 11]>
    	<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    	<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    	<![endif]-->
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
	
    <!-- Favicon icon -->
    <!-- <link rel="icon" href="assets/images/logo/favicon.png" type="image/x-icon"> -->
	
	<!-- data tables css -->
    <link rel="stylesheet" href="assets/css/plugins/dataTables.bootstrap4.min.css">

    <!-- vendor css -->
    <link rel="stylesheet" href="assets/css/style.css">
	
	<!-- custom css -->
    <link rel="stylesheet" href="assets/css/custom.css">
	
	<!-- select2 css -->
    <link rel="stylesheet" href="assets/css/plugins/select2.min.css">
	
	<!-- Date & Time Script -->
    
	<script type="text/javascript">
    function Logout()
    {
        if(confirm("Are you sure want to logout now?\n\nClick [ OK ] to logout now.\nClick [ Cancel ] to continue with current login."))
        {
            document.location="codeAdminLogout.php";
        }
    }
    
    function confdelimg(imgId,imgName){
        var conf = confirm("Are you sure want to delete this image ?");
        
        if(conf){
            MakeRequest(imgId,imgName);
        }else{
            return false;
        }
    }
    
    function selCompanyYear()
    {
        ///var company = document.getElementById("txtCompany").value;
        var fyear = document.getElementById("txtFinancialYear").value;
        document.location="changeAccountDetails.php?fyear="+fyear;
        //document.location="changeAccountDetails.php?fyear="+fyear+"&company="+company;
    }
    </script>
	<script type="text/javascript">
    /***********************************************
    * Local Time script- © Dynamic Drive (http://www.dynamicdrive.com)
    * This notice MUST stay intact for legal use
    * Visit http://www.dynamicdrive.com/ for this script and 100s more.
    ***********************************************/
    
    var weekdaystxt=["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    
    var monthNames = [ "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December" ];
    
    function showLocalTime(container, servermode, offsetMinutes, displayversion){
        if (!document.getElementById || !document.getElementById(container)) return
        this.container=document.getElementById(container)
        this.displayversion=displayversion
        var servertimestring=(servermode=="server-php")? '<? print date("F d, Y H:i:s", time())?>' : (servermode=="server-ssi")? '<!--#config timefmt="%B %d, %Y %H:%M:%S"--><!--#echo var="DATE_LOCAL" -->' : '<%= Now() %>'
        this.localtime=this.serverdate=new Date(servertimestring)
        this.localtime.setTime(this.serverdate.getTime()+offsetMinutes*60*1000) //add user offset to server time
        this.updateTime()
        this.updateContainer()
    }
    
    showLocalTime.prototype.updateTime=function(){
        var thisobj=this
        this.localtime.setSeconds(this.localtime.getSeconds()+1)
        setTimeout(function(){thisobj.updateTime()}, 1000) //update time every second
    }
    
    showLocalTime.prototype.updateContainer=function(){
        var thisobj=this
        if (this.displayversion=="long")
        this.container.innerHTML=this.localtime.toLocaleString()
        else{
        var hour=this.localtime.getHours()
        var minutes=this.localtime.getMinutes()
        var seconds=this.localtime.getSeconds()
        var ampm=(hour>=12)? "PM" : "AM"
        var dayofweek=weekdaystxt[this.localtime.getDay()]
        this.container.innerHTML=formatField(hour, 1)+":"+formatField(minutes)+" "+ampm+" - "+dayofweek+", "+monthNames[this.localtime.getMonth()]+ " "+this.localtime.getDate()+", "+this.localtime.getFullYear()
    }
    setTimeout(function(){thisobj.updateContainer()}, 1000) //update container every second
    }
    
    function formatField(num, isHour){
    if (typeof isHour!="undefined"){ //if this is the hour field
    var hour=(num>12)? num-12 : num
    return (hour==0)? 12 : hour
    }
    return (num<=9)? "0"+num : num//if this is minute or sec field
    }
    </script>
<script type="text/javascript" src="js/validator.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script language="javascript" src="js/ajax.js"></script>
<script language="javascript" src="js/getdetails.js"></script>
<script language="javascript" src="js/add_more.js"></script>

<? // required for exce generate from table ?>
<?php /*?><script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script><?php */?>
<script type="text/javascript" src="js/jquery-1.8.2.min.js"></script>
</head>
<body class="">
	
	<!--========== Preloader ==========-->
	<!--<div class="loader-bg">
		<div class="loader-track">
			<div class="loader-fill"></div>
		</div>
	</div>-->
	<!--========== Preloader ==========-->
	
	<!--========== Include sidemenu.php ==========-->
	<? include("sidemenu.php") ?>
	<!--========== Include sidemenu.php ==========-->
	
	<!--========== Include topheader.php ==========-->
	<? include("topheader.php") ?>
	<!--========== Include topheader.php ==========-->

	<? main(); ?>

    <!-- Required Js -->
    <script src="assets/js/vendor-all.min.js"></script>
    <script src="assets/js/plugins/bootstrap.min.js"></script>
   <?php /*?> <script src="assets/js/ripple.js"></script><?php */?>
    <script src="assets/js/pcoded.min.js"></script>
	<script src="assets/js/menu-setting.min.js"></script>
	
	<? if(stristr($_SERVER['PHP_SELF'],"codeManageBillGST.php")!="" && (isset($_REQUEST['btnAddUser']) || isset($_REQUEST['id'])))
	{ ?>
    <!-- Ckeditor js -->
	<script src="assets/js/plugins/ckeditor.js"></script>
	<script type="text/javascript">
		$(window).on('load', function() {
			ClassicEditor.create(document.querySelector('#classic-editor'))
			ClassicEditor.create(document.querySelector('#classic-editor1'))
			ClassicEditor.create(document.querySelector('#classic-editor2'))
			ClassicEditor.create(document.querySelector('#classic-editor3'))
			ClassicEditor.create(document.querySelector('#classic-editor4'))
			ClassicEditor.create(document.querySelector('#classic-editor5'))
			ClassicEditor.create(document.querySelector('#classic-editor6'))
				.catch(error => {
					console.error(error);
				});
		});
	</script>
	<?php } ?>
	
	<!-- datatable Js -->
	<script src="assets/js/plugins/jquery.dataTables.min.js"></script>
	<script src="assets/js/plugins/dataTables.bootstrap4.min.js"></script>
	<script src="assets/js/pages/data-basic-custom.js"></script>
	
	<!-- select2 Js -->
	<script src="assets/js/plugins/select2.full.min.js"></script>
	
	<!-- form-select-custom Js -->
	<script src="assets/js/pages/form-select-custom.js"></script>
	
	<script src="assets/js/horizontal-menu.js"></script>
    <script>
        $(document).ready(function() {
            $("#pcoded").pcodedmenu({
                themelayout: 'horizontal',
                MenuTrigger: 'hover',
                SubMenuTrigger: 'hover',
            });
        });
    </script>

	<!-- Apex Chart -->
	<?php /*?><script src="assets/js/plugins/apexcharts.min.js"></script><?php */?>
	
	<!-- custom-chart js -->
	<?php /*?><script src="assets/js/pages/dashboard-main.js"></script>
	<script>
		$(document).ready(function() {
			checkCookie();
		});

		function setCookie(cname, cvalue, exdays) {
			var d = new Date();
			d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
			var expires = "expires=" + d.toGMTString();
			document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
		}

		function getCookie(cname) {
			var name = cname + "=";
			var decodedCookie = decodeURIComponent(document.cookie);
			var ca = decodedCookie.split(';');
			for (var i = 0; i < ca.length; i++) {
				var c = ca[i];
				while (c.charAt(0) == ' ') {
					c = c.substring(1);
				}
				if (c.indexOf(name) == 0) {
					return c.substring(name.length, c.length);
				}
			}
			return "";
		}

		function checkCookie() {
			var ticks = getCookie("modelopen");
			if (ticks != "") {
				ticks++ ;
				setCookie("modelopen", ticks, 1);
				if (ticks == "2" || ticks == "1" || ticks == "0") {
					$('#exampleModalCenter').modal();
				}
			} else {
				// user = prompt("Please enter your name:", "");
				$('#exampleModalCenter').modal();
				ticks = 1;
				setCookie("modelopen", ticks, 1);
			}
		}
	</script><?php */?>

<!--===== Date Picker =====-->
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<link rel="stylesheet" href="assets/css/jquery-ui.css">
<?php /*?><link rel="stylesheet" href="/resources/demos/style.css"><?php */?>
<!--<script src="https://code.jquery.com/jquery-1.12.4.js"></script>-->
<?php /*?><script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script><?php */?>
<script src="js/jquery-ui-1.12.1.js"></script>
<script>
    $( function() {
    $( ".dd" ).datepicker({ dateFormat: 'd-mm-yy' });
	//$( "#datepicker" ).datepicker({minDate: 0});
    //$( "#datepicker1" ).datepicker({ dateFormat: 'd-mm-yy' , minDate: 0 });
    } );	
</script>
<!--===== Date Picker =====-->

</body>
</html>