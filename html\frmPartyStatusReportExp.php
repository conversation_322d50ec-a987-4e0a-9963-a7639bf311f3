<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Export</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Pending Payment</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form name="frmsearch" id="frmsearch" action="" method="get">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
									<label>Search Party Name :</label>
                                    <? $listParty=$objParty->selectStatusExp();?>
									<select name="Client_Name" id="Client_Name"  class="js-example-basic-single form-control col-sm-12">
										<option value="">--Select Client--</option>
										<? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($_GET['Client_Name']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
									</select>
								</div>
								<div class="form-group col-md-2">
									<label>&nbsp;</label><br>
									<button name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
									<a onclick="javascript:document.location='codePartyStatusReportExp.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
							<table id="simpletable" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th class="sorting_asc sorting_desc">Sr. No.</th>
										<th class="sorting_asc sorting_desc">Party</th>
										<th class="sorting_asc sorting_desc">Contact Person</th>
										<th class="sorting_asc sorting_desc">Contact No.</th>
										<th class="sorting_asc sorting_desc">Contact Email</th>
										<th class="sorting_asc sorting_desc">Pending Amount</th>
										<th class="sorting_asc sorting_desc">Pending Amount ₹</th>
									</tr>
								</thead>
								<tbody>
                                <?  $n=0;
   	$total_amt = 0;
    if(count($listRec)>0)
	 {
	 	$currency_symbol="";
      	$colorflg=0;
	   	for($e=0;$e<count($listRec);$e++)
		{
			$objBill->party=$listRec[$e]['id'];
			$bilDate=$objBill->selectSumByParty();
			if(count(bilDate) > 0)
				$currency_symbol = $objCrn->getCurrencySymbol($bilDate[0]['currency']);
			
				$objCash->party=$listRec[$e]['id'];
				$cashDate=$objCash->selectSumByParty();
				if(count(cashDate) > 0 && $currency_symbol=="")
					$currency_symbol = $objCrn->getCurrencySymbol($cashDate[0]['currency']);
			
				$objTds->party=$listRec[$e]['id'];
				$partyTds=$objTds->selectSumByParty();
				if(count(partyTds) > 0 && $currency_symbol=="")
					$currency_symbol = $objCrn->getCurrencySymbol($partyTds[0]['currency']);
				
				$final_amount = $bilDate[0]['totamt'] - ($cashDate[0]['credit_amount'] - $cashDate[0]['debit_amount'])-$partyTds[0]['credit_amount'];
			
				$final_amount_inr = $bilDate[0]['tot_inr'] - ($cashDate[0]['tot_credit'] - $cashDate[0]['tot_debit'])-$partyTds[0]['tot_credit'];
				if($final_amount > 0)
				{ 
					$n++;
					$total_amt+=$final_amount;
					$total_amt_inr+=$final_amount_inr;	?>
                    <tr>
                        <td><?=$n;?></td>
                        <td><?=$listRec[$e]['party_name']; ?></td>
                        <td><?=$listRec[$e]['contact_name1']; ?></td>
                        <td><?=$listRec[$e]['contact_no1']; ?></td>
                        <td><?=$listRec[$e]['email1']; ?></td>
                        <td class="text-right"><?=$currency_symbol;?> <?=($final_amount > 0) ? number_format($final_amount,2) : 0; ?></td>
                        <td class="text-right"><?=($final_amount_inr > 0) ? "<img src='images/inr_img.png' width='8' height='10'> ".number_format($final_amount_inr,2) : 0; ?></td>
                    </tr>
			<? }
			}	
		} ?>				
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="6" class="text-right">
                            <strong>Total Pending Amount</strong>
                        </td>
                        <td class="text-right"><strong><?=number_format($total_amt_inr,2);?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <!-- Table -->		
        <!--========== List View ==========-->
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>