<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();
?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}

td
{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>

<div style="width:100%;" id="print_header">
<? 
if($_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "";
		/* if($_GET['Party_Name']!="")
				$query.=" and party='".$_GET['Party_Name']."'"; */
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			//$query.=" and date_format(`bill_date`, '%d-%m-%Y') between '".$_GET['From_Date']."' and '".$_GET['To_Date']."'";
			$fd = explode("-",$_GET['From_Date']);
			$fdate = $fd[2]."-".$fd[1]."-".$fd[0];
			$td = explode("-",$_GET['To_Date']);
			$tdate = $td[2]."-".$td[1]."-".$td[0];
			//$query.=" and date_format(`bill_date`, '%d-%m-%Y') between '".$_GET['From_Date']."' and '".$_GET['To_Date']."'";
			$query.=" and `bill_date` between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year = '".$_SESSION['fyear']."'";
		
		// $objCash->party = $_GET['party'];
		$listRec=$objBill->selectServiceTax($query);
    }
	else
	{
	
		$query.=" and financial_year ='".$_SESSION['fyear']."'";
		// $listRec=$objCash->paging();
		$listRec=$objBill->selectServiceTax($query);
	}
?>
<div style="width:800px; margin:0 auto;">
<table width="100%" border="1" cellspacing="0" cellpadding="3" style="border-collapse:collapse; border:#CCCCCC 1px solid;">
<tr><td colspan="12" align="center">
	<strong><?=$_SESSION['company'];?></strong><br />
      304-A, Ashoka,<br/>
      Opp. Abhushan Complex,<br/>S.P. Stadium Road, Navrangpura,<br />
      Ahmedabad-380014<br />
    
      Ph: +91-79-40391397<br />
      E-Mail: 
      <a href="mailto:<EMAIL>"><EMAIL></a><br />
      Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>
      

</td></tr>
  <tr style="background:#ccc; font-size:12px;">
    <th width="4%">Sr. No. </th>
    <th width="16%">Party</th>
	<th width="19%">Invoice No.</th>
	<th width="15%">Invoice Date</th>
	<th width="12%" align="right">Total Amount</th>
    <th width="6%" align="right">Service Tax</th>
	<th width="3%" align="right">Swachh Bharat Cess</th>
	<th width="4%" align="right">Krishi Kalyan Cess</th>
	<th width="17%" align="right">Edu.Cess</th>
    <th width="1%" align="right">H.S.Edu. Cess</th>
    <th width="1%" align="right">Total Tax</th>
    <th width="2%" align="right">Total</th>
  </tr>

<?  $n=0;
	$total_amt = 0;
	$total_stax = 0;
	$total_eduamt = 0;
	$total_hseduamt = 0;
	$tamount = 0;
	$total_amount = 0;
	$total_tax = 0;
	$total_tax_amt = 0;
	
	if(count($listRec)>0)
	 {
		$colorflg=0;
		for($e=0;$e<count($listRec);$e++)
		{
			$n++;
			$total_amt+=$listRec[$e]['net_amount'];
			$total_stax+=$listRec[$e]['stax_amt'];
			$total_sbtax+=$listRec[$e]['sbtax_amt'];
			$total_kkctax+=$listRec[$e]['kkctax_amt'];
			$total_eduamt+=$listRec[$e]['edu_amt'];
			$total_hseduamt+=$listRec[$e]['hsedu_amt'];
			$total_tax = $listRec[$e]['stax_amt']+$listRec[$e]['sbtax_amt']+$listRec[$e]['kkctax_amt']+$listRec[$e]['edu_amt']+$listRec[$e]['hsedu_amt'];
			$total_tax_amt+=$total_tax;
			$tamount=$listRec[$e]['net_amount']+$listRec[$e]['stax_amt']+$listRec[$e]['sbtax_amt']+$listRec[$e]['kkctax_amt']+$listRec[$e]['edu_amt']+$listRec[$e]['hsedu_amt'];
			$total_amount+=$tamount;
			
			// $eduamt=$listRec[$e]['edu_amt']+$listRec[$e]['hsedu_amt'];
			// $total_eduamt+=$eduamt;

			if($colorflg==1){ $colorflg=0;?>
  		  <tr class="odd" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='odd'">
		   <? }	else {	$colorflg=1;?>
                 <tr class="even" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='even'">
           <? } ?>
                <td><?=$n;?></td>
                <td><? echo $objParty->getPartyName($listRec[$e]['party']); ?></td>
                <td><? echo $listRec[$e]['invoice_no']; ?></td>
                <td><? echo $listRec[$e]['bdt']; ?></td>
                <td align="right"><? echo number_format($listRec[$e]['net_amount'],2); ?></td>
                <td align="right"><? echo number_format($listRec[$e]['stax_amt'],2); ?></td>
                <td align="right"><? echo number_format($listRec[$e]['sbtax_amt'],2); ?></td>
                <td align="right"><? echo number_format($listRec[$e]['kkctax_amt'],2); ?></td>
                <td align="right"><? echo number_format($listRec[$e]['edu_amt'],2);?></td>
	            <td align="right"><? echo number_format($listRec[$e]['hsedu_amt'],2);?></td>
	            <td align="right"><? echo number_format($total_tax,2);?></td>
                <td align="right"><? echo number_format($tamount,2);?></td>
         </tr>
  <? } ?>
  <tr style="background:#333333; color:#FFFFFF; height:30px;">
  <td colspan="4" style="font-weight:bold;">Total Amount : </td>
    <td align="right" style="font-weight:bold;"><?=number_format($total_amt,2);?></td>
    <td align="right" style="font-weight:bold;"><?=number_format($total_stax,2);?></td>
    <td align="right" style="font-weight:bold;"><?=number_format($total_sbtax,2);?></td>
    <td align="right" style="font-weight:bold;"><?=number_format($total_kkctax,2);?></td>
    <td align="right" style="font-weight:bold;"><?=number_format($total_eduamt,2);?></td>
  <td align="right" style="font-weight:bold;"><?=number_format($total_hseduamt,2);?></td>
  <td align="right" style="font-weight:bold;"><? echo number_format($total_tax_amt,2);?></td>
  <td align="right" style="font-weight:bold;"><? echo number_format($total_amount,2);?></td>
  </tr> 
  <tr class="toprow">
            <td colspan="12" style="text-align:right; font-style:italic;">Report by : <?=$_SESSION['membername'];?></td>
        </tr>
  <?
  } else { ?>
  		<tr>
        	<td colspan="16" align="center">
            	No Records Found...</td>
		</tr>
    <?php }?>    
  </table>
</div>

</div>
<script language="javascript">
	window.print();
</script>
<? // } ?>