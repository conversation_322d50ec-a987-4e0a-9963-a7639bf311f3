<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();
//include("template.php");
//function main()
//{
    include("inc/clsObj.php");	
/*	$objBill->id=$_GET['id'];

	$listEdit=$objBill->selectRecById();
		
	$objBill->bid = $listEdit[0]['id'];
	$listItems=$objBill->selectItemsByBillNo();		*/
	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}

td
{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>

<div style="width:100%;" id="print_header">
<?  
if($_GET['Client_Name']!="")
	{
		$query.=" and id='".$_GET['Client_Name']."'";
		$listRec=$objParty->selectRecBySearch($query);
	}
else
	$listRec=$objParty->select(); ?>
<div style="width:800px; margin:0 auto;">
<table width="100%" border="1" cellspacing="0" cellpadding="3" style="border-collapse:collapse; border:#CCCCCC 1px solid;">
  <tr><td colspan="6" align="center">
<strong style="font-size:25px; font-weight:bold;"><?=$_SESSION['company'];?></strong><br />
      <?=$objCompany->getInvoiceHeaderByCompany($_SESSION['company']);?>
      </td></tr>
  
  <tr style="background:#666666; color:#FFFFFF;">
    <td width="5%"><strong>Sr. No. </strong></td>
    <td width="20%"><strong>Party</strong></td>
	<td width="30%"><strong>Address Details</strong></td>
	<td width="20%"><strong>Contact Details</strong></td>
	<td width="25%"><strong>Contact Person</strong></td>  
  </tr>
  
   <?  $n=0;
    if(count($listRec)>0)
	 {
      	$colorflg=0;
	   	for($e=0;$e<count($listRec);$e++)
		{
			$n++;
?>
		 <tr>
                <td><?=$n;?></td>
                <td>
                <a href="codePartyStatusReport.php?Party_Name=<? echo $listRec[$e]['id']; ?>" style="font-size:12px; text-decoration:none; color:#333333;"><? echo $listRec[$e]['party_name']; ?></a></td>
                <td><?=$listRec[$e]['party_address'];?><br/>
                	<?=$listRec[$e]['city'];?> - <?=$listRec[$e]['zipcode'];?><br/>
                    <?=$objState->getNameById($listRec[$e]['state']);?>, <?=$listRec[$e]['country'];?></td>
                <td><?=$listRec[$e]['office_no1'];?><br/>
                	<?=$listRec[$e]['email'];?><br/>
                     <?=$listRec[$e]['domain_name'];?><br/></td>
                <td><?=$listRec[$e]['contact_name1'];?><br/>
                     <?=$listRec[$e]['contact_no1'];?><br/>
                     <?=$listRec[$e]['email1'];?></td>
	     </tr>
  <? 
  } 
  } else { ?>
  		<tr>
        	<td colspan="11" align="center">
            	No Records Found...</td>
		</tr>
    <?php }?>    
  </table>
</div>
</div>
<script language="javascript">
	window.print();
</script>
<? // } ?>