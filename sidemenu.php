<nav class="pcoded-navbar theme-horizontal menu-light brand-blue">
	<div class="navbar-wrapper">
		<div class="navbar-content scroll-div">

			<?php /*?><div class="">
				<div class="main-menu-header">
					<img class="img-radius" src="assets/images/user/avatar-2.jpg" alt="User-Profile-Image">
					<div class="user-details">
						<div id="more-details"><?=$_SESSION['membername'];?><i class="fa fa-caret-down"></i></div>
					</div>
				</div>
				<div class="collapse" id="nav-user-link">
					<ul class="list-inline">
						<li class="list-inline-item">
							<a href="#" data-toggle="tooltip" title="View Profile">
								<i class="feather icon-user"></i>
							</a>
						</li>
						<li class="list-inline-item">
							<a href="#" data-toggle="tooltip" title="Logout" class="text-danger">
								<i class="feather icon-power"></i>
							</a>
						</li>
					</ul>
				</div>
			</div><?php */?>

			<ul class="nav pcoded-inner-navbar">
            	<li class="nav-item pcoded-menu-caption">
					<label>Navigation</label>
				</li>
				<li class="nav-item">
					<a href="dashboard.php" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-aperture"></i></span>
						<span class="pcoded-mtext">Dashboard</span>
					</a>
				</li>
				<li class="nav-item pcoded-hasmenu">
					<a href="#!" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-layout"></i></span>
						<span class="pcoded-mtext">Admin</span>
					</a>
					<ul class="pcoded-submenu">
                    	<? if(in_array($objAdminMenu->getIdByName("codeManageAdminUser.php"),$_SESSION['uper'])){ ?>
						<li><a href="codeManageAdminUser.php">Manage User</a></li><? } ?>
                        <? if(in_array($objAdminMenu->getIdByName("codeAdminChangePassword.php"),$_SESSION['uper'])){ ?>
						<li><a href="codeAdminChangePassword.php">Change Password</a></li><? } ?>
					</ul>
				</li>
                <? if(in_array($objAdminMenu->getIdByName("codeManageParty.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageItem.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageCompany.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageState.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageYear.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageCurrency.php"),$_SESSION['uper'])){ ?>
				<li class="nav-item">
					<a href="codeManageParty.php" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-layout"></i></span>
						<span class="pcoded-mtext">Customer Master</span>
					</a>
					<?php /* 
					<ul class="pcoded-submenu">
                    <? if(in_array($objAdminMenu->getIdByName("codeManageParty.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageParty.php">Customer Master</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageItem.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageItem.php">Particular Master</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageCompany.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageCompany.php">Company Master</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageState.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageState.php">State Master</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageYear.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageYear.php">Financial Year Master</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageCurrency.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageCurrency.php">Currency Master</a></li><? } ?>
					</ul> */ ?>
				</li>
                <? }
				/* if(in_array($objAdminMenu->getIdByName("codeManageBillGST.php"),$_SESSION['uper'])){ ?>
				<li class="nav-item">
					<a href="codeManageBillGST.php" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-aperture"></i></span>
						<span class="pcoded-mtext">Invoice</span>
					</a>
				</li>
                <? } */
				/* if(in_array($objAdminMenu->getIdByName("codeManageBillExp.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageCashbookExp.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageDeduction.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManagePartyReportExp.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codePartyStatusReportExp.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageCollectionExp.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeTdsReportExp.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeTdsPartyReportExp.php"),$_SESSION['uper'])){ ?>
				<li class="nav-item pcoded-hasmenu">
					<a href="#!" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-layout"></i></span>
						<span class="pcoded-mtext">Export</span>
					</a>
					<ul class="pcoded-submenu">
						<? if(in_array($objAdminMenu->getIdByName("codeManageBillExp.php"),$_SESSION['uper'])){ ?>
                        	<li><a href="codeManageBillExp.php">Export Invoice</a></li><? } ?>
                        <? if(in_array($objAdminMenu->getIdByName("codeManageCashbookExp.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageCashbookExp.php">Export Payment</a></li><? } ?>
                        <? if(in_array($objAdminMenu->getIdByName("codeManageDeduction.php"),$_SESSION['uper'])){ ?>    
							<li><a href="codeManageDeduction.php">Deduction</a></li><? } ?>
						<? if(in_array($objAdminMenu->getIdByName("codeManagePartyReportExp.php"),$_SESSION['uper'])){ ?>
                        	<li><a href="codeManagePartyReportExp.php">Partywise Ledger Report</a></li><? } ?>
                        <? if(in_array($objAdminMenu->getIdByName("codePartyStatusReportExp.php"),$_SESSION['uper'])){ ?>
						<li><a href="codePartyStatusReportExp.php">Pending Payment Report</a></li><? } ?>
                        <? if(in_array($objAdminMenu->getIdByName("codeManageCollectionExp.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageCollectionExp.php">Collection Report</a></li><? } ?>
                        <? if(in_array($objAdminMenu->getIdByName("codeTdsReportExp.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeTdsReportExp.php">Deduction Report</a></li><? } ?>
                       	<? if(in_array($objAdminMenu->getIdByName("codeTdsPartyReportExp.php"),$_SESSION['uper'])){ ?>
                        	<li><a href="codeTdsPartyReportExp.php">Partywise Deduction Report</a></li><? } ?>
					</ul>
				</li>
                <? } */
				/* if(in_array($objAdminMenu->getIdByName("codeManageCashbook.php"),$_SESSION['uper'])){ ?>
				<li class="nav-item">
					<a href="codeManageCashbook.php" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-aperture"></i></span>
						<span class="pcoded-mtext">Payment</span>
					</a>
				</li>
                <? } */
				/* if(in_array($objAdminMenu->getIdByName("codeManageTds.php"),$_SESSION['uper'])){ ?>
				<li class="nav-item">
					<a href="codeManageTds.php" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-aperture"></i></span>
						<span class="pcoded-mtext">TDS Entry</span>
					</a>
				</li>
                <? } */ ?>
                <? if(in_array($objAdminMenu->getIdByName("codePartyFollowup.php"),$_SESSION['uper'])){ ?>
				<!-- <li class="nav-item">
					<a href="codePartyFollowup.php" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-aperture"></i></span>
						<span class="pcoded-mtext">Inquiries</span>
					</a>
				</li> -->
                <? } 
				/* if(in_array($objAdminMenu->getIdByName("codeManageCollection.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codePartyStatusReport.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeInvoiceReport.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageClientPrintReport.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageClientPrintReport.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageClientReport.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManagePartyReportAll.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeManageCollectionTax.php"),$_SESSION['uper']) || in_array($objAdminMenu->getIdByName("codeTdsReport.php"),$_SESSION['uper'])){ ?>
				<li class="nav-item pcoded-hasmenu">
					<a href="#!" class="nav-link">
						<span class="pcoded-micon"><i class="feather icon-layout"></i></span>
						<span class="pcoded-mtext">Reports</span>
					</a>
					<ul class="pcoded-submenu">
                    	<? if(in_array($objAdminMenu->getIdByName("codeManageCollection.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageCollection.php">Collection Report</a></li><? } 
						if(in_array($objAdminMenu->getIdByName("codePartyStatusReport.php"),$_SESSION['uper'])){ ?>	
							<li><a href="codePartyStatusReport.php">Pending Payment Report</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeInvoiceReport.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeInvoiceReport.php">Invoice Report</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageClientPrintReport.php"),$_SESSION['uper'])){ ?>	
							<li><a href="codeManageClientPrintReport.php">Envelope Print</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageClientReport.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageClientReport.php">Customer Detail Report</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManagePartyReport.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManagePartyReport.php">Partywise Ledger Report</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManagePartyReportAll.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManagePartyReportAll.php">Partywise Historic Ledger Report</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeManageCollectionTax.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeManageCollectionTax.php">Tax Payment Report</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeTdsReport.php"),$_SESSION['uper'])){ ?>
							<li><a href="codeTdsReport.php">TDS Deduction Report</a></li><? }
						if(in_array($objAdminMenu->getIdByName("codeTdsPartyReport.php"),$_SESSION['uper'])){ ?>	
							<li><a href="codeTdsPartyReport.php">Partywise TDS Report</a></li><? } ?>
					</ul>	
				</li>
                <?  */ 
                if(in_array($objAdminMenu->getIdByName("database_backup.php"),$_SESSION['uper'])){ ?>
                    <li class="nav-item">
                        <a href="database_backup.php" class="nav-link">
                            <span class="pcoded-micon"><i class="feather icon-download"></i></span>
                            <!-- <span class="pcoded-mtext">Database Backup</span> -->
                        </a>
                    </li>
                <? } ?>
			</ul>
			
		</div>
	</div>
</nav>