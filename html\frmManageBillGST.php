<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>Manage Bill</h5>
							</div>
							<div class="col-md-6">
								<? if(isset($_REQUEST['btnAddUser']) || isset($_REQUEST['id'])){ ?>
										<a href="<?=$pageName;?>" class="btn btn-success btn-sm btn-round has-ripple f-right">
											<i class="fas fa-angle-left"></i> Back
										</a>
								<?php } elseif($_SESSION['act_add']==1) { ?>
									<form action="<?=$pageName;?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add Bill
										</button>
									</form>									
								<? } ?>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<? if(isset($_REQUEST['btnAddUser']) || isset($_REQUEST['id']) || ($_REQUEST['msg']=="add")){ ?>
						<!--========== Add Bill ==========-->
						<form action="codeManageBillGST.php" method="post" name="frmBill" id="frmBill" onsubmit="MM_validateForm();return document.MM_returnValue">
                        	<input name="hid" id="hid" type="hidden" value="<?=$_GET['id'];?>" />
                            <input type="hidden" name="Total_Items" id="Total_Items" value="<?=(isset($_GET['id'])) ? (count($listItems)==0) ? 1 : count($listItems) : 1;?>" />
<input type="hidden" name="Total_Default_Items" id="Total_Default_Items" value="<?=(isset($_GET['id'])) ? (count($listItems)==0) ? 1 : count($listItems) : 1;?>" />
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label>Date</label>
                                    <input type="text" name="Bill_Date" id="Bill_Date" size="10" value="<?=($listEdit[0]['bdt']) ? $listEdit[0]['bdt'] : date("d-m-Y");?>"  class="form-control dd">
                                </div>
                                <div class="form-group col-md-4">
                                	<? 
										$objBill->company=$_SESSION['company'];
										$objBill->fyear=$_SESSION['fyear'];
										$billRec=$objBill->selectRecByCompanyYear(); 
										//============generate bill no.=========
										$objYear->company=$_SESSION['company'];
										$objYear->fyear=$_SESSION['fyear'];
										$billId=$objYear->selectBidByCompanyYear(); 
										$new_bill_no = sprintf('%03d',$billId[0]['last_bill_id']+1);	
									
										$invno = explode("-",$_SESSION['fyear']);
										$invoice_no="TS/" . $invno[0] . "-" . substr($invno[1], -2) . "/";
										$invoice_no.=$new_bill_no;	
									?>
                                    <label>Invoice No</label>
                                    <input type="text" name="Invoice_No" id="Invoice_No" size="30"  value="<?=isset($listEdit) ? $listEdit[0]['invoice_no'] : $invoice_no;?>"  readonly  class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>PO/PI Number</label>
                                    <input type="text" name="PO_No" id="PO_No" size="30"  value="<?=isset($listEdit) ? $listEdit[0]['po_no'] : "";?>"  class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                <? $listParty=$objParty->selectStatus();?>
                                    <label>Name of the Client</label>
                                    <select name="Party_Name" id="Party_Name" class="js-example-basic-single form-control col-sm-12" onchange="getpartyaddress(this)" required>
                                    <option value="">--Select Party--</option>
                                    <? for($i=0;$i<count($listParty);$i++)
                                    { ?>
                                    <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                    <? } ?>
                                    </select>
                                     <input type="hidden" name="hidden_party" id="hidden_party" value="<?=($listEdit[0]['party']!="") ? $listEdit[0]['party'] : "";?>">
                                </div>
								<div class="form-group col-md-8">
                                    <label>Address</label>
                                    <div id="partyadd"></div>
                                </div>	
                            </div>
							
							<hr>
							<? //================First Item for invoice=============== ?>
							<div class="form-row" id="item1" >
								<div class="form-group col-md-4">
                                    <label>Particulars</label>
									<? $ItemsRec=$objItem->selectStatus(); ?>
                                    <select class="form-control" name="Item_Name1" id="Item_Name1" onchange="getitemprice(this,1)" >
										<option value="">--Select Item--</option>
										<?
                                        for($i=0;$i<count($ItemsRec);$i++) 
                                        { ?>
                                            <option value="<?=$ItemsRec[$i]['id'];?>" <?=($listItems[0]['item']==$ItemsRec[$i]['id']) ? "selected" : ""; ?>><?=$ItemsRec[$i]['item_name'];?></option>
                                    <?	} ?>
                                     </select>
                                    <textarea type="text" name="Item_Detail1" id="Item_Detail1"  class="form-control mt-5"><?=$listItems[0]['description'];?></textarea>
                                </div>
								<div class="form-group col-md-2">
                                    <label>Rate</label>
                                    <input type="text" name="Rate1" id="Rate1" onblur="billcharges(1)" value="<?=$listItems[0]['rate_per_qty'];?>"  class="form-control" onkeypress="return isFloatNumber(event);">
                                </div>
								<div class="form-group col-md-2">
                                    <label>Qty</label>
                                    <input type="text" name="No_of_Items1" id="No_of_Items1" onblur="billcharges(1)" value="<?=($listItems!=0) ? $listItems[0]['no_of_items'] : 1;?>" class="form-control" onkeypress="return isNumber(event);">
                                </div>
								<div class="form-group col-md-2">
                                    <label>Amount</label>
                                    <input type="text" name="Amount1" id="Amount1" size="10" value="<?=$listItems[0]['amount'];?>" class="form-control" readonly>
                                </div>
								<? if($_SESSION['act_del']==1){ ?>
                                <div class="form-group col-md-2 text-center">
                                    <label>Remove</label><br>
                                    <a onclick="delItemGST(1,'<?=($listItems[0]['bid']!="") ? $listItems[0]['bid'] : "";?>','Item','codeManageBillGST.php')"><i class="fas fa-trash-alt clr-red"></i></a>
                                </div>
                                <? } ?>
							</div>
                            <? //==========End - First Item for invoice=============== ?>
                            
                            <? //==========Other Items for invoice=============== ?>
                            <? for($i=2;$i<=10;$i++) { ?>
                            <div class="form-row" id="item<?=$i;?>" style="display:<?=(isset($_GET['id']) && count($listItems)>=$i) ? "block" : "none";?>; width:100%;">
								<div class="form-group col-md-4" style="float:left;">
									<select class="form-control" name="Item_Name<?=$i;?>" id="Item_Name<?=$i;?>" onchange="getitemprice(this,<?=$i;?>)" >
										<option value="">--Select Item--</option>
										<?
                                        for($p=0;$p<count($ItemsRec);$p++) 
                                        { ?>
                                            <option value="<?=$ItemsRec[$p]['id'];?>" <?=($listItems[$i-1]['item']==$ItemsRec[$p]['id']) ? "selected" : ""; ?>><?=$ItemsRec[$p]['item_name'];?></option>
                                            <? } ?>
									</select>
                                    <textarea type="text" name="Item_Detail<?=$i;?>" id="Item_Detail<?=$i;?>"  class="form-control mt-5"><?=$listItems[$i-1]['description'];?></textarea>
                                </div>
								<div class="form-group col-md-2" style="float:left;">
                                    <input type="text" name="Rate<?=$i;?>" id="Rate<?=$i;?>" onblur="billcharges(<?=$i;?>)" value="<?=$listItems[$i-1]['rate_per_qty'];?>"  class="form-control" onkeypress="return isFloatNumber(event);">
                                </div>
								<div class="form-group col-md-2" style="float:left;">
                                    <input type="text" name="No_of_Items<?=$i;?>" id="No_of_Items<?=$i;?>" onblur="billcharges(<?=$i;?>)" value="<?=($listItems!=0) ? $listItems[$i-1]['no_of_items'] : 1;?>" class="form-control" onkeypress="return isNumber(event);">
                                </div>
								<div class="form-group col-md-2" style="float:left;">
                                    <input type="text" name="Amount<?=$i;?>" id="Amount<?=$i;?>" size="10" value="<?=$listItems[$i-1]['amount'];?>" class="form-control" readonly>
                                </div>
								<? if($_SESSION['act_del']==1){ ?>
                                <div class="form-group col-md-2 text-center" style="float:left;">
                                    <a onclick="delItemGST(<?=$i;?>,'<?=($listItems[$i-1]['bid']!="") ? $listItems[$i-1]['bid'] : "";?>','Item','codeManageBillGST.php')"><i class="fas fa-trash-alt clr-red"></i></a>
                                </div>
                                <? } ?>
							</div>
                            <div style="clear:both;"></div>
                            <? } ?>
                            <div class="form-group col-md-12 text-right mb-0">
                                <button type="button" class="btn btn-success" onclick="show()">Add More</button>
                            </div>
                            <? //==========End Other Items for invoice========= ?>
							<hr>

							<div class="form-row">
								<? //============Taxable Amount======== ?>
                                <div class="col-md-10" style="text-align:right;"><label>Taxable Amount : </label></div>
								<div class="form-group col-md-2"><input type="text" name="net_amount" id="net_amount" size="12" value="<?=$listEdit[0]['net_amount'];?>" onclick="billcharges();" class="form-control" onkeypress="return isFloatNumber(event);"></div>
								<div style="clear:both;"></div>
                                
                                <? //============GST/IGST Amount======== ?>
                                <div class="col-md-10" style="text-align:right;">
                                	GST ? 
                                    <input type="radio" name="chkgst" id="chkgst"  value="gst" onclick="billcharges();" <?=($listEdit[0]['bill_type'] == "GST" || !isset($_GET['id'])) ? "checked" : "";?> >&nbsp;&nbsp;&nbsp;
                                    IGST ? 
                                    <input type="radio" name="chkgst" id="chkigst"  value="igst" onclick="billcharges();" <?=($listEdit[0]['bill_type'] == "IGST") ? "checked" : "";?> >
                                </div>
                                <div style="clear:both;"></div>
                                
                                <div id="div_gst" style="display:<?=($listEdit[0]['bill_type'] == "GST"  || !isset($_GET['id'])) ? "block" : "none";?>; float:right!important; width:100%!important;">
                                    <div class="form-row">
                                        <div class="col-md-10" style="text-align:right;">CGST <input type="text" name="cgst_rate" id="cgst_rate" value="<?=(isset($listEdit[0]['cgst_rate']) && $listEdit[0]['cgst_rate']>0) ? $listEdit[0]['cgst_rate'] : 9;?>" class="textbox" style="width:50px!important; text-align:right!important;" onblur="billcharges()" readonly>% :&nbsp;&nbsp;&nbsp;</div>
                                        <div class="form-group col-md-2"><input type="text" name="cgst_amount" id="cgst_amount" size="12" class="form-control" style="text-align:right;" value="<?=$listEdit[0]['cgst_amt'];?>" readonly /></div>
                                        <div style="clear:both;"></div>
                                        <div class="col-md-10" style="text-align:right;">SGST <input type="text" name="sgst_rate" id="sgst_rate" value="<?=(isset($listEdit[0]['sgst_rate']) && $listEdit[0]['sgst_rate']>0) ? $listEdit[0]['sgst_rate'] : 9;?>" class="textbox" style="width:50px!important; text-align:right!important;" onblur="billcharges()" readonly>% :&nbsp;&nbsp;&nbsp;</div>
                                        <div class="form-group col-md-2"><input type="text" name="sgst_amount" id="sgst_amount" size="12" class="form-control" style="text-align:right;" value="<?=$listEdit[0]['sgst_amt'];?>" readonly /></div>
                                    </div>
                                </div>
                               
                                <div id="div_igst" style="display:<?=($listEdit[0]['bill_type'] == "IGST") ? "block" : "none";?>; float:right!important; width:100%!important;">
                                    <div class="form-row">
                                        <div class="col-md-10" style="text-align:right;">IGST <input type="text" name="igst_rate" id="igst_rate" value="<?=(isset($listEdit[0]['igst_rate']) && $listEdit[0]['igst_rate']>0) ? $listEdit[0]['igst_rate'] : 18;?>" class="textbox" style="width:30px!important; text-align:right!important;" onblur="billcharges()" readonly>% :</div>
                                        <div class="form-group col-md-2"><input type="text" name="igst_amount" id="igst_amount" size="12" class="form-control" style="text-align:right; float:left;" value="<?=$listEdit[0]['igst_amt'];?>" readonly /></div>
                                        <div id="err_tax" class="err_msg"></div>
                                    </div>
                                </div>
                                
                                <? $round_off = round($listEdit[0]['net_amount'] + $listEdit[0]['cgst_amt'] + $listEdit[0]['sgst_amt']) - ($listEdit[0]['net_amount'] + $listEdit[0]['cgst_amt'] + $listEdit[0]['sgst_amt']); ?>
       <input type="hidden" name="round_off" id="round_off" size="12" class="textbox" style="text-align:right;" value="<?=number_format($round_off,2);?>" readonly />
                                
                                <? //============Total Amount======== ?>
                                <div class="col-md-10" style="text-align:right;"><label>Taxable Amount : </label></div>
								<div class="form-group col-md-2"><input type="text" name="total_amount" id="total_amount" size="12" value="<?=$listEdit[0]['amount'];?>"  class="form-control" readonly></div>
								<div style="clear:both;"></div>

                                <div class="form-group col-md-12">
									<label>Terms & Conditions :</label>
                                    <textarea name="invoice_terms" id="classic-editor6">
										<?=(isset($_GET['id']) && $_GET['id']!="") ? $listEdit[0]['invoice_terms'] : $objCompany->getInvoiceTermsByCompany($_SESSION['company']);?>
									</textarea>
                                </div>
                                <div style="clear:both;"></div>
                            </div>
                           
	<script type="text/javascript" src="https://code.jquery.com/jquery-1.7.1.js"></script>
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.16/jquery-ui.js"></script>
    <link rel="stylesheet" type="text/css" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.17/themes/base/jquery-ui.css">
                           
                            <?php if(isset($_GET['id'])){?>
                            <input type="submit" name="btnUpdate" value="Update Bill Details" class="btn btn-success" />
                           
                            &nbsp;
                            <input name="btnPrintHard" id="btnPrintHard" type="button" class="btn btn-success checkedhc" value="Print Invoice" />
                        &nbsp;
                            <input type="button" name="btnUpdate" value="Print Invoice Letterhead" class="btn btn-success checkedwl" />
                        <?
                            } else {?>
                            <input type="submit" name="btnAdd" value="Add Bill Details" class="btn btn-success" />
                            <?php } ?>  
                            
                        </form>
						<!--========== Add Bill ==========-->
						<? }else{ ?>
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form name="frmsearch" id="frmsearch" action="codeManageBillGST.php" method="get">
                        <script language="javascript">
						function printInvoice()
						{
							window.open('<?=BASE_URL;?>print_invoice_all_gst.php?Party_Name=<?=$_GET['Client_Name'];?>&Invoice_Number=<?=$_GET['Invoice_Number'];?>&From_Date=<?=$_GET['From_Date'];?>&To_Date=<?=$_GET['To_Date'];?>','_blank');
						}
						
						function printparty()
						{	
							var pname = document.getElementById("Client_Name").value;
							
							var ino = document.getElementById("Invoice_Number").value;
							var fdate = document.getElementById("From_Date").value;
							
							var tdate = document.getElementById("To_Date").value;
						
							if(pname!="" || (fdate!="" && tdate!="") || ino!=""){
								window.open('<?=BASE_URL;?>print_invoice_all.php?Party_Name='+pname+'&Invoice_Number='+ino+'&From_Date='+fdate+'&To_Date='+tdate,'_blank');
							}
							else{
								alert("Please select party name or enter invoice number");
							} 
						}
						
						function printallparty()
						{	
								window.open('<?=BASE_URL;?>print_all_invoice.php','_blank');
						}
						</script>
                        
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
									<? $listParty=$objParty->selectStatus();?>
                                    <label>Search Client Name :</label>
									<select name="Client_Name" id="Client_Name" class="js-example-basic-single form-control col-sm-12">
                                    	<option value="">All</option>
										<? for($i=0;$i<count($listParty);$i++){ ?>
										<option value="<?=$listParty[$i]['id'];?>" <?=($_GET['Client_Name']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
										<? } ?>
									</select>
								</div>
								<div class="form-group col-md-2">
                                    <label>Invoice No</label>
                                    <input type="text" name="Invoice_Number" id="Invoice_Number" size="15" value="<?=$_REQUEST['Invoice_Number'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-3">
									<label>From Date</label>
									<input type="text" name="From_Date" id="From_Date" size="10" value="<?=$_REQUEST['From_Date'];?>" onblur="chkDateRange()" class="form-control dd">
                                </div>
								<div class="form-group col-md-3">
									<label>To Date</label>
									<input type="text" name="To_Date" id="To_Date" value="<?=$_REQUEST['To_Date'];?>"  class="form-control dd">
                                </div>
								<div class="form-group text-center col-md-12">
									<button  type="submit" name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
									<a onclick="javascript:printInvoice()" class="btn btn-info btn-sm has-ripple">Print</a>
									<a onclick="document.location='codeManageBillGST.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
                        <form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                         <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
						<div id="error_frmvalidate" class="err_msg" style="float:right; font-weight:bold; font-style:italic; padding:3px;"></div><div style="clear:both;"></div>
							<table id="tblBillGst" class="table table-striped table-bordered nowrap" width="100%">
								<thead>
									<tr>
										<th colspan="6" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select name="optAction" id="optAction"  class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													<option value="">-- Select Action --</option>
													<? if($_SESSION['act_del']==1){ ?>
                                                      <option value="0">Delete</option><? } ?>
                                                      <? if($_SESSION['act_edit']==1){ ?>
                                                      <option value="1">Published</option>
                                                      <option value="2">Unpublished</option><? } ?> 
                                                      <option value="3">Send Email to Clients</option>
												</select>
											</label>
											<input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" value="Submit" name="btnAction" id="btnAction" />
										</th>			
									</tr>
									<tr>
										<th>Sr. No.</th>
										<th>Bill No.</th>
										<th>Client Name</th>
										<th>Bill Date</th>
										<th>Bill Amount</th>
										<th>
											<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"> Action
										</th>
									</tr>
								</thead>
								<tbody>
                                <? $total_amt = 0;
									if(count($listRec)>0)
									 {
									   for($e=0;$e<count($listRec);$e++){ ?>
                                        <tr>
                                            <td><?=$e+1;?></td>
                                            <td><?=$listRec[$e]['bmin'];?></td>
                                            <td><?=$listRec[$e]['pmpn'];?></td>
                                            <td><?=$listRec[$e]['bdt'];?></td>
                                            <td>₹ <?=$listRec[$e]['bmamt'];
												$total_amt+=$listRec[$e]['bmamt'];
												?>
                                            </td>
                                            <td class="table-action">
                                            	<? if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
                  <input type="checkbox" name="chkAction[]" id="chkAction[]"  value="<? echo $listRec[$e]['bid'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10 chkboxcontent"/>&nbsp;<? } ?>
                  <?php  
				  if($_SESSION['act_edit']==1){
					if($listRec[$e]['status']=='0')
					{ ?>
                      <a href="?status=1&amp;uid=<?=$listRec[$e]['bid'];?>"><i class="fas fa-minus mr-10 clr-red"></i></a>
                      <?php } else {?>
                      <a href="?status=0&amp;uid=<?=$listRec[$e]['bid'];?>"><i class="fas fa-check mr-10 clr-green"></i></a>
                      <?php } ?>
                      	<a href="<?=$pageName;?>?id=<?=$listRec[$e]['bid'];?>"><i class="fas fa-pencil-alt mr-10"></i></a>
					  <? }
					  if($_SESSION['act_del']==1){ ?><a onclick="deleteAll(<?php echo $listRec[$e]['bid'];?>,'<?php echo $listRec[$e]['bmin'];?>','Bill','<?=$pageName;?>')"><i class="fas fa-trash-alt clr-red mr-10"></i></a><? } ?>
						<?php /*?><a href="view_invoice_gst.php?id=<?=$listRec[$e]['bid'];?>" target="_blank"><span class="ripple ripple-animate"></span></a><?php */?>
						<a href="viewcodeManageBillGST.php?id=<?=$listRec[$e]['bid'];?>" class="btn btn-info btn-sm has-ripple">View </a>
                                            </td>
                                        </tr>
                                    <? } 
									} ?>
									
								</tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="5" class="text-right">
                                            <strong>Total Amount</strong>
                                        </td>
                                        <td class="text-right"><strong><?=$totalAmount;?></strong></td>
                                    </tr>
                                </tfoot>
							</table>
                            </form>
						</div>
						<!-- Table -->	
						<!--========== List View ==========-->
						
						<? } ?>
						
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>
<script type="text/JavaScript">

function billcharges(ino) 
{ 
	var amount=0;
	var net_amount=0;
	var round_total = 0;
	var round_off = 0;
	for(var ano=1;ano<=10;ano++)
	{
		if(!isNaN(parseFloat(document.getElementById("Rate"+ano).value))){
			amount = parseFloat(document.getElementById("Rate"+ano).value) * parseFloat(document.getElementById("No_of_Items"+ano).value);
			document.getElementById("Amount"+ano).value=parseFloat(amount);
			net_amount=parseFloat(net_amount)+parseFloat(amount);
		}
	}
	
	if(isNaN(net_amount))
		var net_amount= 0;
	else
		var net_amount=parseFloat(net_amount);
	document.getElementById("net_amount").value = net_amount.toFixed(2);
	
	// GST Calculation
	//alert(document.getElementById("chkgst").value);
	if (document.getElementById("chkgst").checked){
			document.getElementById("err_tax").innerHTML='';
			document.getElementById("div_gst").style.display = "block";
			document.getElementById("div_igst").style.display = "none";
			
			var cgst_rate = document.getElementById("cgst_rate").value;
			var cgst_amt=net_amount*cgst_rate/100;
			document.getElementById("cgst_amount").value = cgst_amt.toFixed(2);
			
			var sgst_rate = document.getElementById("sgst_rate").value;
			var sgst_amt=net_amount*sgst_rate/100;
			document.getElementById("sgst_amount").value = sgst_amt.toFixed(2);
			
			var total_amount = parseFloat(net_amount) + parseFloat(cgst_amt) + parseFloat(sgst_amt);
	}
	else if (document.getElementById("chkigst").checked){
			document.getElementById("err_tax").innerHTML='';
			document.getElementById("div_gst").style.display = "none";
			document.getElementById("div_igst").style.display = "block";
			
			var igst_rate = document.getElementById("igst_rate").value;
			var igst_amt=net_amount*igst_rate/100;
			document.getElementById("igst_amount").value = igst_amt.toFixed(0);
			
			var total_amount = parseFloat(net_amount) + parseFloat(igst_amt);
	}
	else{
		document.getElementById("div_gst").style.display = "none";
		document.getElementById("div_igst").style.display = "none";
		var cgst_amt=0;
		var sgst_amt=0;
		var igst_amt=0;
		var total_amount = parseFloat(net_amount);
	}
	round_total=Math.round(total_amount);
	round_off = parseFloat(round_total - total_amount);
	document.getElementById("round_off").value = round_off.toFixed(2);
	document.getElementById("total_amount").value = round_total.toFixed(2);
}


<!--
function roundNumber(num, dec)
{
	var result = Math.round(num*Math.pow(10,dec))/Math.pow(10,dec);
	return result;
}
//-->
</script>
<script>
$('.checked').click(function(e) {
	e.preventDefault();
	var dialog = $('<p>Want to print original copy ?</p>').dialog({
		buttons: {
			"Yes": function() {window.open("print_invoice_cgst.php?id=<?=$_GET['id'];?>&bt=org",'_blank');
			dialog.dialog('close');},
			"No":  function() {window.open("print_invoice_cgst.php?id=<?=$_GET['id'];?>&bt=dpc",'_blank');
			dialog.dialog('close');},
			"Cancel":  function() {
				//alert('you chose cancel');
				dialog.dialog('close');
			}
		}
	});
});

$('.checkedwl').click(function(e) {
	e.preventDefault();
	var dialog = $('<p>Want to print original copy ?</p>').dialog({
		buttons: {
			"Yes": function() {window.open("print_invoice_letterhead_gst.php?id=<?=$_GET['id'];?>&bt=org",'_blank');
			dialog.dialog('close');},
			"No":  function() {window.open("print_invoice_letterhead_gst.php?id=<?=$_GET['id'];?>&bt=dpc",'_blank');
			dialog.dialog('close');},
			"Cancel":  function() {
				//alert('you chose cancel');
				dialog.dialog('close');
			}
		}
	});
});

$('.checkedhc').click(function(e) {
	e.preventDefault();
	var dialog = $('<p>Want to print original copy ?</p>').dialog({
		buttons: {
			"Yes": function() {window.open("print_invoice_gst.php?id=<?=$_GET['id'];?>&bt=org",'_blank');
			dialog.dialog('close');},
			"No":  function() {window.open("print_invoice_gst.php?id=<?=$_GET['id'];?>&bt=dpc",'_blank');
			dialog.dialog('close');},
			"Cancel":  function() {
				//alert('you chose cancel');
				dialog.dialog('close');
			}
		}
	});
});
</script>