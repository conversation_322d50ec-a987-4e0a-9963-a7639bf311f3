<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Expense ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$object = $objExpense;

	// echo $Contact_Number1; die;
	$object->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$object->party_name=$Company_Name;
	$object->party_address=$Party_Address;	
	$object->area=$Area;
	$object->city=$City;
	$object->state=$State;
	$object->zipcode=$Zipcode;
	$object->country=$Country;
	$object->office_no1=$Office_Number1;
	$object->office_no2=$Office_Number2;
	$object->fax_no=$Fax_Number;
	$object->email=$Email_Address;
	$object->domain_name=$Website;
	$object->contact_name1=$Contact_Name1;
	$object->contact_no=$Contact_Number;
	$object->email1=$Email_Address1;
	$object->contact_name2=$Contact_Name2;
	$object->contact_no2=$Contact_Number2;
	$object->email2=$Email_Address2;
	$object->reference_name=$Reference_Name;
	$object->reference_no=$Reference_Number;
	$object->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$object->insert();	
		redirect("codeExpenseMaster.php");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		$object->update();
		redirect("codeExpenseMaster.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$object->deleteSelect($chkAction);
					break;
			case 1:
					$object->statusUpdatePublish($chkAction);
					break;
			case 2:
					$object->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeExpenseMaster.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$object->delete();
		redirect("codeExpenseMaster.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$object->status();
		redirect("codeExpenseMaster.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$object->selectRecById();						
	}	
	
	if($_GET['Party_Name']!="" || $_GET['Area_Name'])
	{
		if($_GET['Party_Name']!="")
			$query.=" and id='".$_GET['Party_Name']."'";
		if($_GET['Area_Name']!="")
			$query.=" and area='".$_GET['Area_Name']."'";
				
		$listRec=$object->selectRecBySearch($query);
	}
	else
		$listRec=$object->paging();
    
	include("html/frmExpenseMaster.php");
 } 
?>