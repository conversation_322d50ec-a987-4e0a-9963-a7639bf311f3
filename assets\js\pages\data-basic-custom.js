$(document).ready(function() {
    setTimeout(function() {
        // [ Zero Configuration ] start
        $('#simpletable').DataTable();
		
		$('#tblCurrency').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25							
		});
		
		$('#tblFinancialYear').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25							
		});
		
		$('#tblCompany').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 100,
			"stateSave" : true
		});
		
		$('#tblAdminUser').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25						
		});
		
		$('#tblParty').DataTable({
			"lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblItem').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblState').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblBillGst').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		 
		$('#tblCashbook').DataTable({
			 "columnDefs": [
				  { "width": "50px", "targets": 0 },
				  { "width": "100px", "targets": 1 },
				  { "width": "100px", "targets": 2 },
				  { "width": "10px", "targets": 3 },
				  { "width": "200px", "targets": 4 },
				  { "width": "50px", "targets": 5 }
				],						
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		 
		$('#tblTDS').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		 
		$('#tblCollection').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		 
		$('#tblInvoiceReport').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblClientPrintReport').DataTable({
			"lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblClientReport').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblCollectionTax').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblTdsReport').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblTdsPartyReport').DataTable({
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblFollowup').DataTable({
			"columnDefs": [
				  { "width": "5%", "targets": 0 },
				  { "width": "25%", "targets": 1 },
				  { "width": "10%", "targets": 2 },
				  { "width": "20%", "targets": 3 },
				  { "width": "10%", "targets": 4 },
				  { "width": "15%", "targets": 5 },
				  { "width": "20%", "targets": 6 }
				],
			 "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			"pageLength": 25,
			"stateSave" : true
		});
		
		$('#tblPartyStatusReport').DataTable({
			 "order": [
                [1, "asc"]
            ],
			 "columnDefs": [
				  { "width": "50px", "targets": 0 },
				  { "width": "300px", "targets": 1 },
				  { "width": "200px", "targets": 2 },
				  { "width": "50px", "targets": 3 },
				  { "width": "250px", "targets": 4 },
				  { "width": "150px", "targets": 5 }
				],
			  "lengthMenu": [[25, 50, 100, -1], [25, 50, 100, "All"]],
			  "pageLength": 25,
			  "stateSave" : true
		});
		
		/* $('#tblCashbook').DataTable({
			 "order": [
                [1, "asc"]
            ],
			 "columnDefs": [
				  { "width": "50px", "targets": 0 },
				  { "width": "100px", "targets": 1 },
				  { "width": "100px", "targets": 2 },
				  { "width": "10px", "targets": 3 },
				  { "width": "200px", "targets": 4 },
				  { "width": "50px", "targets": 5 }
				],
			  "lengthMenu": [[10, 25, 50, 100, -1], [5, 10, 25, 50, 100, "All"]],
			  "pageLength": 100
		}); */

        // [ Default Ordering ] start
        $('#order-table').DataTable({
            "order": [
                [3, "desc"]
            ]
        });

        // [ Multi-Column Ordering ]
        $('#multi-colum-dt').DataTable({
            columnDefs: [{
                targets: [0],
                orderData: [0, 1]
            }, {
                targets: [1],
                orderData: [1, 0]
            }, {
                targets: [4],
                orderData: [4, 0]
            }]
        });

        // [ Complex Headers ]
        $('#complex-dt').DataTable();

        // [ DOM Positioning ]
        $('#DOM-dt').DataTable({
            "dom": '<"top"i>rt<"bottom"flp><"clear">'
        });

        // [ Alternative Pagination ]
        $('#alt-pg-dt').DataTable({
            "pagingType": "full_numbers"
        });

        // [ Scroll - Vertical ]
        $('#scr-vrt-dt').DataTable({
            "scrollY": "200px",
            "scrollCollapse": true,
            "paging": false
        });

        // [ Scroll - Vertical, Dynamic Height ]
        $('#scr-vtr-dynamic').DataTable({
            scrollY: '50vh',
            scrollCollapse: true,
            paging: false
        });

        // [ Language - Comma Decimal Place ]
        $('#lang-dt').DataTable({
            "language": {
                "decimal": ",",
                "thousands": "."
            }
        });

    	//========added by MD============
        $('#admin-user').DataTable({
          columnDefs: [ {
			orderable: false,
			className: '',
			targets:   [0,-1]
		  } ],
			"order": [
                [2, "asc"]
            ]
        })
		//==========end of added=========
	}, 350);
});
