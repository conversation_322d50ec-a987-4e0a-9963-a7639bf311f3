<?
	session_start();
	include("inc/fileInclude.php");
	include("inc/clsObj.php");

	$objBill->party=$_GET['cid'];
	$partyCurrency=$objBill->selectCurrencyByParty();
	
	$partyBil=$objBill->selectSumByPartyExp();

	//$objPurchase->party=$_GET['cid'];
	//$partyPurchase=$objPurchase->selectSumByParty();
	
	$objTds->party=$_GET['cid'];
	$partyTds=$objTds->selectSumByPartyExp();
	
	$partyAmount = $partyBil[0]['totamt']-$partyTds[0]['credit_amount'];

//===================================================================
	$objCash->party=$_GET['cid'];
	$partyBal=$objCash->selectRecByParty();
	$total_debit = 0;
	$total_credit = 0;
	
	for($c=0;$c<count($partyBal);$c++)
	{
		if($partyBal[$c]['credit_amount']==0 && $partyBal[$c]['debit_amount']>0)
		{
			/*if($partyBal[$c]['currency']>0 && $partyBal[$c]['currency_inr']>0)
				$total_debit+=($partyBal[$c]['debit_amount']*$partyBal[$c]['currency_inr']);
			else */
				$total_debit+=$partyBal[$c]['debit_amount'];
		}
		elseif($partyBal[$c]['credit_amount']>0 && $partyBal[$c]['debit_amount']==0)
		{
			/*if($partyBal[$c]['currency']>0 && $partyBal[$c]['currency_inr']>0)
				$total_credit+=($partyBal[$c]['credit_amount']*$partyBal[$c]['currency_inr']);
			else */
				$total_credit+=$partyBal[$c]['credit_amount'];
		}
	}

	$balance_amount = $partyAmount-($total_credit-$total_debit);
	
	if($balance_amount < 0)
		echo "Credit Amount in ".$objCrn->getCurrencySymbol($partyCurrency[0]['currency'])." : ".-($balance_amount);
	elseif($balance_amount >= 0)
		echo "Debit Amount in ".$objCrn->getCurrencySymbol($partyCurrency[0]['currency'])." : ".$balance_amount;
?>