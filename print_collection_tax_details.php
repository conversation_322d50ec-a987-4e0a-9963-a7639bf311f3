<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();
//include("template.php");
//function main()
//{
    include("inc/clsObj.php");	
/*	$objBill->id=$_GET['id'];

	$listEdit=$objBill->selectRecById();
		
	$objBill->bid = $listEdit[0]['id'];
	$listItems=$objBill->selectItemsByBillNo();		*/
	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}

td
{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>

<div style="width:100%;" id="print_header">
<? // $listRec=$objParty->paging_pending(); 
if($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="__-__-____" && $_GET['To_Date']!="__-__-____")
	{
		$query = "";

		if($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="__-__-____" && $_GET['To_Date']!="__-__-____")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
			$query.=" and `transaction_date` between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		

		$listRec=$objCash->selectRecBySearch($query);
    }
	else{
			if($_GET['month']!="")
				$month=$_GET['month'];
			else	
				$month=date("m");
				
		$query.=" and MONTH( transaction_date ) = ".$month." and financial_year='".$_SESSION['fyear']."'";
		$listRec=$objCash->selectRecBySearch($query);

	}
	// else
		// $listRec=$objCash->selectReport();

?>
<div style="width:800px; margin:0 auto;">
<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#CCCCCC 1px solid;">
  <tr><td align="center" colspan="5">
  
<strong style="font-size:25px; font-weight:bold;"><?=$_SESSION['company'];?></strong><br />
      <?=$objCompany->getInvoiceHeaderByCompany($_SESSION['company']);?>
      </td></tr>
  
  	<tr style="background:#666666; color:#FFFFFF;">
    <td width="9%"><strong>Sr. No. </strong></th>
    <td width="18%"><strong>Date</strong></th>
    <td width="29%"><strong>Party</strong></th>
	<td width="15%"><strong>Credit Amount</strong></th>
	<td width="15%"><strong>Tax Amount</strong></th>
  </tr>
   <?  $n=0;
    if(count($listRec)>0)
	 {
      	$colorflg=0;
	   	for($e=0;$e<count($listRec);$e++)
		{
			$objParty->id = $listRec[$e]['party'];
			$partynm = $objParty->selectRecById();
			if($partynm[0]['country']=="India"){
		  	if($colorflg==1){ $colorflg=0;?>
  		  <tr class="odd" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='odd'">
   <? }	else {	$colorflg=1;?>
		 <tr class="even" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='even'">
   <? } ?>
                <td><?=$e+1;?></td>
                <td><?=$listRec[$e]['tdt'];?></td>
                <td>
                <? echo $partynm[0]['party_name']; ?>
               </td>
                <td style="text-align:right;"><?=number_format($listRec[$e]['credit_amount'],2);?>
                 <? $total+=$listRec[$e]['credit_amount'];?>
                </td>
                <td style="text-align:right;"><?
					if($listRec[$e]['tax_type']==""){	
					if($listRec[$e]['applicable_tax']==0)
						$at="12.36";
					else
						$at=$listRec[$e]['applicable_tax'];
					$taxable_amount =  ($listRec[$e]['credit_amount']*$at)/(100+$at);
					echo number_format($taxable_amount)." (@ ".$at."%)";
					$total_tax+=$taxable_amount;
				}
				elseif($listRec[$e]['tax_type']=="GST"){
					$total_gst_rate = $listRec[$e]['cgst_per'] + $listRec[$e]['sgst_per'];
				
					$taxable_amount =  (($listRec[$e]['credit_amount']*$total_gst_rate)/(100+$total_gst_rate));
					echo number_format($taxable_amount)." (@ CGST ".$listRec[$e]['cgst_per']."% + SGST ".$listRec[$e]['sgst_per']."%)";
					$total_tax+=$taxable_amount;
				
				}
				elseif($listRec[$e]['tax_type']=="IGST"){
					$taxable_amount =  (($listRec[$e]['credit_amount']*$listRec[$e]['igst_per'])/(100+$listRec[$e]['igst_per']));
					echo number_format($taxable_amount)." (@ IGST ".$listRec[$e]['igst_per']."%)";
					$total_tax+=$taxable_amount;
				
				}
					?></td>
	     </tr>
  <? }
  } ?>
  <tr style="background:#333333; color:#FFFFFF; height:30px;"><td colspan="3" style="font-weight:bold;">Total  Amount : </td>
  <td align="right" style="font-weight:bold;"><img src="images/inr_img_white.png" width="8" height="10"> <?=number_format($total);?></td>
  <td align="right" style="font-weight:bold;"><img src="images/inr_img_white.png" width="8" height="10"> <?=number_format($total_tax);?></td>
  </tr> 
  <tr class="toprow">
            <td colspan="5" style="text-align:right; font-style:italic;">Report by : <?=$_SESSION['membername'];?></td>
        </tr>
  <?
  } else { ?>
  		<tr>
        	<td colspan="11" align="center">
            	No Records Found...</td>
		</tr>
    <?php }?>    
  </table>
</div>

</div>
<script language="javascript">
	window.print();
</script>
<? // } ?>