total=0;

		function delItem(id,bid,caption,page)
		{		
			var msg="Want to delete the item "+
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				document.location=page+"?delItem="+id+"&bid="+bid;
			}			
		}
		
		function delItemBill(id,bid,caption,page)
		{		
			var msg="Want to delete the item "+
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				// document.location=page+"?delItem="+id+"&bid="+bid;
				document.getElementById("Item_Name"+id).value="";
				document.getElementById("Item_Detail"+id).value="";
				document.getElementById("Rate"+id).value="";
				document.getElementById("No_of_Items"+id).value=1;
				document.getElementById("Amount"+id).value="";
				//document.getElementById("Discount"+id).value="";
				//document.getElementById("Taxable_Amount"+id).value="";
				document.getElementById("item"+id).style.display="none";
				
				billcharges();
			}			
		}
		
		function delItemCgst(id,bid,caption,page)
		{		
			var msg="Want to delete the item "+
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				// document.location=page+"?delItem="+id+"&bid="+bid;
				document.getElementById("Item_Name"+id).value="";
				document.getElementById("Item_Detail"+id).value="";
				document.getElementById("Rate"+id).value="";
				document.getElementById("No_of_Items"+id).value=1;
				document.getElementById("Amount"+id).value="";
				document.getElementById("Discount"+id).value="";
				document.getElementById("Taxable_Amount"+id).value="";
				document.getElementById("cgst"+id).value="";
				document.getElementById("sgst"+id).value="";
				document.getElementById("item"+id).style.display="none";
				
				billcharges();
			}			
		}
		
		function delItemIgst(id,bid,caption,page)
		{		
			var msg="Want to delete the item "+
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				// document.location=page+"?delItem="+id+"&bid="+bid;
				document.getElementById("Item_Name"+id).value="";
				document.getElementById("Item_Detail"+id).value="";
				document.getElementById("Rate"+id).value="";
				document.getElementById("No_of_Items"+id).value=1;
				document.getElementById("Amount"+id).value="";
				document.getElementById("Discount"+id).value="";
				document.getElementById("Taxable_Amount"+id).value="";
				document.getElementById("igst"+id).value="";
				document.getElementById("item"+id).style.display="none";
				
				billcharges();
			}			
		}

		function delItemGST(id,bid,caption,page)
		{		
			var msg="Want to delete the item "+
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				// document.location=page+"?delItem="+id+"&bid="+bid;
				document.getElementById("Item_Name"+id).value="";
				document.getElementById("Item_Detail"+id).value="";
				document.getElementById("Rate"+id).value="";
				document.getElementById("No_of_Items"+id).value=1;
				document.getElementById("Amount"+id).value="";
				//document.getElementById("Discount"+id).value="";
				//document.getElementById("Taxable_Amount"+id).value="";
				//document.getElementById("cgst"+id).value="";
				//document.getElementById("sgst"+id).value="";
				document.getElementById("item"+id).style.display="none";
				
				billcharges();
			}			
		}


		function deleteAll(cid,caption,caption1,page)
		{			
			var msg="Want to delete "+ caption + " "+ caption1 +
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			var fstatus = document.getElementById("fstatus").value;
			if(confirm(msg))
			{ 
				document.location=page+"?delete="+cid+"&fstatus="+fstatus;
			}			
		}	

		function deleteAllByName(party,caption,caption1,page)
		{			
			var msg="Want to delete "+ caption + " for "+ party +
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				document.location=page+"?delete="+party;
			}			
		}	

		<!--===========Delete item =========-->
		function delQuoteItem(id,qid,caption,page)
		{		
			//alert("testing..");
			var msg="Want to delete the item "+
			"\nYou can not undo after deleting"+
			"\n\nPress [Enter] or click [Ok] to delete"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				document.getElementById("itemDetail"+id).value="";
				document.getElementById("Rate"+id).value="";
				document.getElementById("Amount"+id).value="";
				document.getElementById("item"+id).style.display="none";
				//document.getElementById("Total_Items").value=document.getElementById("Total_Items").value-1;
				billcharges();
			}
			
		}
		<!--======end of item delete=========-->

function deleteAdmin(cid,caption,caption1,page)
		{
			if(cid=='1'){
				alert("You cannot delete admin user");
			}else{
				var msg="Want to delete "+ caption + " "+ caption1 +
				"\nYou can not undo after deleting any of the Admin User"+
				"\n\nPress [Enter] or click [Ok] to delete the Admin User"+
				"\nPress [Esc] or click on [Cancel] to cancel the operation";
				if(confirm(msg))
				{ 
					document.location=page+"?delete="+cid
				}
			}
		}		
	function check_all(Form_Obj,eventgiver)
		{		  
	    var ellen=Form_Obj.elements.length;		
		if(eventgiver.checked==true){
			for(var i=1;i<ellen;i++){
				if(Form_Obj.elements[i].name=="chkAction[]")
				Form_Obj.elements[i].checked=true;
			}
		}else if(eventgiver.checked==false){
			for(var i=1;i<ellen;i++){
				if(Form_Obj.elements[i].name=="chkAction[]")
				Form_Obj.elements[i].checked=false;
			}
		}
}	
function select_all(value)
		{		  
			var Form_Obj = document.forms['frmManageDetails'];
			var total =0;
			for(var i=1;i<Form_Obj.elements.length;i++)
			{
					if(Form_Obj.elements[i].type=="checkbox")
					{
						Form_Obj.elements[i].checked=value;
						total++;
					}
			}
			total--;
			if(!value) total=0;
				document.getElementById("total_selected").innerHTML=total;		
		}
function chkTotal(Form_Obj)		
		{
			total=0;
			for(var i=1;i<Form_Obj.elements.length;i++)
			{
					if(Form_Obj.elements[i].type=="checkbox")
					{
					if(Form_Obj.elements[i].checked==true)
					{						
						total++;
					}
					}
					 
			}
			document.getElementById("total_selected").innerHTML=total;
		}
function chkTotal(Form_Obj)		
		{
			total=0;
			for(var i=1;i<Form_Obj.elements.length;i++)
			{
					if(Form_Obj.elements[i].type=="checkbox")
					{
					if(Form_Obj.elements[i].checked==true)
					{						
						total++;
					}
					}
					 
			}
			document.getElementById("total_selected").innerHTML=total;
		}		
		
function ProperCase(obj)
{
	strTemp = trimAll(obj.value);
	strTemp = strTemp.toLowerCase()
	
	var test=""
	var isFirstCharOfWord = 1
	
	for (var intCount = 0; intCount < strTemp.length; intCount++) 
	{	
		var temp = strTemp.charAt(intCount)
		if (isFirstCharOfWord == 1)
	{
	temp = temp.toUpperCase()
	}
	
	test = test + temp
	if (temp == " ")
	{
	isFirstCharOfWord = 1
	}
	else isFirstCharOfWord = 0
	}
	obj.value = test
}
		
function trimAll(sString)
{
	while (sString.substring(0,1) == ' '){
		sString = sString.substring(1, sString.length);
	}
	while (sString.substring(sString.length-1, sString.length) == ' ')
	{
		sString = sString.substring(0,sString.length-1);
	}
	return sString;
}

function printInvoiceCGST(id){		
	 var msg="Want to print original copy ?";
	if(confirm(msg)){ 
		document.location="print_invoice_cgst.php?id="+id+"&bt=org";
	}
	else{
		document.location="print_invoice_cgst.php?id="+id+"&bt=dpc";
	} 
	
}

function chkSub(obj,cattype,objno,subno){
	foundtrue=0;
	var inputElement = document.getElementsByClassName('chkCat'+objno);
	var inputSubElements = document.getElementsByClassName('chkSubCat'+objno);
	//var inputSubElementsInner = document.getElementsByClassName('chkSubCat'+subno);
	
	if(cattype=="mcat"){
		if(inputSubElements.length>0){
			for(var i=0; inputSubElements[i]; ++i){
				if(obj.checked==true)
					inputSubElements[i].checked = true;
				else
					inputSubElements[i].checked = false;
				
				var inputOptions = document.getElementsByClassName('chkOpt_'+objno+'_'+i);
				for(var o=0; inputOptions[o]; ++o){
				if(obj.checked==true)
					inputOptions[o].checked = true;
				else
					inputOptions[o].checked = false;
				}	
			}
		}
		else{
			var inputOptions = document.getElementsByClassName('chkOpt_'+objno+'_'+0);
			for(var o=0; inputOptions[o]; ++o){
				if(obj.checked==true)
					inputOptions[o].checked = true;
				else
					inputOptions[o].checked = false;
				}	
		}
	}
	else if(cattype=="scat"){
		for(var i=0; inputSubElements[i]; ++i){
			if(inputSubElements[i].checked==true)
				foundtrue = 1;
		}
		if(foundtrue==1)
			inputElement[0].checked=true;
		else
			inputElement[0].checked=false;
		
		var inputOptions = document.getElementsByClassName('chkOpt_'+objno+'_'+subno);
		for(var o=0; inputOptions[o]; ++o){
			if(obj.checked==true)
				inputOptions[o].checked = true;
			else
				inputOptions[o].checked = false;
		}
	}
	else if(cattype=="opt"){
		var inputOptions = document.getElementsByClassName('chkOpt_'+objno+'_'+subno);
		for(var i=0; inputOptions[i]; ++i){
			if(inputOptions[i].checked==true)
				foundtrue = 1;
		}
		if(inputSubElements.length==0){
			if(foundtrue==1)
				inputElement[0].checked=true;
			else
				inputElement[0].checked=false;
		}
		else{
			for(var i=0; inputSubElements[i]; ++i){
				if(i==subno){
					if(foundtrue==1)
						inputSubElements[i].checked=true;
					else
						inputSubElements[i].checked=false;
				}
			}
		}
	}
}


function checkActionValidation() {
	var cf = 0;
	var sf = 0;
	var foundtrue=0;
	//var total = document.getElementById("total_records").value;

	if(document.getElementById("optAction").value!="")
		sf++;
	
	var inputElement = document.getElementsByClassName('chkboxcontent');
	
	//alert(inputElement.length);
	for(var i=0; inputElement[i]; ++i){
		if(inputElement[i].checked==true)
			foundtrue++;
	}
	
	if(sf==0){
		document.getElementById("error_frmvalidate").innerHTML="You must select any option";
		return false;
	}
	if(foundtrue == 0) {
	  document.getElementById("error_frmvalidate").innerHTML="You must check atleast one checkbox!";
	  return false; 
	}
	document.getElementById("error_frmvalidate").innerHTML='';
	return true;
}

function isAlphabets(e, t) {
	try {
		if (window.event) {
			var charCode = window.event.keyCode;
		}
		else if (e) {
			var charCode = e.which;
		}
		else { return true; }
		
		if ((charCode > 64 && charCode < 91) || (charCode > 96 && charCode < 123) || charCode == 8 || charCode == 9 || charCode == 27 || charCode == 13 || charCode ==32 || charCode ==35 || charCode == 36 || charCode == 37 || charCode == 39 || charCode == 46  || charCode == 45 || charCode == 0)
			return true;
		else
			return false;
	}
	catch (err) {
		alert(err.Description);
	}
}

function isNumber(evt) {
	evt = (evt) ? evt : window.event;
	var charCode = (evt.which) ? evt.which : evt.keyCode;
	if (charCode > 31 && (charCode < 48 || charCode > 57)) {
		return false;
	}
	return true;
}

function isFloatNumber(evt) {
	evt = (evt) ? evt : window.event;
	var charCode = (evt.which) ? evt.which : evt.keyCode;
	if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode!=46) {
		return false;
	}
	return true;
}