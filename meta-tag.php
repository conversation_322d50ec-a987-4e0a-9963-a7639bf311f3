<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">

<?
	if(stristr($_SERVER['PHP_SELF'],"index.php")!="") {
	?>
		<title>Welcome to ERP Techspin Solution</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"dashboard.php")!="") {
	?>
		<title>Dashboard</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
    elseif(stristr($_SERVER['PHP_SELF'],"login.php")!="") {
	?>
		<title>Login</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"viewcodeManageAdminUser.php")!="") {
	?>
		<title>View Manage User</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeAdminChangePassword.php")!="") {
	?>
		<title>Change Password</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageAdminMenu.php")!="") {
	?>
		<title>Manage Admin Menu</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageParty.php")!="") {
	?>
		<title>Manage Client</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageItem.php")!="") {
	?>
		<title>Manage Particular</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageCompany.php")!="") {
	?>
		<title>Manage Company</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageState.php")!="") {
	?>
		<title>Manage State</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageYear.php")!="") {
	?>
		<title>Manage Financial Year</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageCurrency.php")!="") {
	?>
		<title>Manage Currency</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageBillGST.php")!="") {
	?>
		<title>Manage Bill</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageBillGST.php")!="") {
	?>
		<title>Manage Bill</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageBillExp.php")!="") {
	?>
		<title>Export Invoice</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageCashbookExp.php")!="") {
	?>
		<title>Export Payment</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageDeduction.php")!="") {
	?>
		<title>Export Deduction</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManagePartyReportExp.php")!="") {
	?>
		<title>Partywise Ledger Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codePartyStatusReportExp.php")!="") {
	?>
		<title>Pending Payment</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageCollectionExp.php")!="") {
	?>
		<title>Collection Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeTdsReportExp.php")!="") {
	?>
		<title>Deduction Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeTdsPartyReportExp.php")!="") {
	?>
		<title>Partywise Deduction Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageCashbook.php")!="") {
	?>
		<title>Manage Payment</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageTds.php")!="") {
	?>
		<title>TDS Entry</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codePartyFollowup.php")!="") {
	?>
		<title>Followups</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageCollection.php")!="") {
	?>
		<title>Collection Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codePartyStatusReport.php")!="") {
	?>
		<title>Pending Payment Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeInvoiceReport.php")!="") {
	?>
		<title>Invoice Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageClientReport.php")!="") {
	?>
		<title>Customer Detail Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManagePartyReport.php")!="") {
	?>
		<title>Partywise Ledger Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManagePartyReportAll.php")!="") {
	?>
		<title>Partywise Historic Ledger Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeManageCollectionTax.php")!="") {
	?>
		<title>Tax Payment Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeTdsReport.php")!="") {
	?>
		<title>Tax Deduction Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
	elseif(stristr($_SERVER['PHP_SELF'],"codeTdsPartyReport.php")!="") {
	?>
		<title>Partywise TDS Report</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
    else {
	?>
		<title>Welcome to ERP Techspin Solution</title>
		<meta name="description" content="" />
		<meta name="keywords" content="" />
	<?
	}
?>