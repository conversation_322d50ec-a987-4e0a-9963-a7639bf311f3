<?php session_start();
global $db;
class personalMaster
{
   /* Variable Declaration */		
	var $tablename='personalbook';
	var $id;
	var $cb_type;
	var $credit_amount;
	var $debit_amount;
	var $cheque_cash;
	var $cheque_detail;
	var $transaction_detail;
	var $transaction_date;
	var $limit;
	var $start;

	/* Constructor to intialize the Dabatabase */
	function personalMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
				'$this->cb_type',
				'$this->credit_amount',
				'$this->debit_amount',
				'$this->cheque_cash',
				'$this->cheque_detail',
				'$this->transaction_detail',
				'$this->transaction_date')";
		// echo $sql; die();
		$this->db->insert($sql);
		$id=mysql_insert_id();
		return($id);
	} 			

	/* update the record in database */		
	function update()
	{
		$sql = "update `$this->tablename` set
						`credit_amount`='$this->credit_amount',
						`debit_amount`='$this->debit_amount',
						`cheque_cash`='$this->cheque_cash',
						`cheque_detail`='$this->cheque_detail',
						`transaction_detail`='$this->transaction_detail',
						`transaction_date`='$this->transaction_date' 
						where `id`=$this->id";
		// echo $sql;die();
		$this->db->edit($sql);		
		return true;
	}	

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select *, date_format(`transaction_date`, '%d-%m-%Y') as trn_dt from `$this->tablename` where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*delete a record from database*/	
	function deleteById()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";
		//echo $sql;die();
		mysql_query($sql);
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql="delete from `$this->tablename` where `id` = '$id'";
			$res = mysql_query($sql);
		}
		return true;
	}	

	/*...paging...*/
	function pagingPersonal()
	{			
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where cb_type = 'Personal'";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}		

	/*...paging...*/
	function pagingOffice()
	{			
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where cb_type = 'Office'";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}						

	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename`";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	

	/*...paging...*/
	function selectRecBySearch($query)
	{
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where id!=''".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*...paging...*/
	function pagingAccountStatus()
	{			
		$pages = new Paging();
		$pages->sql ="select party, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount from `cashbook` group by party";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}	
}		
?>   