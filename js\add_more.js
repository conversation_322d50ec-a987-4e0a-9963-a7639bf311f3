// JavaScript Document
//-------------------------------------------------------
//..........ADD MORE button for Items............
//-------------------------------------------------------
function show()
{	

	var Total_Items=parseInt(document.getElementById("Total_Items").value);
	var clicktime=parseInt(Total_Items);

	if(clicktime==0) clicktime=1;	
	switch(clicktime)
	{
		case 1:
			document.getElementById("item2").style.display="block";
			clicktime++;
			Total_Items++;
			break;
		case 2:
			document.getElementById("item3").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		case 3:
			document.getElementById("item4").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		case 4:
			document.getElementById("item5").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		case 5:
			document.getElementById("item6").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		case 6:
			document.getElementById("item7").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		case 7:
			document.getElementById("item8").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		case 8:
			document.getElementById("item9").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		case 9:
			document.getElementById("item10").style.display="block";	clicktime++;
			Total_Items++;				
			break;
		default:
			alert("Opps! you can not add more item details"); 
	}	
	document.getElementById("Total_Items").value=Total_Items;	
}

function hide()
{
	var Total_Items=parseInt(document.getElementById("Total_Items").value);
	var clicktime=parseInt(Total_Items);
	if(clicktime>document.getElementById("Total_Default_Items").value)
	{
		clicktime--;
		switch(clicktime)
		{
			case 1:
				document.getElementById("item2").style.display="none";
				document.getElementById("No_of_Items2").value="";
				document.getElementById("No_of_Items2").style.backgroundColor='white';	
				document.getElementById("Rate2").value="";												
				document.getElementById("Rate2").style.backgroundColor='white';	
				document.getElementById("Amount2").value="";																		
				Total_Items--;				
				break;
			case 2:
				document.getElementById("item3").style.display="none";
				document.getElementById("No_of_Items3").value="";
				document.getElementById("No_of_Items3").style.backgroundColor='white';	
				document.getElementById("Rate3").value="";												
				document.getElementById("Rate3").style.backgroundColor='white';	
				document.getElementById("Amount3").value="";																		
				Total_Items--;				
				break;
			case 3:
				document.getElementById("item4").style.display="none";
				document.getElementById("No_of_Items4").value="";
				document.getElementById("No_of_Items4").style.backgroundColor='white';	
				document.getElementById("Rate4").value="";												
				document.getElementById("Rate4").style.backgroundColor='white';	
				document.getElementById("Amount4").value="";																		
				Total_Items--;				
				break;
			case 4:
				document.getElementById("item5").style.display="none";
				document.getElementById("No_of_Items5").value="";
				document.getElementById("No_of_Items5").style.backgroundColor='white';	
				document.getElementById("Rate5").value="";												
				document.getElementById("Rate5").style.backgroundColor='white';	
				document.getElementById("Amount5").value="";																		
				Total_Items--;				
				break;
			case 5:
				document.getElementById("item6").style.display="none";
				document.getElementById("No_of_Items6").value="";
				document.getElementById("No_of_Items6").style.backgroundColor='white';	
				document.getElementById("Rate6").value="";												
				document.getElementById("Rate6").style.backgroundColor='white';	
				document.getElementById("Amount6").value="";																		
				Total_Items--;				
				break;
			case 6:
				document.getElementById("item7").style.display="none";
				document.getElementById("No_of_Items7").value="";
				document.getElementById("No_of_Items7").style.backgroundColor='white';	
				document.getElementById("Rate7").value="";												
				document.getElementById("Rate7").style.backgroundColor='white';	
				document.getElementById("Amount7").value="";																		
				Total_Items--;				
				break;
			case 7:
				document.getElementById("item8").style.display="none";
				document.getElementById("No_of_Items8").value="";
				document.getElementById("No_of_Items8").style.backgroundColor='white';	
				document.getElementById("Rate8").value="";												
				document.getElementById("Rate8").style.backgroundColor='white';	
				document.getElementById("Amount8").value="";																		
				Total_Items--;				
				break;
			case 8:
				document.getElementById("item9").style.display="none";
				document.getElementById("No_of_Items9").value="";
				document.getElementById("No_of_Items9").style.backgroundColor='white';	
				document.getElementById("Rate9").value="";												
				document.getElementById("Rate9").style.backgroundColor='white';	
				document.getElementById("Amount9").value="";																		
				Total_Items--;				
				break;
			case 9:
				document.getElementById("item10").style.display="none";
				document.getElementById("No_of_Items10").value="";
				document.getElementById("No_of_Items10").style.backgroundColor='white';	
				document.getElementById("Rate10").value="";												
				document.getElementById("Rate10").style.backgroundColor='white';	
				document.getElementById("Amount10").value="";																		
				Total_Items--;				
				break;			
			default:
				alert("You can not remove the default one."); 
		}
		
		var net_amount = 0;
		for(var ano=1;ano<=10;ano++)
		{
			if(!isNaN(parseFloat(document.getElementById("Amount"+ano).value)))
				net_amount = parseFloat(net_amount) + parseFloat(document.getElementById("Amount"+ano).value);
		}
	
		if(isNaN(net_amount))
			var net_amount = 0;
		else
			var net_amount = parseFloat(net_amount);
	
		// document.getElementById("Net_Amount").value = roundNumber(parseFloat(net_amount),2);		
		document.getElementById("Total_Items").value=Total_Items;
		billcharges();
	}
}

function roundNumber(num, dec)
{
	var result = Math.round(num*Math.pow(10,dec))/Math.pow(10,dec);
	return result;
}







