var ajax = new sack();	

//=========function for auto suggest=======//
function executeFunctionByName(functionName, context, args) {
  var args = [].slice.call(arguments).splice(2);
  var namespaces = functionName.split(".");
  var func = namespaces.pop();
  for(var i = 0; i < namespaces.length; i++) {
	context = context[namespaces[i]];
  }
  return context[func].apply(this, args);
}

function callFun(obj,fname){
	executeFunctionByName(fname, window, obj);
}
//=======end of auto suggest text function=======//
	
function getaddress(sel)
{		
	if(sel.options[sel.selectedIndex].value!="")
		var cid = sel.options[sel.selectedIndex].value;
	else
		var cid = 0;

	ajax.requestFile = 'getaddress.php?cid='+cid;				
	ajax.onCompletion = creataddress;			
	ajax.runAJAX();		
}	

function creataddress()
{
	var results = ajax.response.split("***");
	var obj = document.getElementById('comadd');
	if(!obj.disabled)
		obj.innerHTML = trim(results[0]);
	
/*	for(var i=1;i<=5;i++)
	{
		var obj = document.getElementById('Item_Name'+i);
		if(!obj.disabled)
		{
			document.getElementById('Item_Name'+i).options.length=0;
			eval(results[1]);
		}
	}  */
}

/* function getpartyaddress(sel)
{		
	if(sel.options[sel.selectedIndex].value!="")
		var cid = sel.options[sel.selectedIndex].value;
	else
		var cid = 0;

	ajax.requestFile = 'getpartyaddress.php?cid='+cid;				
	ajax.onCompletion = creatpartyaddress;			
	ajax.runAJAX();		
} */	

function getpartyaddress(obj)
{	
	var cid = document.getElementById("Party_Name").value;
	ajax.requestFile = 'getpartyaddress.php?cid='+cid;				
	ajax.onCompletion = creatpartyaddress;			
	ajax.runAJAX();		
}

function creatpartyaddress()
{
	if(trim(ajax.response)!="")
		document.getElementById("partyadd").innerHTML=trim(ajax.response);
	else
		document.getElementById("partyadd").innerHTML="";
}


function getpartyaddressquote(sel)
{	//var obj = document.getElementById('Party_Address');
	//obj.innerHTML = "";
	document.getElementById("hidden_party").value=document.getElementById("Party_Name").value;
	var party = document.getElementById("Party_Name").value;
	ajax.requestFile = 'getpartyaddressquote.php?party='+party;				
	ajax.onCompletion = creatpartyaddressquote;			
	ajax.runAJAX();		
}

function creatpartyaddressquote()
{
	var obj1 = document.getElementById('Party_Address');
	var obj2 = document.getElementById('Party_City');
	var obj3 = document.getElementById('Contact_No');
	var obj4 = document.getElementById('Email_ID');
	var results = ajax.response.split("***");
	// alert(results);
	if(trim(results[1] || results[2])!="" || results[3] || results[4]){
		obj1.value = trim(results[1]);
		obj2.value = trim(results[2]);
		obj3.value = trim(results[3]);
		obj4.value = trim(results[4]);
	/* if(!obj.disabled)
		obj.innerHTML = trim(ajax.response); */
	}
	else{
		obj1.value = "";
		obj2.value = "";
		obj3.value = "";
		obj4.value = "";
	}
}


function getpartybalance()
{
	document.getElementById("party_balance").innerHTML="";
	if(document.getElementById("Party_Name").value!=""){
		var cid = document.getElementById("Party_Name").value;
		ajax.requestFile = 'getpartybalance.php?cid='+cid;				
		ajax.onCompletion = creatpartybalance;			
		ajax.runAJAX();		
	}
}	

function creatpartybalance()
{
//	var results = ajax.response.split("*****");
	
	document.getElementById("party_balance").style.display = "block";
	document.getElementById("party_balance").innerHTML = ajax.response;
	
/*	Pending
	document.getElementById("party_balance").style.display = "block";
	document.getElementById("party_balance").innerHTML = results[0];
	document.getElementById("party_invoice_details").innerHTML = results[1]; */

/* 	var obj = document.getElementById('partyadd');
	if(!obj.disabled)
		obj.innerHTML = trim(ajax.response); */
}

function getpartybalance_exp()
{
	document.getElementById("party_balance").innerHTML="";
	if(document.getElementById("Party_Name").value!=""){
		var cid = document.getElementById("Party_Name").value;
		ajax.requestFile = 'getpartybalance_exp.php?cid='+cid;				
		ajax.onCompletion = creatpartybalance_exp;			
		ajax.runAJAX();		
	}
}	

function creatpartybalance_exp()
{
//	alert(ajax.response);

	document.getElementById("party_balance").style.display = "block";
//	document.getElementById("party_balance").value = ajax.response;
	document.getElementById("party_balance").innerHTML = ajax.response;

/* 	var obj = document.getElementById('partyadd');
	if(!obj.disabled)
		obj.innerHTML = trim(ajax.response); */
}




function getpartybalanceall(obj)
{		
	var cid = obj.value;

	ajax.requestFile = 'getpartybalanceall.php?cid='+cid;				
	ajax.onCompletion = creatpartybalanceall;			
	ajax.runAJAX();		
}	

function creatpartybalanceall()
{
	document.getElementById("party_balance").style.display = "block";
	document.getElementById("party_balance").innerHTML = ajax.response;
}


function newItem(sn){
	document.getElementById("Item_Name"+sn).value="";
	document.getElementById("New_Item"+sn).style.display="block";
	document.getElementById("Item_Detail"+sn).value="";
	document.getElementById("Rate"+sn).value="";
	document.getElementById("Amount"+sn).value="";
	
	billcharges(sn);
}


function getitemprice(obj,sn)
{
//	alert("test");
//	document.getElementById("New_Item"+sn).style.display="none";
	
	var item_id = obj.value;
	ajax.requestFile = 'getitemprice.php?id='+item_id+'&sno='+sn;
	ajax.onCompletion = itemdetail;
	ajax.runAJAX();				
}	

function itemdetail()
{
	results = ajax.response.split("***");
	var sno = results[2];
	
	if(trim(ajax.response)!="")
	{
		document.getElementById("Rate"+sno).value=trim(results[0]);
		document.getElementById("Item_Detail"+sno).value=results[1];
	}

	billcharges(sno);
}

function getitempriceoutsource(obj,sn)
{
//	document.getElementById("New_Item"+sn).style.display="none";
//	alert("test");
	var item_id = obj.value;
	ajax.requestFile = 'getitempriceoutsource.php?id='+item_id+'&sno='+sn;
	ajax.onCompletion = itemdetailoutsource;
	ajax.runAJAX();				
}	

function itemdetailoutsource()
{
	results = ajax.response.split("***");
	var sno = results[2];
	
	if(trim(ajax.response)!="")
	{
		document.getElementById("Rate"+sno).value=trim(results[0]);
		document.getElementById("Item_Detail"+sno).value=results[1];
	}

	billcharges(sno);
}


function getcurrencyinr(obj)
{
//	alert("test");
	var item_id = obj.value;
	ajax.requestFile = 'getcurrencyinr.php?id='+item_id;
	ajax.onCompletion = fillcurrencyinr;
	ajax.runAJAX();				
}	

function fillcurrencyinr()
{
	results = ajax.response.split("***");
	var sno = results[1];
	
	if(trim(ajax.response)!="")
		document.getElementById("currency_inr").value=trim(results[1]);
}


function get_inv_no(obj)
{
	var invoice_type = obj.value;
	if(obj.value==9 || obj.value==11)
	{
		document.getElementById("rendatelbl").style.display="block";
		document.getElementById("rendate").style.display="block";
	}
	else
	{
		document.getElementById("rendatelbl").style.display="none";
		document.getElementById("rendate").style.display="none";
	}
	
	ajax.requestFile = 'getinvoiceno.php?cid='+invoice_type;
	ajax.onCompletion = invoicedetail;
	ajax.runAJAX();
}	

function invoicedetail()
{
	// alert(ajax.response);
	results = trim(ajax.response);
	document.getElementById("Invoice_No").value = results;
}	


function getbillno(bno,bid)
{
	ajax.requestFile = 'checkbillno.php?bid='+bid+'&bno='+bno.value;	
	ajax.onCompletion = creatbillno;
	ajax.runAJAX();				
}	

function creatbillno()
{
	if(trim(ajax.response)=="Exist")
	{
		alert("Bill No. "+document.getElementById('Bill_No').value+"  already exist.");
		document.getElementById('Bill_No').value="";
		document.getElementById('Bill_No').focus();
	}
}	

function getitems(sel)
{
	if(sel.options[sel.selectedIndex].value!="")
		var cid = sel.options[sel.selectedIndex].value;
	else
		var cid = 0;
	document.getElementById('Item_Name').options.length=0;
	document.getElementById('LR_No').options.length=1;
	document.getElementById('Available_Qty').value = "";
	document.getElementById('Weight_Per_Bag').value="";
	document.getElementById("Sale_Qty").value = "";
	document.getElementById("Total_Weight").value = "";
	document.getElementById("Your_Price").value = "";
	document.getElementById("Amount").value = "";
	ajax.requestFile = 'getitems.php?cid='+cid;				
	ajax.onCompletion = creatitems;			
	ajax.runAJAX();		
}	

function creatitems()
{
	var obj = document.getElementById('Item_Name');
	if(!obj.disabled)
		eval(ajax.response);					
}

function getlrno()
{		
	/* if(sel.options[sel.selectedIndex].value!="")
		var itemid = sel.options[sel.selectedIndex].value;
	else
		var itemid = 0; */

	var cid = 0;
	var itemid = 0;
	var cid = document.getElementById('Company_Name').value;
	var itemid = document.getElementById('Item_Name').value;
	
	document.getElementById('LR_No').options.length=0;
	document.getElementById('Available_Qty').value = "";
	document.getElementById('Weight_Per_Bag').value="";
	document.getElementById("Sale_Qty").value = "";
	document.getElementById("Total_Weight").value = "";
	document.getElementById("Your_Price").value = "";
	document.getElementById("Amount").value = "";
	
	ajax.requestFile = 'getlrno.php?cid='+cid+'&itemid='+itemid;				
	ajax.onCompletion = creatlrnos;			
	ajax.runAJAX();		
}

function creatlrnos()
{
	var obj = document.getElementById('LR_No');
	if(!obj.disabled)
		eval(ajax.response);
}

function getqty(sel)
{		
	if(sel.options[sel.selectedIndex].value!="")
		var bid = sel.options[sel.selectedIndex].value;
	else
		var bid = 0;

	var cid = document.getElementById('Company_Name').value;
	var itemid = document.getElementById('Item_Name').value;

	ajax.requestFile = 'getqty.php?cid='+cid+'&itemid='+itemid+'&bid='+bid;				
	ajax.onCompletion = creatqty;
	ajax.runAJAX();		
}	

function creatqty()
{
//	alert(ajax.response);
	results = ajax.response.split("***");
	document.getElementById('Available_Qty').value = trim(results[0]);
	document.getElementById('Weight_Per_Bag').value = trim(results[1]);
}

function whenLoading()
{
	var e = document.getElementById('replaceme'); 
	e.innerHTML = '<b>checking Email</b>';
}
function whenCompleted()
{
	var e = document.getElementById('replaceme'); 
	var return_value = ajax.response;
	if(return_value.indexOf("Email Already Exits")!=-1)
		document.getElementById('email').focus();
	else
		e.innerHTML = ajax.response;	
}
function getusername()
{
	var form = document.getElementById('frmManage');		
		ajax.setVar("email", form.email.value); 		
		ajax.requestFile = "getuser.php";
		ajax.method = "post";
		ajax.element = 'replaceme';
		ajax.onLoading = whenLoading;
		ajax.onCompletion = whenCompleted;
		ajax.runAJAX();	
}

function whenLoadingMember()
{
	var e = document.getElementById('replaceme'); 
	e.innerHTML = "<p>Searching Member...</p>";
}

function whenCompletedMember()
{
	var e = document.getElementById('replaceme');	
}	


function check_pname(obj)
{
	// var pname = obj.value
	ajax.requestFile = 'check_pname.php?pn='+obj.value;	
	ajax.onCompletion = chkCompletedPname;
	ajax.runAJAX();				
}	

function chkCompletedPname()
{
	// alert(trim(ajax.response));
	document.getElementById('cpn').innerHTML = "";
	if(trim(ajax.response)=="not exist")
	{
		// obj.value="";
		document.getElementById('cpn').innerHTML = "Party name is not available. New party name will be added.";
		// document.getElementById('Party_Name').focus();
	}
}	

