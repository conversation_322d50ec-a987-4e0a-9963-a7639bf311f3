<?php 
include("template.php");
function main()
{
	$heading="Manage Personal Cashbook ";
    include("inc/clsObj.php");	
	
	extract($_POST);	
	$cdate = explode("-",$Transaction_Date);
	$transaction_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

	$objPersonal->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objPersonal->cb_type="Personal";
	if($Credit_Debit==0)
	{
		$objPersonal->credit_amount=$Amount;
		$objPersonal->debit_amount=0;
	}
	elseif($Credit_Debit==1)
	{
		$objPersonal->credit_amount=0;
		$objPersonal->debit_amount=$Amount;
	}
	$objPersonal->cheque_cash=$Cash_Cheque;
	$objPersonal->cheque_detail=$Cheque_Detail;
	$objPersonal->transaction_detail=$Transaction_Detail;
	$objPersonal->transaction_date=$transaction_date;
	
	if(isset($_POST['btnAdd']))
	{
		$objPersonal->insert();
		redirect("codePersonalbook.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objPersonal->update();
		redirect("codePersonalbook.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objPersonal->deleteSelect($chkAction);
				break;
			case 1:
				$objPersonal->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objPersonal->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
					
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codePersonalbook.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objPersonal->deleteById();
		redirect("codePersonalbook.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objPersonal->status();
		redirect("codePersonalbook.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objPersonal->selectRecById();

		$objPersonal->cid = $listEdit[0]['id'];
		$listItems=$objPersonal->selectRecById();	
	}
	elseif($_GET['From_Date']!="")
	{
		$query = "and cb_type='Personal'";
		if($_GET['From_Date']!="" && $_GET['To_Date']=="")
			$query.=" and date_format(`transaction_date`, '%d-%m-%Y') = '".$_GET['From_Date']."'";
		elseif($_GET['From_Date']!="" && $_GET['To_Date']!="")
			$query.=" and date_format(`transaction_date`, '%d-%m-%Y') between '".$_GET['From_Date']."' and '".$_GET['To_Date']."'";
		
		// echo $query; die;
		
		$listRec=$objPersonal->selectRecBySearch($query);
    }
	else
		$listRec=$objPersonal->pagingPersonal();
	include("html/frmPersonalbook.php");
} 
?>
