<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>Manage TDS</h5>
							</div>
							<div class="col-md-6">
								
								<? if(isset($_REQUEST['btnAddUser']) or isset($_REQUEST['id']) or ($_REQUEST['msg']=="add")){ ?>
								
									<form action="<?=$pageName;?>" method="post">
										<button type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack">
											<i class="fas fa-angle-left"></i> Back
										</button>
									</form>
								
								 <?php } elseif($_SESSION['act_add']==1) { ?>
								
									<form action="<?=$pageName;?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add TDS
										</button>
									</form>									
								
								<? } ?>
								
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<? if(isset($_REQUEST['btnAddUser']) or isset($_REQUEST['id']) or ($_REQUEST['msg']=="add")){ ?>
						
						<!--========== Add TDS ==========-->
						<form action="codeManageTds.php" method="post" name="frmcb" id="frmcb" >
                            <input name="hid" id="hid" type="hidden" value="<?=$_GET['id'];?>" />
                            <input type="hidden" name="action" id="action" value="" />
                            <div class="form-row">
								<div class="form-group col-md-4">
                                    <label>Date</label>
                                    <input type="text" class="form-control dd" name="Transaction_Date" id="Transaction_Date" size="10" value="<?=($listEdit[0]['trn_dt']!="") ? $listEdit[0]['trn_dt'] : date("d-m-Y");?>">
                                </div>	
                                <div class="form-group col-md-4">
                                	 <? $listParty=$objParty->selectStatus();?>
                                    <label>Party Name</label>
                                    <?php /*?><select name="Party_Name" id="Party_Name" class="form-control">
                                    <option value="">--Select Client--</option>
                                    <? for($i=0;$i<count($listParty);$i++)
                                    { ?>
                                    <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                    <? } ?>
                                    </select><?php */?>
                                    
                                    <select name="Party_Name" id="Party_Name" class="js-example-basic-single form-control col-sm-12" required onchange="getpartybalance()">
                                    <option value="">--Select Party--</option>
                                    <? for($i=0;$i<count($listParty);$i++)
                                    { ?>
                                    	<option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                    <? } ?>
                                    </select>
                                    <b id="party_balance"></b>
                                </div>								
								<div class="form-group col-md-4">
                                    <label>Details</label>
                                    <textarea type="text" name="Transaction_Detail" id="Transaction_Detail" class="form-control"><?=$listEdit[0]['transaction_detail'];?></textarea>
                                </div>	
								<div class="form-group col-md-4">
                                    <label>Amount</label>
                                    <?
										if($listEdit[0]['credit_amount'] > 0)
										{
											$amount = $listEdit[0]['credit_amount'];
											$credit_debit = 0;
										}
										elseif($listEdit[0]['debit_amount'] > 0)
										{
											$amount = $listEdit[0]['debit_amount'];
											$credit_debit = 1;
										} ?>
                                    <input type="text" name="Amount" id="Amount" value="<?=$amount;?>" class="form-control" required>
                                </div>
                            </div>
                            <?php if(isset($_GET['id']) && $_GET['id']!=""){?>
<input type="submit" name="btnUpdate" value="Update Tds Details" class="btn btn-success"/>
<?php } else { ?>
<input type="submit" name="btnAdd" value="Add Tds Details" class="btn btn-success" />
<?php } ?> 
                        </form>
						<!--========== Add TDS ==========-->
						
						<? }
						else {
						?>
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form  name="frmsearch" id="frmsearch" action="" method="get">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
                                <? $listParty=$objParty->selectStatus();?>
									<label>Search Party Name :</label>
									<select name="Client_Name" id="Client_Name" class="js-example-basic-single form-control col-sm-12">
										<option value="">--Select Client--</option>
										<? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($_GET['Client_Name']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
									</select>
								</div>
								<div class="form-group col-md-3">
									<label>From Date</label>
									 <input type="text" name="From_Date" id="From_Date" size="10" value="<?=$_REQUEST['From_Date'];?>" class="form-control dd">
                                </div>
								<div class="form-group col-md-3">
									<label>To Date</label>
									<input type="text" class="form-control dd" name="To_Date" id="To_Date" size="10" value="<?=$_REQUEST['To_Date'];?>">
                                </div>
								<div class="form-group text-center col-md-12">
									<a href="#" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</a>
									<a href="#" class="btn btn-info btn-sm has-ripple">Reset</a>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
                        <form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                         <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
						<div id="error_frmvalidate" class="err_msg" style="float:right; font-weight:bold; font-style:italic; padding:3px;"></div><div style="clear:both;"></div>
							<table id="tblTDS" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th colspan="6" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select  name="optAction" id="optAction" class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													<option value="">--Select Action--</option>
													  <? if($_SESSION['act_del']==1){ ?>
                                                      <option value="0">Delete</option><? } ?>
												</select>
											</label>
											<input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" value="Submit" name="btnAction" id="btnAction" />
										</th>			
									</tr>
									<tr>
										<th class="sorting_asc sorting_desc">Sr. No.</th>
										<th class="sorting_asc sorting_desc">Party</th>
										<th class="sorting_asc sorting_desc">Amount</th>
										<th class="sorting_asc sorting_desc">Date</th>
										<th class="sorting_asc sorting_desc">
											<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"/> Action
										</th>
									</tr>
								</thead>
								<tbody>
                                <?	if(count($listRec)>0){
										for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
										<td><?	$objParty->id = $listRec[$e]['party'];
												$partynm = $objParty->selectRecById();
												echo $partynm[0]['party_name']; ?>
                                        </td>
										<td><?=number_format($listRec[$e]['credit_amount'],2);?></td>
										<td><?=$listRec[$e]['tdt'];?></td>
										<td class="table-action">
											<? if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
                                                    <input type="checkbox" name="chkAction[]" id="chkAction[]" value="<? echo $listRec[$e]['id'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10 chkboxcontent"/>&nbsp;<? } ?>
										 <?php  
										  if($_SESSION['act_edit']==1){
											if($listRec[$e]['status']=='0')
											{ ?>
											<a href="?status=1&amp;uid=<?=$listRec[$e]['id'];?>">
												<i class="fas fa-minus mr-10 clr-red"></i>
											</a>
                                            <? }else{ ?>
                                            <a href="?status=0&amp;uid=<?=$listRec[$e]['id'];?>">
												<i class="fas fa-check mr-10 clr-green"></i>
											</a>
                                            <? } ?>
											<a href="codeManageTds.php?id=<?=$listRec[$e]['id'];?>">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
                                            <? } 
											 if($_SESSION['act_del']==1){ ?>
											<a onclick="deleteAll(<?php echo $listRec[$e]['id'];?>,'','TDS','codeManageTds.php')">
												<i class="fas fa-trash-alt clr-red mr-10"></i>
											</a>
                                            <? } ?>
										</td>
									</tr>
                                    <? }
									} ?>
									
								</tbody>
							</table>
                       </form>
						</div>
						<!-- Table -->	
						<!--========== List View ==========-->
						<? } ?>
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>