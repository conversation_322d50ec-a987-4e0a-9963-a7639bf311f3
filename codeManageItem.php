<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> particular ";
	$pagename='codeManageItem.php';
    include("inc/clsObj.php");	
	extract($_POST);	
	$objItem->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objItem->item_name=strtoupper($Item_Name);	
	$objItem->price=$Price;	
	$objItem->outsource_price=$Outsource_Price;	
	$objItem->hsnsac_code=$HSNSAC_Code;	
	$objItem->description=$Description;	
	$objItem->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objItem->insert();	
		redirect("codeManageItem.php");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objItem->update();
		 redirect("codeManageItem.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objItem->deleteSelect($chkAction);
					break;
			case 1:
					$objItem->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objItem->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageItem.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objItem->delete();
		redirect("codeManageItem.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objItem->status();
		redirect("codeManageItem.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objItem->selectRecById();						
	}	
	elseif($_GET['Item_Name']!="")
	{
		$objItem->id = $_GET['Item_Name'];
		$listRec=$objItem->selectRecById();
    }
	else
		$listRec=$objItem->select();
		
    include("html/frmManageItem.php");
 } 
?>