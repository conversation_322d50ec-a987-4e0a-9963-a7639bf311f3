<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();
?>
<style>
body{
	padding:0px;
	margin:0px;
	left:0;
	top:0;
}
#print_header{
	font-size:10px;
	font-family:Verdana, Geneva, sans-serif;
}

td
{
	font-size:11px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>

<div style="width:100%;" id="print_header">
<? 
$query = "";
if($_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$fd = explode("-",$_GET['From_Date']);
		$fdate = $fd[2]."-".$fd[1]."-".$fd[0];
		$td = explode("-",$_GET['To_Date']);
		$tdate = $td[2]."-".$td[1]."-".$td[0];
		$query.=" and `bill_date` between '".$fdate."' and '".$tdate."'";
	}
	
	if(!empty($_GET['chkServiceTax']))
		$query_tt[] = " stax_amt > 0 ";
	if(!empty($_GET['chkSBKKTax']))
		$query_tt[] = " (sbtax_amt > 0 || kkctax_amt > 0) ";
	if(!empty($_GET['chkEduTax']))
		$query_tt[] = " (edu_amt > 0 || hsedu_amt > 0) ";
	if(!empty($_GET['chkGST']))
		$query_tt[] = " (cgst_amt > 0 || sgst_amt > 0) ";
	if(!empty($_GET['chkIGST']))
		$query_tt[] = " (igst_amt > 0) ";
	
	if(count($query_tt)>0)
		$query.=" and (".implode(" or ",$query_tt).")";
	
	$query.=" and financial_year = '".$_SESSION['fyear']."'";
	
	$listRec=$objBill->selectServiceTax($query);
?>
<div style="width:1000px; margin:0 auto;">
<table width="100%" border="1" cellspacing="0" cellpadding="3" style="border-collapse:collapse; border:#CCCCCC 1px solid;">
<tr><td colspan="16" align="center" style="font-size:12px!important;">
	<strong><?=strtoupper($_SESSION['company']);?></strong><br />
      <?=$objCompany->getInvoiceHeaderByCompany($_SESSION['company']);?></td></tr>

  <tr style="background:#ccc; font-size:12px;">
    <th width="3%">Sr. No. </th>
    <th width="12%">Party</th>
	<th width="12%">GST No.</th>
	<th width="8%">Invoice No.</th>
	<th width="9%">Invoice Date</th>
	<th width="12%" align="right">Amount</th>
    <? if(!empty($_GET['chkServiceTax'])){ ?><th width="10%" align="right">Service Tax</th><? } ?>
	<? if(!empty($_GET['chkSBKKTax'])){ ?><th width="7%" align="right">Swachh Bharat Cess</th>
	<th width="8%" align="right">Krishi Kalyan Cess</th><? } ?>
	<? if(!empty($_GET['chkEduTax'])){ ?><th width="7%" align="right">Edu. Cess</th>
    <th width="5%" align="right">H.S.Edu. Cess</th><? } ?>
    <? if(!empty($_GET['chkGST'])){ ?><th width="5%" align="right">CGST (9%)</th>
    <th width="5%" align="right">SGST (9%)</th><? } ?>
    <? if(!empty($_GET['chkIGST'])){ ?><th width="5%" align="right">IGST (18%)</th><? } ?>
    <th width="3%" align="right">Total Tax</th>
    <th width="4%" align="right">Total Amount</th>
  </tr>
<?  $n=0;
	$total_amt = 0;
	$total_stax = 0;
	$total_sbtax = 0;
	$total_kkctax = 0;
	$total_eduamt = 0;
	$total_hseduamt = 0;
	$total_cgstamt = 0;
	$total_sgstamt = 0;
	$tamount = 0;
	$total_amount = 0;
	$total_tax = 0;
	$total_tax_amt = 0;
	
    if(count($listRec)>0 && $listRec!="")
	 {
      	$colorflg=0;
	   	for($e=0;$e<count($listRec);$e++)
		{
			$n++;
			$tax = 0;
			$total_amt+=$listRec[$e]['net_amount'];
			
			if($colorflg==1){ $colorflg=0;?>
			 <tr class="even" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='even'">
   			<? } ?>
                <td><?=$n;?></td>
                <td class="left-align"><? echo $objParty->getPartyName($listRec[$e]['party']); ?></td>
                <td class="left-align"><? echo $objParty->getGSTNo($listRec[$e]['party']); ?></td>
                <td class="left-align"><? echo $listRec[$e]['invoice_no']; ?></td>
                <td><? echo $listRec[$e]['bdt']; ?></td>
                <td class="right-align"><? echo number_format($listRec[$e]['net_amount'],2); ?></td>
                 <? if(!empty($_GET['chkServiceTax'])){ 
				 	$tax+=$listRec[$e]['stax_amt'];
					 $total_stax+=$listRec[$e]['stax_amt'];
				 ?><td class="right-align"><? echo number_format($listRec[$e]['stax_amt'],2); ?></td><? } 
				 if(!empty($_GET['chkSBKKTax'])){ 
				 	$tax+=($listRec[$e]['sbtax_amt']+$listRec[$e]['kkctax_amt']);
					$total_sbtax+=$listRec[$e]['sbtax_amt'];
					$total_kkctax+=$listRec[$e]['kkctax_amt'];
				?><td class="right-align"><? echo number_format($listRec[$e]['sbtax_amt'],2); ?></td>
                <td class="right-align"><? echo number_format($listRec[$e]['kkctax_amt'],2); ?></td><? } 
				if(!empty($_GET['chkEduTax'])){ 
					$tax+=($listRec[$e]['edu_amt']+$listRec[$e]['hsedu_amt']);
					$total_eduamt+=$listRec[$e]['edu_amt'];
					$total_hseduamt+=$listRec[$e]['hsedu_amt'];
				?><td class="right-align"><? echo number_format($listRec[$e]['edu_amt'],2);?></td>
		        <td class="right-align"><? echo number_format($listRec[$e]['hsedu_amt'],2);?></td><? } 
				if(!empty($_GET['chkGST'])){
				$tax+=($listRec[$e]['cgst_amt']+$listRec[$e]['sgst_amt']);
				$total_cgstamt+=$listRec[$e]['cgst_amt'];
				$total_sgstamt+=$listRec[$e]['sgst_amt'];
				?><td class="right-align"><? echo number_format($listRec[$e]['cgst_amt'],2);?></td>
			    <td class="right-align"><? echo number_format($listRec[$e]['sgst_amt'],2);?></td><? } 
				if(!empty($_GET['chkIGST'])){ 
				$tax+=$listRec[$e]['igst_amt'];
				$total_igstamt+=$listRec[$e]['igst_amt'];
				?><td class="right-align"><? echo number_format($listRec[$e]['igst_amt'],2);?></td><? }
				//=======calculate all total amount========
				$total_tax = $tax;
				$total_tax_amt+=$total_tax;
				$tamount=$listRec[$e]['net_amount']+$tax;
				$total_amount+=$tamount;
				//==========end of total calculation amount=========
				 ?>
			    <td class="right-align"><? echo number_format($total_tax,2);?></td>
			    <td class="right-align"><? echo number_format($tamount,2);?></td>
			 </tr>
	  <? } ?>
  <tr style="background:#333333; color:#FFFFFF; height:30px;"><td colspan="5" style="font-weight:bold;">Total Amount : </td>
    <td class="right-align" style="font-weight:bold;"><?=number_format($total_amt,2);?></td>
    <? if(!empty($_GET['chkServiceTax'])){ ?><td class="right-align" style="font-weight:bold;"><?=number_format($total_stax,2);?></td><? } 
	if(!empty($_GET['chkSBKKTax'])){ ?>    
    <td class="right-align" style="font-weight:bold;"><?=number_format($total_sbtax,2);?></td>
    <td class="right-align" style="font-weight:bold;"><?=number_format($total_kkctax,2);?></td>
    <? } 
	if(!empty($_GET['chkEduTax'])){ ?>
    <td class="right-align" style="font-weight:bold;"><?=number_format($total_eduamt,2);?></td>
  <td class="right-align" style="font-weight:bold;"><?=number_format($total_hseduamt,2);?></td><? } 
  if(!empty($_GET['chkGST'])){ ?><td class="right-align" style="font-weight:bold;"><?=$total_cgstamt;?></td>
  <td class="right-align" style="font-weight:bold;"><?=$total_sgstamt;?></td><? } 
  if(!empty($_GET['chkIGST'])){ ?>
  <td class="right-align" style="font-weight:bold;"><?=$total_igstamt;?></td><? } ?>
  <td class="right-align" style="font-weight:bold;"><?=number_format($total_tax_amt,2);?></td>
  <td class="right-align" style="font-weight:bold;"><? echo number_format($total_amount,2);?></td>
  </tr> 
  <?
  } else { ?>
  		<tr>
        	<td colspan="20" align="center">
            	No Records Found...</td>
		</tr>
    <?php }?>    
  </table>
</div>

</div>
<script language="javascript">
	window.print();
</script>
<? // } ?>