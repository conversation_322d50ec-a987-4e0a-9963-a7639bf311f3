<?	session_start(); 
	error_reporting(0);
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?=$_SESSION['company'];?></title>
</head>
<?
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();	

	$objCash->id=$_GET['id'];
	$listEdit=$objCash->selectRecById();

	$objCash->cid = $listEdit[0]['id'];
	$listItems=$objCash->selectRecById();
	//echo count($listItems);

function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }

?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>
<body>
<div style="width:100%; margin-top:60px;" id="print_header">
<div style="width:600px; margin:0 auto;">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
<tr>
  <td>
<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">

  <tr class="toprow">
    <td align="center" valign="top">
    <strong style="font-size:20px;"><?=strtoupper($_SESSION['company']);?></strong><br />
      <?=$objCompany->getInvoiceHeaderByCompany($listEdit[0]['company']);?>
      </td>  
</tr>
  
  <tr class="toprow">
    <td style="text-align:center; font-size:25px; font-weight:bold; height:50px;" valign="middle">Payment Receipt</td>
    </tr>
  </table>
</td></tr>
<tr><td style="border:#CCCCCC 1px solid; padding:10px;">
    <table width="100%" style="margin-top:20px;">
    <tr>
    <td align="left"><strong>Receipt No. : <?=$listEdit[0]['receipt_no'];?></strong></td>
    <td align="right"><strong>Date : <?=$listEdit[0]['trn_dt'];?></strong></td>
    </tr>
    <tr>
    <td colspan="2" style="line-height:22px;">
	<? $objParty->id=$listEdit[0]['party'];
       $partydet=$objParty->selectRecById(); 
	
	if($listEdit[0]['credit_amount'] > 0){
		$amount = $listEdit[0]['credit_amount'];
		$credit_debit = 0;
	}
	elseif($listEdit[0]['debit_amount'] > 0){
		$amount = $listEdit[0]['debit_amount'];
		$credit_debit = 1;
	}
	
	if($listEdit[0]['transaction_type']=="Cash")
		$payment_msg = "Cash dated : ".$listEdit[0]['trn_dt'];
	elseif($listEdit[0]['transaction_type']=="Cheque")
		$payment_msg = "Cheque No. : ".$listEdit[0]['cheque_no']." dated : ".$listEdit[0]['chq_dt']; 
	else
		$payment_msg = "Payment with ".$listEdit[0]['transaction_type']."  dated : ".$listEdit[0]['trn_dt'];
?>		
    <p>&nbsp;</p>
    <p>Received with thanks from <strong><u><?=$partydet[0]['party_name'];?></u></strong>  as sum of Rs. <?=$amount;?> (Rupees  <?=ucwords(no_to_words($amount));?> Only) as an Payment for "<?=$listEdit[0]['transaction_detail'];?>" <?=($listEdit[0]['transaction_type']=="Cheque") ? "through  ".$listEdit[0]['cheque_detail'] : "";?> vide <?=$payment_msg;?> favoring M/s. <?=strtoupper($_SESSION['company']);?></p>
    <p><strong>Rs. <?=$amount;?><br/>
    Amount in words : Rupees <?=ucwords(no_to_words($amount));?> Only
    </strong></p>
    </td>
    </tr>
    </table>
</td></tr>

<tr>
<td>
<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;">
  <tr>
    <td valign="top" style="font-size:12px;">
      <p align="right">For, <?=strtoupper($_SESSION['company']);?></p>
      <br/><br/><br/><br/><br/><br/><br/><br/><br/>
      <p align="right">AUTHORISED SIGNATORY</p></td>
    </tr>
</table>
<br/>
<span style="display:block; text-align:center;">This is a Computer Generated Receipt</span>
</td>
</tr>
</table>
</div>
</div>
<script language="javascript">
	window.print();
</script>
</body>
</html>