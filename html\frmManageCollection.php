<script src="js/jquery.table2excel.js"></script>
<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Collection Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
<script language="javascript">
function printparty()
{
/*	var pname = document.getElementById("Party_Name").value; */
	var fdate = document.getElementById("From_Date").value;
	var tdate = document.getElementById("To_Date").value; 
//	var month = document.getElementById("month").value;
	var ptype = document.getElementById("Payment_Type").value;
	window.open(
'<?=BASE_URL;?>print_collection_details.php?ptype='+ptype+'&From_Date='+fdate+'&To_Date='+tdate,'_blank'
	  // http://erp.erpdemocompany123.com/demo 
	  // <- This is what makes it open in a new window.
	);
}
</script>
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form action="#">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-2">
									<label>Search Type</label>
									<select class="form-control col-sm-12" name="Payment_Type" id="Payment_Type" onchange="checkPaytype(this)">
										<option value="">All</option>
                                        <option value="Cash" <?=($_GET['Payment_Type']=="Cash") ? "selected" : "";?>>Cash</option>
                                        <option value="Cheque" <?=($_GET['Payment_Type']=="Cheque") ? "selected" : "";?>>Cheque</option>
                                        <option value="NEFT" <?=($_GET['Payment_Type']=="NEFT") ? "selected" : "";?>>NEFT</option>
                                        <option value="IMPS" <?=($_GET['Payment_Type']=="IMPS") ? "selected" : "";?>>IMPS</option>
                                        <option value="RTGS" <?=($_GET['Payment_Type']=="RTGS") ? "selected" : "";?>>RTGS</option>
                                        <option value="UPI" <?=($_GET['Payment_Type']=="UPI") ? "selected" : "";?>>UPI</option>
                                        <option value="Paytm" <?=($_GET['Payment_Type']=="Paytm") ? "selected" : "";?>>Paytm</option>
                                        <option value="PhonePe" <?=($_GET['Payment_Type']=="PhonePe") ? "selected" : "";?>>PhonePe</option>
                                        <option value="PayPal" <?=($_GET['Payment_Type']=="PayPal") ? "selected" : "";?>>PayPal</option>
									</select>
								</div>
								<div class="form-group col-md-3">
									<label>From Date</label>
									<input type="text" name="From_Date" id="From_Date" value="<?=$_REQUEST['From_Date'];?>"  class="form-control dd">
								</div>
								<div class="form-group col-md-3">
									<label>To Date</label>
									<input type="text" name="To_Date" id="To_Date" size="10" value="<?=$_REQUEST['To_Date'];?>" class="form-control dd">
								</div>
								<div class="form-group col-md-4">
									<label>&nbsp;</label><br>
									<button type="submit" name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
									<? if($_SESSION['act_prn']==1){ ?>
                                    <a href="javascript:void(0)" onclick="javascript:printparty()"  class="btn btn-info btn-sm has-ripple">Print</a><? } ?>
									<a href="javascript:void(0)" onclick="javascript:document.location='codeManageCollection.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
									<?php /*?><a href="#" class="btn btn-info btn-sm has-ripple">Export to Excel</a><?php */?>
                                    <? if($_SESSION['act_prn']==1){ ?>
                                    <a class="btn btn-info btn-sm has-ripple" style="text-decoration:none;" id="download">Export to Excel</a><? } ?>
                                    
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive table-responsive">
                        
                        <div style="clear:both;"></div>
                        <form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                         <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
							<table class="table table-striped table-bordered nowrap table2excel tabular" id="tblCollection">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Party</th>
										<th>Date</th>
                                        <th>Credit Amount</th>
									</tr>
								</thead>
								<tbody>
                                	 <?	if(count($listRec)>0){
											$total=0;
											for($e=0;$e<count($listRec);$e++){ ?>
                                            <tr>
                                                <td><?=$e+1;?></td>
                                                <td><?	$objParty->id = $listRec[$e]['party'];
														$partynm = $objParty->selectRecById();
														echo $partynm[0]['party_name']; ?>
                                                </td>
                                                <td><?=$listRec[$e]['tdt'];?></td>
                                                <td style="text-align:right;"><?=number_format($listRec[$e]['credit_amount'],2);?></td>
                                            </tr>
                                    <? 	$total+=$listRec[$e]['credit_amount'];
										}
									} ?>
									
								</tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-right">
                                            <strong>Total Amount</strong>
                                        </td>
                                        <td class="text-right"><strong><?=$total;?></strong></td>
                                    </tr>
                                </tfoot>
							</table>
                            </form>
						</div>
						<!-- Table -->		
						<!--========== List View ==========-->						
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>
<script>
var $j = jQuery.noConflict();
$j(document).ready(function () {
	$j('#download').click(function() {
		$j(".table2excel").table2excel({
			exclude: ".noExl",
			name: "Excel Document Name",
			filename: "Collection_Report",
			fileext: ".xls",
			exclude_img: true,
			exclude_links: false,
			exclude_inputs: true
		});
	});
});
</script>
<script>
	setup();
</script>