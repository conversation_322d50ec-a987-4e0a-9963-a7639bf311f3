<?	

$obj_admin->setRights($_SESSION['uid']);

	$_SESSION['act_view']=1;
	$_SESSION['act_add']=1;
	$_SESSION['act_edit']=1;
	$_SESSION['act_del']=1;
	$_SESSION['act_det']=1;
	$_SESSION['act_prn']=1;

	//========check action rights for current page name=========
	$pagename=getPageName(); 
	
	$_SESSION['pid']=$objAdminMenu->getIdByName($pagename); //====== current page id
	$_SESSION['act_view']=(in_array(($_SESSION['pid']),$_SESSION['uper'])) ? 1 : 0;
	$_SESSION['act_add']=(in_array(($_SESSION['pid']+1000),$_SESSION['uper'])) ? 1 : 0;
	$_SESSION['act_edit']=(in_array(($_SESSION['pid']+2000),$_SESSION['uper'])) ? 1 : 0;
	$_SESSION['act_del']=(in_array(($_SESSION['pid']+3000),$_SESSION['uper'])) ? 1 : 0;
	$_SESSION['act_det']=(in_array(($_SESSION['pid']+4000),$_SESSION['uper'])) ? 1 : 0;
	$_SESSION['act_prn']=(in_array(($_SESSION['pid']+5000),$_SESSION['uper'])) ? 1 : 0;
	//========end of rights for current page name=========

	if($_SESSION['act_view']!=1 && $_SESSION['act_add']!=1 && $_SESSION['act_edit']!=1 && $_SESSION['act_del']!=1 && $_SESSION['act_det']!=1 && $pagename!="frmMain.php" && $_SESSION['pid']!=""){ ?>
	<script>
		alert("You are not allowed to access this page");
		document.location='frmMain.php';
	</script>	
<? } ?>