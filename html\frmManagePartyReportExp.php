<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Partywise Ledger Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<!--========== List View ==========-->
							<!-- Search & Shorting -->
							<form name="frmsearch" id="frmsearch" action="" method="get">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-6">
                                	<? $listParty=$objParty->selectStatus();?>
									<label>Search Party Name :</label>
									<select class="js-example-basic-single form-control" name="Client_Name" id="Client_Name">
										<option value="">--Select Client--</option>
										<? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($_GET['Client_Name']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
									</select>
								</div>
                                <script language="javascript">
								function printparty()
								{
									var pname = document.getElementById("Client_Name").value;
									//var fdate = document.getElementById("From_Date").value;
									//var tdate = document.getElementById("To_Date").value;
									if(pname!=""){	
										window.open(
									'<?=BASE_URL;?>print_party_details.php?pid='+pname,'_blank' 
										  // http://erp.erpdemocompany123.com/demo
										  // <- This is what makes it open in a new window.
										);
									}
									else{
										alert("Please select party name");
									}
								}
								</script>
								<div class="form-group col-md-6">
									<label>&nbsp;</label><br>
									<button name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
									<a onclick="javascript:document.location='javascript:printparty()'" class="btn btn-info btn-sm has-ripple">Print</a>
									<a onclick="javascript:document.location='codeManagePartyReport.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
									<a id="download" class="btn btn-info btn-sm has-ripple">Export to Excel</a>
                                </div>
								<div class="clearfix"></div>
							</div>
							</form>	
							<!-- Search & Shorting -->

							<div class="table_border mt-20">
								<div id="print_header" style="width:100%;">
									<div style="width:600px; margin:0 auto;">
                                    <? if(isset($_GET['Client_Name'])){
											$objParty->id=$_GET['Client_Name'];
											$partydet=$objParty->selectRecById();
										?>
										<table class="table-responsive" border="0" cellpadding="0" cellspacing="0">
											<tbody>
												<tr>
													<td>
														<table width="100%" border="1" cellspacing="0" cellpadding="5">
															<tbody>
																<tr>
																	<td colspan="3" class="text-center f-18 f-bold"><?=$partydet[0]['party_name'];?></td>
																</tr>
																<tr class="toprow">
																	<td colspan="3" align="center" valign="top"><?=$partydet[0]['city'];?></td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5">
															<tbody>
																<tr style="background:#CCC;">
																	<td>
																		<strong>Sr. No.</strong>
																	</td>
																	<td>
																		<strong>Particulars</strong>
																	</td>
																	<td>
																		<strong>Date</strong>
																	</td>
																	<td>
																		<strong>Debit</strong>
																	</td>
																	<td>
																		<strong>Credit</strong>
																	</td>
																	<td>
																		<strong>Balance</strong>
																	</td>
																</tr>
                                                                
                                                                <?	
	$cfcredit = 0;
	$cfdebit = 0;
	$total_debit = 0;
	$total_credit = 0;
	$total_amount = 0;
	
	if($_GET['From_Date']=="" && $_GET['To_Date']=="")
	{
	 	$fy = explode("-",$_SESSION['fyear']);
		// $_GET['From_Date'] = "01-04-".$fy[0];
		// $_GET['To_Date'] = "31-03-".$fy[1];
		$fdate = "01-04-".$fy[0];
		$tdate = "31-03-".$fy[1];
	}
	else
	{
		$fdate = $_GET['From_Date'];
		$tdate = $_GET['To_Date'];
	}

	$fd_format = explode("-",$fdate);
	$fd = $fd_format[2]."-".$fd_format[1]."-".$fd_format[0];
	$fdtime = strtotime($fd);
	// echo $fdtime."<br/>";

	$td_format = explode("-",$tdate);
	$td = $td_format[2]."-".$td_format[1]."-".$td_format[0];
	$tdtime = strtotime($td);
	// echo $tdtime."<br/><br/>";

$currency_symbol = "";
//=========calculation for carry forward amount===========
	$objBill->bdate=$fd;
	$objBill->party=$_GET['Client_Name'];
	$bd=$objBill->selectSumByPartyBeforeDate();
//	echo $bilDate[0]['totamt']."<br/>";
	
	if(count($bd) > 0)
		$currency_symbol = $objCrn->getCurrencySymbol($bd[0]['currency']);
		
//	$objPurchase->bdate=$fd;
//	$objPurchase->party=$_GET['Client_Name'];
//	$pd=$objPurchase->selectSumByPartyBeforeDate();
//	echo $purchaseDate[0]['totamt']."<br/>";
	
	$objCash->bdate=$fd;
	$objCash->party=$_GET['Client_Name'];
	$cd=$objCash->selectSumByPartyBeforeDate();
//	echo $cashDate[0]['credit_amount']."-".$cashDate[0]['debit_amount']."<br/>";
	if(count($cd)>0 && $currency_symbol=="")
		$currency_symbol = $objCrn->getCurrencySymbol($cd[0]['currency']);

	$objTds->bdate=$fd;
	$objTds->party=$_GET['Client_Name'];
	$tds=$objTds->selectSumByPartyBeforeDate();
	if(count($tds) > 0 && $currency_symbol=="")
		$currency_symbol = $objCrn->getCurrencySymbol($tds[0]['currency']);
	
	$final_amount = $bd[0]['totamt'] - $pd[0]['totamt'] - ($cd[0]['credit_amount'] - $cd[0]['debit_amount'])-$tds[0]['credit_amount'];
//	echo $final_amount;

	if($final_amount <= 0) 
		$cfcredit=-($final_amount); 	
	else
		$cfdebit=$final_amount; 	
		
//==============end of carry forward amount===============	

	//======select transaction date from bill master===
	$objBill->party=$_GET['Client_Name'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `bill_date` between '".$fd."' and '".$td."'";
		$bilDate=$objBill->selectSearchDateByParty($query);
	}
	else
		$bilDate=$objBill->selectDateByParty();

	for($b=0;$b<count($bilDate);$b++)
	{
		$adt[] = $bilDate[$b]['tdate'];
	}

	//======select transaction date from purchase master===
	/* $objPurchase->party=$_GET['Client_Name'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `date` between '".$fd."' and '".$td."'";
		$purchaseDate=$objPurchase->selectSearchDateByParty($query);
	}
	else
		$purchaseDate=$objPurchase->selectDateByParty();
	
	
	for($p=0;$p<count($purchaseDate);$p++)
	{
		$adt[] = $purchaseDate[$p]['tdate'];
	} */
	
	//======select transaction date from Cashbook===
	$objCash->party=$_GET['Client_Name'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `transaction_date` between '".$fd."' and '".$td."'";
		$cashDate=$objCash->selectSearchDateByParty($query);
	}
	else
		$cashDate=$objCash->selectDateByParty();
	
	for($c=0;$c<count($cashDate);$c++)
	{
		$adt[] = $cashDate[$c]['tdate'];
	}
	
	//======select transaction date from TDS===
	$objTds->party=$_GET['Client_Name'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `transaction_date` between '".$fd."' and '".$td."'";
		$tdsDate=$objTds->selectSearchDateByParty($query);
	}
	else
		$tdsDate=$objTds->selectDateByParty();
	
	for($t=0;$t<count($tdsDate);$t++)
	{
		$adt[] = $tdsDate[$t]['tdate'];
	}

	for($a=0;$a<count($adt);$a++) 
	{
		$dt_format = explode("-",$adt[$a]);
		$dl = $dt_format[0]."-".$dt_format[1]."-".$dt_format[2];
			// echo $dl."<br/>";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			if(strtotime($dl)<=$tdtime && strtotime($dl)>=$fdtime)
				$datelist[$a] = strtotime($dl);
		}
		else
			$datelist[$a] = strtotime($dl);
	}

	array_multisort($datelist);
	$datelist = array_values(array_unique($datelist));

	for($d=0;$d<count($datelist);$d++)
	{
		$adate[$d] = date("Y-m-d",$datelist[$d]);
//		echo $adate[$d]."<br/>";
	}

	if($_GET['From_Date']=="" && $_GET['To_Date']=="")
	{ ?>
                                                                
                                                                
        <tr>
        <td>&nbsp;</td>
        <td>C/F</td>
        <td>&nbsp;</td>
        <td><?=$currency_symbol;?> <?=($cfdebit>0) ? number_format($cfdebit,2) : "-"; ?></td>
        <td><?=$currency_symbol;?> <?=($cfcredit>0) ? number_format($cfcredit,2) : "-"; ?></td>
        <td><? 
        if($cfdebit>0) 
        {
        echo number_format($cfdebit,2)." Dr.";
        $balance = $cfdebit;
        $total_debit+=$cfdebit;
        }
        else
        {
        echo number_format($cfcredit,2)." Cr.";
        $balance = -$cfcredit;
        $total_credit+=$cfcredit;
        }
        
        $balance = ($billRec[$b]['amount']>0) ? $balance + $billRec[$b]['amount'] : $balance;					
        ?></td>
        </tr><? } ?>
        <? for($i=0;$i<count($adate);$i++){
															   
		// echo $adate[$i]."<br/>";
		//=============check records from invoice===============
		$objBill->party=$_GET['Client_Name'];
		$objBill->tdt = $adate[$i];
		$billRec=$objBill->selectRecByPartyDate();
		for($b=0;$b<count($billRec);$b++)
		{ ?>
            <tr>
              <td width="8%" style="border-right:#999999 1px solid;"><?=$sr+=1;?></td>
              <td width="36%" style="border-right:#999999 1px solid;">
              <? echo $billRec[$b]['invoice_no']; ?>              </td>
              <td width="15%" style="border-right:#999999 1px solid;"><?=$billRec[$b]['bdt'];?></td>
              <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($billRec[$b]['amount']>0) ? $billRec[$b]['amount'] : "-";
              $total_debit+=$billRec[$b]['amount'];
              ?></td>
              <td width="13%" style="border-right:#999999 1px solid; text-align:right;">-</td>
              <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
              <? $balance = ($billRec[$b]['amount']>0) ? $balance + $billRec[$b]['amount'] : $balance; 
                if($balance >= 0) 
                    echo $balance." Dr.";
                else
                    echo -($balance)." Cr.";
              ?>              </td>
            </tr>
<? 		}
//====================end of checking bill invoice========================

		/* //=============check records from purchase===============
		$objPurchase->party=$_GET['Client_Name'];
		$objPurchase->tdt = $adate[$i];
		$purchaseRec=$objPurchase->selectRecByPartyDate();
		for($p=0;$p<count($purchaseRec);$p++)
		{ 
			$sr+=1; ?>
        <tr>
          <td width="8%" style="border-right:#999999 1px solid;"><?=$sr;?></td>
          <td width="36%" style="border-right:#999999 1px solid;">
          <? echo $purchaseRec[$p]['invoice_no']; ?>          </td>
          <td width="15%" style="border-right:#999999 1px solid;"><?=$purchaseRec[$p]['bdt'];?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;">-</td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($purchaseRec[$p]['amount']>0) ? $purchaseRec[$p]['amount'] : "-" ;
		   $total_credit+=$purchaseRec[$p]['amount'];?></td>
          <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
			<? $balance = ($purchaseRec[$p]['amount']>0) ? $balance - $purchaseRec[$p]['amount'] : $balance; 
		  	if($balance >= 0) 
				echo $balance." Dr.";
			else
				echo -($balance)." Cr.";
		  ?>          </td>
        </tr>
<? 	} */
	//============end of checking purchase invoice===============
	
	//=============check records from cashbook===============
		$objCash->party=$_GET['Client_Name'];
		$objCash->tdt = $adate[$i];
		$cbRec=$objCash->selectRecByPartyDate();

		for($c=0;$c<count($cbRec);$c++)
		{ $sr+=1; 
			if($currency_symbol=="")
				$currency_symbol = $objCrn->getCurrencySymbol($cbRec[$c]['currency']);
		?>
        <tr>
          <td width="8%" style="border-right:#999999 1px solid;"><?=$sr;?></td>
          <td width="36%" style="border-right:#999999 1px solid;"><? 
		  if($cbRec[$c]['transaction_detail']!="") echo $cbRec[$c]['transaction_detail']."<br/>";
		  if($cbRec[$c]['transaction_type']=="Cheque" && $cbRec[$c]['cheque_no']!="")
	  			echo "Cheque : ".$cbRec[$c]['cheque_no']."<br/>".$cbRec[$c]['cheque_detail'];
		  else 
		  		echo $cbRec[$c]['transaction_type'];
		?></td>
          <td width="15%" style="border-right:#999999 1px solid;"><?=$cbRec[$c]['bdt'];?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($cbRec[$c]['debit_amount']>0) ?  $currency_symbol." ".$cbRec[$c]['debit_amount'] : "-" ;
		  $total_debit+=$cbRec[$c]['debit_amount'];
		  ?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($cbRec[$c]['credit_amount']>0) ?  $currency_symbol." ".$cbRec[$c]['credit_amount'] : "-";
		  $total_credit+=$cbRec[$c]['credit_amount'];
		  ?></td>
          <td width="15%" style="border-right:#999999 1px solid; text-align:right;"> 
          <?=$currency_symbol;?>&nbsp;
		<? $balance = $balance + $cbRec[$c]['debit_amount'] - $cbRec[$c]['credit_amount']; 
		  	if($balance >= 0) 
				echo $balance." Dr.";
			else
				echo -($balance)." Cr.";
		  ?>          </td>
        </tr>
	<? 	}
		//====================end of checking cashbook========================

	//=============check records from TDS===============
		$objTds->party=$_GET['Client_Name'];
		$objTds->tdt = $adate[$i];
		$tdsRec=$objTds->selectRecByPartyDate();

		for($t=0;$t<count($tdsRec);$t++)
		{ $sr+=1; 
			if($currency_symbol=="")
				$currency_symbol = $objCrn->getCurrencySymbol($tdsRec[$t]['currency']);	?>
        <tr>
          <td width="8%" style="border-right:#999999 1px solid;"><?=$sr;?></td>
          <td width="36%" style="border-right:#999999 1px solid;"><? 
		  if($tdsRec[$t]['transaction_detail']!="") echo $tdsRec[$t]['transaction_detail']."<br/>";
		  if($tdsRec[$t]['cheque_cash']==1 && $tdsRec[$t]['cheque_no']!="")
	  			echo "Cheque : ".$tdsRec[$t]['cheque_no']."<br/>".$tdsRec[$t]['cheque_detail'];
		  elseif($tdsRec[$t]['cheque_cash']==0)
				echo "TDS";?></td>
          <td width="15%" style="border-right:#999999 1px solid;"><?=$tdsRec[$t]['bdt'];?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($tdsRec[$t]['debit_amount']>0) ? $currency_symbol." ".$tdsRec[$t]['debit_amount'] : "-" ;
		  $total_debit+=$tdsRec[$t]['debit_amount'];
		  ?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($tdsRec[$t]['credit_amount']>0) ? $currency_symbol." ".$tdsRec[$t]['credit_amount'] : "-";
		  $total_credit+=$tdsRec[$t]['credit_amount'];
		  ?></td>
          <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
          <?=$currency_symbol;?>&nbsp;
		<? $balance = $balance + $tdsRec[$t]['debit_amount'] - $tdsRec[$t]['credit_amount']; 
		  	if($balance >= 0) 
				echo $balance." Dr.";
			else
				echo -($balance)." Cr.";
		  ?>          </td>
        </tr>
	<? 	}
		//====================end of checking cashbook========================
} ?>
        <tr class="toprow">
            <td colspan="3" class="text-right">
                <strong>Total Amount :</strong>
            </td>
            <td class="text-right">
                <strong><?=$currency_symbol;?>&nbsp;<?=number_format($total_debit,2);?></strong>
            </td>
            <td class="text-right"><strong>
<?=$currency_symbol;?>&nbsp;<?=number_format($total_credit,2);?></strong></td>
            <td class="text-right">
                <strong><?=$currency_symbol;?>&nbsp;<? 
$total_amount = ($total_debit>$total_credit) ? $total_debit-$total_credit : $total_debit-$total_credit ;

if($total_debit >= $total_credit) 
echo number_format($total_amount,2)." Dr." ;
else
echo number_format(-($total_amount),2)." Cr.";
?>
</strong> 
            </td>
        </tr>
															</tbody>
														</table>
													</td>
												</tr>
											</tbody>
										</table>
                                        <? } ?>
									</div>
								</div>
							</div>	
						<!--========== List View ==========-->
                    </div>
                </div>
            </div>
        </div>		
	</div>
</div>