<?php
$mess = array(
"invalid" => "Invalid Username / Password! <br/>",
"add" => "Record Added sucsessfully!",
"del" => "Record has been deleted!",
"edit" => "Record Updated successfully!",
"status"=>"Staus Updated Successfully!",
"seq"=>"Sequence Updated Successfully!",
"errn" => "Error Occured, Please Try Again !",
"que" => "Your Query has been sent!",
"logout" => "Bye Bye...... See You Soon",
"login" => "You have Log In successfully.",
"sameuser" => "Please enter another username name.",
"logfirst" => "Login First !",
"nlog" => "Now you can Login !",
"chp" => "Your Password has been changed !",
"emhrgs" => "Two Users cannot have the same E-Mail Address !",
"unalrex" => "Please Select another Login Name !",
"reg" => "Well Done ! You are now a Member...... You have Mail",
"chpsucc"=>"Password has been changed",
"credtadd"=>"Credit has been added to your account",
"thnkfpay"=>"Thank you for Payment Your",
"buyfree"=>"Freebi has been sent to you!!",
"norecord"=>"No Record found for this email address, Please Enter valid Email Address",
"passsent"=>"Password has been sent to your email address",
"regpay"=>"You are registered successfully. To active your account make payment. For payment click on payment button",
"regconf"=>"You have registered successfully, To access your account go to My Account",
"lowage"=>"Sorry,this site is for users of 18+ years.",
"signup"=>"You are sign up successfully",
"samepwd"=>"Your Current Password is same as Old Password",
"defimg"=>"You Can not Delete Default Image",
"maximg"=>"You can not Upload More Than Four Images",
"varifyemail"=>"We have sent an Email to your email ID please check your Email address to activate you account.",
"lnvpwd" => "Invalid Current Password! <br/>",
"lnvitefriend" => "An email has been sent to your friend to join MyHotz.com <br /><a href='invitefriend.php'>click here</a> to invite another friend.<br/>",
"msgsent" => "Message Sent... <br/>",
"notlogin" => "Please Login or Signup to Use All Features...<br/>",
"msgsent1" => "Your Message is Sent.....<br/>",
"notreg" => "You Are Not Authorised To Send Message. Please Sign up/Login To Use All Features.<br/>",
"nsent" => "Newsletter has been sent.<br/>"

);

?>