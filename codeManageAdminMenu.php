<?php 
include("template.php");
function main()	
	{
		$heading="Manage Admin Menu";
		$pageName="codeManageAdminMenu.php";	
		include("inc/clsObj.php");			
		JscriptDeleteVerification("codeManageAdminMenu.php");		
		extract($_POST);
		$objAdminMenu->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id']:$hid)) ;
				$objAdminMenu->adminMenuSubId=$optMenuPosition;
				$objAdminMenu->adminMenuName=$txtMenuName;
				$objAdminMenu->status=isset($_GET['status']) ? $_GET['status'] : 1;
				$objAdminMenu->adminMenuLink=$txtMenuLink;
				if($txtSeqNo=="")
				 	  $objAdminMenu->adminMenuSeqNo=0;		
				else	  
					   $objAdminMenu->adminMenuSeqNo=$txtSeqNo;
					   
			   $objAdminMenu->adminRights=(!empty($chkAdmin)) ? 1 : 0;
			   
			   // echo $objAdminMenu->adminRights; die;
			   		
		if(isset($_POST['btnAdd']))
			{		
				$objAdminMenu->insert();
				redirect("codeManageAdminMenu.php?msg=add");
			}
		if(isset($_POST['btnUpdate']))			
			{		  
				$objAdminMenu->update();
				redirect("codeManageAdminMenu.php?msg=edit");
			}
		if(isset($_POST['btnAction']))
		{
		 extract($_POST);
		 switch($optAction)
		  {
		  	case 0:
					$objAdminMenu->deleteSelect($chkAction);
    				redirect("codeManageAdminMenu.php?msg=del");
					break;
			case 1:
			 		$objAdminMenu->statusUpdatePublish($chkAction);
         			redirect("codeManageAdminMenu.php?msg=Publish");
			        break;
			case 2:
			        $objAdminMenu->statusUpdateUnPublish($chkAction);
					redirect("codeManageAdminMenu.php?msg=UnPublish");
					break;
			case 3:					
					for($i=0;count($categoryId)>$i;$i++)
					{
						$objAdminMenu->id=$categoryId[$i];
						$objAdminMenu->adminMenuSeqNo=$txtSeqNo[$i];
						$objAdminMenu->sequenceUpdate();									
					}	
					
				 redirect("codeManageAdminMenu.php?msg=seq");	
		  } 		  

		}				
		if(isset($_GET['status']))
			{					
				$objAdminMenu->status();
				redirect("codeManageAdminMenu.php?msg=status");
			 } 
		if(isset($_GET['delete']))
			{
					$objAdminMenu->delete();
					redirect("codeManageAdminMenu.php?msg=del");
					
			}
		if(isset($_GET['id']))
			{					
					$menuCatListEdit=$objAdminMenu->selectRecById();
					$objAdminMenu->id=$menuCatListEdit[0]['adminMenuSubId'];	
					$menuCatListEditId=$objAdminMenu->selectRecById();					
			}
		$menuMainCatList=$objAdminMenu->menuCategoryList();
		$menuMainCatListRec=$objAdminMenu->menuCategoryListRec();
		include("html/frmManageAdminMenu.php");	
	}
?>