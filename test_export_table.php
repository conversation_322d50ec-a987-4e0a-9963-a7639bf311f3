<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script>
<script src="js/jquery.table2excel.js"></script>
<table id="simpletable" class="table table-striped table-bordered nowrap table2excel tabular">
    <thead>
        <tr>
            <th>Sr. No.</th>
            <th>Party</th>
            <th>Credit Amount</th>
            <th>Date</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th>1</th>
            <th>dkljflkdjf</th>
            <th>1000</th>
            <th>20-06-2020</th>
        </tr>
        <tr>
            <th>2</th>
            <th>dkjfkld dkljflkdjf</th>
            <th>2000</th>
            <th>20-06-2020</th>
        </tr>
        <tr>
            <th>3</th>
            <th>kjflkd dkljflkdjf</th>
            <th>3000</th>
            <th>20-06-2020</th>
        </tr>
        <tr>
            <th>4</th>
            <th>dfldkfj</th>
            <th>4000</th>
            <th>20-07-2020</th>
        </tr>
        <tr>
            <th>5</th>
            <th>kdkfjljl llljkljklj</th>
            <th>5000</th>
            <th>30-08-2020</th>
        </tr>
    </tbody>
</table>
<a class="btn btn-info btn-sm has-ripple" style="text-decoration:none; cursor:pointer;" id="download">Export to Excel</a>
<script>
var $j = jQuery.noConflict();
$j(document).ready(function () {
	$j('#download').click(function() {
		$j(".table2excel").table2excel({
			exclude: ".noExl",
			name: "Excel Document Name",
			filename: "Collection_Report",
			fileext: ".xls",
			exclude_img: true,
			exclude_links: false,
			exclude_inputs: true
		});
	});
});
</script>
<script>
	setup();
</script>