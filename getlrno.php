<?
    session_start();  	
	include("inc/fileInclude.php"); 
	include("inc/clsObj.php"); 

	$objBill->company=$_GET['cid'];
	$objBill->item=$_GET['itemid'];
	$aqty = 0;
	$sqty = 0;

	if(isset($objBill->company))
		$billRec=$objBill->selectDistLrByCompanyItem();	

/*	$avail_qty = $billRec[0]['aqty']; 
	//========check total available qty from company and item======
	$current_cart=$_SESSION['cart'];
	for($i=0;$i<count($current_cart[0]);$i++)
	{
		if($_GET['cid'] == $current_cart[0][$i] && $_GET['itemid'] == $current_cart[1][$i])
			$sale_qty+=$current_cart[4][$i];
	}
	//=========end of checking items===========  */
?>
	obj.options[obj.options.length] = new Option('--Select LR No--','');
<?	for($i=0;$i<count($billRec);$i++) 
	{ ?>
		obj.options[obj.options.length] = new Option('<?php echo $billRec[$i]['bno'];?>','<?php echo $billRec[$i]['bid'];?>');
<?	}  ?>