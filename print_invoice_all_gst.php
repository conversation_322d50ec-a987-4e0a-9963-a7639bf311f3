<?	session_start(); 
	error_reporting(0);
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>ERP Demo</title>
</head>
<?
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();	
		
//	$objBill->from_srno = $_GET['from_srno']-1;
//	$objBill->to_srno = $_GET['to_srno']-($_GET['from_srno']-1);
//	$listIds = $objBill->selectItemsByRecords();	


		$query = "";
		if($_GET['Invoice_Number']!="")
			$query.=" and invoice_no='".$_GET['Invoice_Number']."'";
		if($_GET['Party_Name']!="")
			$query.=" and party ='".$_GET['Party_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			
			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
		
			$query.=" and bill_date between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		
		$query.=" and bill_type!='' ";
		
		$listIds=$objBill->selectItemsByQuery($query);

?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
.tbl_invoice_header, .tbl_invoice_header td{
	border:none!important;
}

.tbl_invoice td{
	font-size:13px!important;
	padding:3px!important;
}

.tbl_invoice td ul, .tbl_invoice td ol{
	padding:0px 20px!important;
	margin-top:0px!important;
}

.tbl_invoice td ul li, .tbl_invoice td ol li{
	font-size:12px!important;
}

.tbl_invoice td strong{
	font-size:13px!important;
}

table {
    border:solid #999 !important;
    border-width:1px !important;
}
th, td {
    border:solid #999 !important;
    border-width:1px !important;
}
p{
	margin:0px;
	padding:0px;
}
</style>
<body>
<?	

for($n=0;$n<count($listIds);$n++)
{
	$objBill->id=$listIds[$n]['id'];
	$listEdit=$objBill->selectRecById();

	$objBill->bid = $listEdit[0]['id'];
	$listItems=$objBill->selectItemsByBillNo();	
	
	$objParty->id=$listEdit[0]['party'];
    $partydet=$objParty->selectRecById();
?>
<div style="width:100%; margin-top:60px;" id="print_header">
<div style="width:810px; margin:0 auto;">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
<tr><td>
<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;" class="tbl_invoice">
<tr>
<td colspan="3" style="text-align:center;font-size:25px!important; font-weight:bold; height:50px;">Tax Invoice<br/>
<strong style="float:right;">ORIGINAL COPY</strong></td>      
      </tr>
  <tr class="toprow">
    <td width="34%" rowspan="3" align="left" valign="top">
    <strong>ERP Demo</strong><br />
      <? echo $objCompany->getInvoiceHeaderByCompany($listEdit[0]['company']);
	  	if($objCompany->getGstinByCompany($listEdit[0]['company'])!="")
      		echo "GSTIN : ".$objCompany->getGstinByCompany($listEdit[0]['company'])."<br/>";
		if($objCompany->getPannoByCompany($listEdit[0]['company'])!="")
      		echo "PAN No. : ".$objCompany->getPannoByCompany($listEdit[0]['company']); ?>   	</td>  
    <td align="left" valign="top">Invoice No. :<br/><strong><?=$listEdit[0]['invoice_no'];?></strong></td>
    <td width="27%" align="left" valign="top">Invoice Date :<br/><strong><?=$listEdit[0]['bdt'];?></strong>    </td>
    </tr>
  <tr class="toprow">
    <td align="left" valign="top">PO/PI No :<br/><strong><?=$listEdit[0]['po_no'];?></strong></td>
    <td align="left" valign="top">Mode/Terms of Payment :<br/><strong>7 Days</strong></td>
  </tr>
  
  <tr class="toprow">
    <td align="left" valign="top" width="25%">Place of Supply :<br/><strong><?=$partydet[0]['city'];?></strong></td>
    <td align="left" valign="top">Date of Supply :<br/><strong><?=$listEdit[0]['bdt'];?></strong></td>
  </tr>
  <tr><td colspan="3" style="text-align:center;"><strong>Bill to Party</strong></td></tr>
  <tr class="toprow"><td colspan="3" align="left" valign="top"><strong>Name : <?=$partydet[0]['party_name'];?></strong></td></tr>
   	<tr class="toprow"><td colspan="3" align="left" valign="top"><strong>Address : </strong><?=$partydet[0]['party_address'].", ".$partydet[0]['city'].", ".$objState->getNameById($partydet[0]['state']);?> </td></tr>
    <tr class="toprow"><td colspan="3" align="left" valign="top"><strong>GSTIN : </strong><?=$partydet[0]['gstin'];?></td></tr>
	
  <?php /*?><tr class="toprow">
    <td colspan="3" align="left" valign="top">&nbsp;</td>
    </tr><?php */?>
  </table>
</td></tr>

<tr>
<td>
<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;" class="tbl_invoice">
    <tr>
      <td style="border-right:#999999 1px solid;" width="4%" align="center"><strong>No.</strong></td>
      <td style="border-right:#999999 1px solid;" width="22%" align="center" colspan="4"><strong>Product Description</strong></td>
      <td style="border-right:#999999 1px solid;" width="5%" align="center"><strong>HSN/SAC Code</strong></td>
      <td style="border-right:#999999 1px solid;" width="5%" align="center"><strong>Qty.</strong></td>
      <td style="border-right:#999999 1px solid;" width="5%" align="center"><strong>Rate<br/>(<img src="images/inr_img.png" width="8" height="10">)</strong></td>
      <td style="border-right:#999999 1px solid;" width="8%" align="center"><strong>Amount<br/>(<img src="images/inr_img.png" width="8" height="10">)</strong></td>
      </tr>
    
  <? 
  $total_items = 0;
  $total_net_amount = 0;
  $total_discount = 0;
  $total_taxable_amt = 0;
  $total_cgst_amt = 0;
  $total_sgst_amt = 0;
  $total_amount = 0;
  
  for($i=0;$i<count($listItems);$i++) { ?>
  <tr>
  	<td style="border-right:#999999 1px solid;" align="right" valign="top"><?=$i+1;?></td>
   <td style="border-right:#999999 1px solid;"  valign="top" colspan="4">
   	<? 	$objItem->id=$listItems[$i]['item'];
		$itemdet=$objItem->selectRecById();
		
		echo "<strong>".$itemdet[0]['item_name']."</strong><br/>";
		echo $listItems[$i]['description'];
		//========Totals=
		$total_items+=$listItems[$i]['no_of_items'];
		$total_net_amount+=$listItems[$i]['amount'];
		//$total_discount+=$listItems[$i]['discount'];
		//$total_taxable_amt+=$listItems[$i]['taxable_amount'];
		//$total_cgst_amt+=$listItems[$i]['cgst'];
		//$total_sgst_amt+=$listItems[$i]['sgst'];
		//$total_amount+=$listItems[$i]['total_amount'];
	?>	</td>
    <td style="border-right:#999999 1px solid; text-align:center;"  valign="top"><?=$itemdet[0]['hsnsac_code'];?></td>
    <td style="border-right:#999999 1px solid; text-align:center;"  valign="top"><?=$listItems[$i]['no_of_items'];?></td>
    <td style="border-right:#999999 1px solid;"  align="right"  valign="top"><?=number_format($listItems[$i]['rate_per_qty'],0);?></td>
    <td style="border-right:#999999 1px solid;"  align="right"  valign="top"><?=number_format($listItems[$i]['amount'],0);?></td>
    </tr>
<? } ?>  
  <?php /*?><tr class="toprow">
    <td colspan="5" style="text-align:right;">&nbsp;</td>
    </tr><?php */?>
  
  <tr class="toprow">
    <td colspan="6" align="right"><strong>Total</strong> </td>
    <td style="text-align:center;"><?=$total_items;?></td>
    <td style="text-align:right;">&nbsp;</td>
    <td style="text-align:right;"><?=number_format($total_net_amount,2);?></td>
    </tr>
   <? if($listEdit[0]['bill_type']=="GST"){ ?>  
  <tr class="toprow">
    <td colspan="8" align="right">Add : CGST</td>
    <td style="text-align:right;"><?=number_format($listEdit[0]['cgst_amt'],2);?></td>
  </tr>
  <tr class="toprow">
    <td colspan="8" align="right">Add : SGST</td>
    <td style="text-align:right;"><?=number_format($listEdit[0]['cgst_amt'],2);?></td>
  </tr>
  <? }else{ ?>
  	<tr class="toprow">
    <td colspan="8" align="right">Add : IGST</td>
    <td style="text-align:right;"><?=number_format($listEdit[0]['igst_amt'],2);?></td>
  </tr>
  <? } ?>
  <tr class="toprow">
    <td colspan="8" align="right"><strong>Total Amount :</strong></td>
    <td style="text-align:right;"><?=number_format($listEdit[0]['amount'],2);?></td>
  </tr>
    <tr class="toprow">
    <td colspan="12" style="text-align:left;">
    <?
	echo "<strong>Total Invoice amount in words : </strong> Rs. ".ucwords(no_to_words($listEdit[0]['amount']))." Only";
?>
    </td>
	</tr>
    
<tr><td colspan="6">
	<?=$objCompany->getBankdetByCompany($listEdit[0]['company']) ?>
</td>
  <td colspan="5" align="center">Certified that the particulars given above are true and correct</td>
  </tr>
<tr><td colspan="6" valign="top">
<style>
.terms_conditions ul li{
	font-size:25px!important;
}
</style>
<strong style="font-weight:bold!important; font-size:18px!important;"><u>TERMS &amp; CONDITION ::</u></strong><br />
<div class="terms_conditions">
	<?=$listEdit[0]['invoice_terms'];?>
</div>
</td>
  <td colspan="5">
  <p align="center">For, <?=strtoupper($_SESSION['company']);?></p>
      <br/><?php /*?><p align="center"><img src="images/signature.jpg" /></p><?php */?><br/><br/><br/><br/><br/><br/><br/><br/><br/>
      <p align="center">AUTHORISED SIGNATORY</p>  </td>
  </tr>
</table></td>
</tr>
</table>
</div>
</div>
<? }

function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }


 ?>
<script language="javascript">
	window.print();
</script>
</body>
</html>