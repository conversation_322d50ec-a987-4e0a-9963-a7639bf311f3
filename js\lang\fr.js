Calendar.LANG("fr", "Français", {

        fdow: 1,                // first day of week for this locale; 0 = Sunday, 1 = Monday, etc.

        goToday : "Aujourd'hui",

        today: "Aujourd'hui",         // appears in bottom bar

        wk: "sm.",

        weekend: "0,6",         // 0 = Sunday, 1 = Monday, etc.

        AM: "am",

        PM: "pm",

        mn : [ "<PERSON><PERSON>",
               "Février",
               "Mars",
               "Avril",
               "Mai",
               "<PERSON>in",
               "Juillet",
               "Août",
               "Septembre",
               "Octobre",
               "Novembre",
               "Décembre" ],

        smn : [ "<PERSON>",
                "Fév",
                "<PERSON>",
                "Avr",
                "<PERSON>",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "<PERSON>ou",
                "Sep",
                "Oct",
                "Nov",
                "Déc" ],

        dn : [ "<PERSON><PERSON><PERSON>",
               "<PERSON><PERSON>",
               "Mar<PERSON>",
               "Mercredi",
               "<PERSON><PERSON>",
               "Vendredi",
               "<PERSON><PERSON>",
               "<PERSON>man<PERSON>" ],

        sdn : [ "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON><PERSON>",
                "<PERSON>",
                "<PERSON>" ]

});
