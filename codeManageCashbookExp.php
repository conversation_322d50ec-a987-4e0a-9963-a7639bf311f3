<?php 
include("template.php");
function main()
{
	$heading="<span>Manage</span> Payment ";
	$pageName="codeManageCashbookExp.php";
    include("inc/clsObj.php");	
	
	extract($_POST);	
	$cdate = explode("-",$Transaction_Date);
	$transaction_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }


	$objCash->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objCash->company=$_SESSION['company'];
	$objCash->financial_year=$_SESSION['fyear'];
	$objCash->receipt_no=$Receipt_No;
	$objCash->pay_type="Exp";
	$objCash->party=$Party_Name;
	$objCash->currency=$Currency;
	$objCash->currency_inr=$currency_inr;
	
	if($Credit_Debit==0)
	{
		$objCash->credit_amount=$Amount;
		$objCash->debit_amount=0;
	}
	elseif($Credit_Debit==1)
	{
		$objCash->credit_amount=0;
		$objCash->debit_amount=$Amount;
	}
	$objCash->applicable_tax=$Applicable_Tax;
	$objCash->transaction_type=$Transaction_Type;
	$objCash->ref_no=$Reference_No;
	$objCash->cheque_no=$Cheque_No;
	
	$chqdate = explode("-",$Cheque_Date);
	$chequedate=$chqdate[2]."-".$chqdate[1]."-".$chqdate[0];
	$objCash->cheque_date=$chequedate;
	$objCash->cheque_detail=$Cheque_Detail;
	$objCash->cheque_cleared=(!empty($Cheque_Cleared)) ? 1 : 0;
	$objCash->transaction_detail=$Transaction_Detail;
	$transdate=explode("-",$Transaction_Date);
	$objCash->transaction_date=$transdate[2]."-".$transdate[1]."-".$transdate[0];
	if(isset($_POST['btnAdd']))
	{
		$cid = $objCash->insert();
		//=============update last bill no in increment master======
		$rno = explode("/",$Receipt_No);
		$objYear->company=$_SESSION['company'];
		$objYear->financial_year=$_SESSION['fyear'];
		$objYear->lcno = $rno[3];
		$objYear->updateCompanyCashbookNo();
		//========end of last updated bill no.================
		redirect("codeManageCashbookExp.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objCash->update();
		redirect("codeManageCashbookExp.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objCash->deleteSelect($chkAction);
				break;
			case 1:
				$objCash->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objCash->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
					
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManageCashbookExp.php?msg=edit");
	}	


	if(isset($_GET['pid']))
	{
	
		//extract($_POST);
						$id = $_GET['pid'];
						
						//##################################################################
						//      send email function by invoice number 
						//##################################################################
					
							$objCash->id=$_GET['pid'];
							$listEdit=$objCash->selectRecById();
						
							$objCash->cid = $listEdit[0]['id'];
							$listItems=$objCash->selectRecById();		
						
					$msg.='<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">
  <tr class="toprow">
    <td align="center" valign="top">
    <strong style="font-size:20px;">'.$_SESSION['company'].'</strong><br />
      304-A, Ashoka, Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
      Ahmedabad-380014<br />
      Ph: +91-79-40391397<br />E-Mail: ';
      $msg.='<a href="mailto:<EMAIL>"><EMAIL></a><br />
      Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a><br/>';
     
	    $msg.='<strong>PAN No. : </strong>'.$objCompany->getPannoByCompany($listEdit[0]['company']);
		
		 $msg.='&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>GSTIN : </strong>'.$objCompany->getGstinByCompany($listEdit[0]['company']);
		
	   $msg.='</td>  
</tr>
<tr><td style="text-align:center; font-size:25px; font-weight:bold; height:50px;" valign="middle">Payment Receipt</td></tr>
<tr><td>
    <table width="100%" style="margin-top:20px;">
    <tr>
    <td align="left"><strong>Receipt No. ';
	$msg.='TS/RC/'.$_GET['pid'].'</strong></td>
    <td align="right"><strong>Date: - '.$listEdit[0]['trn_dt'].'</strong></td>
    </tr>
    <tr>
    <td colspan="2" style="line-height:22px;">';
	$objParty->id=$listEdit[0]['party'];
            $partydet=$objParty->selectRecById(); 
	
	if($listEdit[0]['credit_amount'] > 0)
	{
		$amount = $listEdit[0]['credit_amount'];
		$credit_debit = 0;
	}
	elseif($listEdit[0]['debit_amount'] > 0)
	{
		$amount = $listEdit[0]['debit_amount'];
		$credit_debit = 1;
	}
	
	if($listEdit[0]['transaction_type']=="Cash")
		$payment_msg = "Cash dated : ".$listEdit[0]['trn_dt'];
	elseif($listEdit[0]['transaction_type']=="Cheque")
		$payment_msg = "Cheque No. : ".$listEdit[0]['cheque_no']." dated ".$listEdit[0]['chq_dt']; 
	else
		$payment_msg = "NEFT dated : ".$listEdit[0]['trn_dt'];
		
    $msg.='<p>&nbsp;</p>
    <p>Received with thanks from <strong><u>'.$partydet[0]['party_name'].'</u></strong>� as sum of Rs. '.$amount.' (Rupees �'.ucwords(no_to_words($amount)).' Only) as an Payment for "'.$listEdit[0]['transaction_detail'].'" vide '.$payment_msg.' favoring M/s. '.$_SESSION['company'].'</p><p><strong>Rs. '.$amount.'<br/>
    Amount in words : �Rupees '.ucwords(no_to_words($amount)).' Only
    </strong></p>
    </td>
    </tr>
    </table>
</td></tr>
 <tr>
<td>
<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;">
  <tr>
    <td width="226" valign="top" align="right";>
      <p align="right">For, '.$_SESSION['company'].'</p>
      <p align="right"><img width="169" height="85" src="print_invoice_clip_image002.jpg" /></p>
      <p align="right">AUTHORISED    SIGNATORY</p></td>
  </tr>
</table>
</td>
</tr> 
  </table>';
					// echo $msg; die;
					
						$to=$partydet[0]['email'];
						
						$emailIds = explode(",",$to);
						for($e=0;$e<count($emailIds);$e++){
						// $to="<EMAIL>";
						// echo $emailIds[$e]."<br/>";
						$headers  = "MIME-Version: 1.0\r\n";
						$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
						$headers .= "To:$to\r\n";
						$headers .= "From:<EMAIL>\r\n";
						// mail($emailIds[$e],"Receipt Details ",$msg,$headers); 
						}
		  				// die;
		?>
        <script language="javascript">
			alert("Receipt sent to the client");
		</script>
        <? 
		redirect("codeManageCashbookExp.php?msg=edit");
	}

	if(isset($_GET['delete']))
	{	$objCash->id=$_GET['delete'];
		$objCash->deleteById();
		// $objCash->deleteItemsById();
		redirect("codeManageCashbookExp.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objCash->status();
		redirect("codeManageCashbookExp.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objCash->selectRecById();

		$objCash->cid = $listEdit[0]['id'];
		$listItems=$objCash->selectRecById();	
	}
	elseif($_GET['Client_Name']!="" || $_GET['Cheque_Number']!="" ||  ($_GET['From_Date']!="" && $_GET['To_Date']!=""  && $_GET['From_Date']!="__-__-____" && $_GET['To_Date']!="__-__-____"))
	{
		//echo "test"; die;
		$query = "";
		if($_GET['Cheque_Number']!="")
			$query.=" and cheque_no='".$_GET['Cheque_Number']."'";
		if($_GET['Client_Name']!="")
			$query.=" and party='".$_GET['Client_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!=""  && $_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
			$query.=" and `transaction_date` between '".$fdate."' and '".$tdate."'";
		}
		else{
			if($_GET['Cheque_Number']=="")
				$query.=" and financial_year='".$_SESSION['fyear']."'";
		}
		$objCash->party = $_GET['party'];
		$listRec=$objCash->selectRecBySearch($query);
    }
	else
		$listRec=$objCash->selectExp();
	include("html/frmManageCashbookExp.php");
} 
?>