<script src="js/jquery.table2excel.js"></script>
<script type="text/javascript">
function chkValidation(){
	var errors='';
	if(document.getElementById("chkGST").checked == false && document.getElementById("chkIGST").checked == false){
		errors+="- Any Tax Option Should be selected \n";
	}
	
	if(document.getElementById("From_Date").value!=""){
		var from_date =	document.getElementById("From_Date").value;
		/* if (from_date.length !== 10 || from_date.substring(2, 3) !== '-' || from_date.substring(5, 6) !== '-')					
		{ 
			from_date.value="";
			errors+='- From Date Should be valid format\n';
		}  */
	}

	if(document.getElementById("To_Date").value!=""){
		var to_date = document.getElementById("To_Date").value;
		/* if (to_date.length !== 10 || to_date.substring(2, 3) !== '-' || to_date.substring(5, 6) !== '-') { 		to_date.value="";
			errors+='- To Date Should be valid format\n';
		} */
	}

	if(document.getElementById("To_Date").value!="" && document.getElementById("From_Date").value!=""){
		var tdate = to_date.split("-");
		var str1="";
		var td = str1.concat(tdate[0],"/",tdate[1],"/",tdate[2]);
		
		var fdate = from_date.split("-");
		var str2="";
		var fd = str2.concat(fdate[0],"/",fdate[1],"/",fdate[2]);
	
		var a = new Date(td);
		var b = new Date(fd);
	
		var msDateA = Date.UTC(a.getFullYear(), a.getMonth()+1, a.getDate());
		var msDateB = Date.UTC(b.getFullYear(), b.getMonth()+1, b.getDate());
	
		if (parseFloat(msDateA) < parseFloat(msDateB)){
			errors+="- To Date should be bigger than From date \n";
			document.getElementById("To_Date").value = "";
			// document.getElementById("To_Date").focus;	  
		}  // lt
	} 
	
	if(errors!=''){
		document.getElementById("error_searchvalidation").innerHTML=errors;
		return false;
	}
	else
		document.getElementById("error_searchvalidation").innerHTML='';
	
	return true;
}
</script>
<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Invoice Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form action="#" onsubmit="return chkValidation()">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-3">
									<label>From Date</label>
									<input type="text" class="form-control dd" name="From_Date" id="From_Date" size="10" value="<?=$_REQUEST['From_Date'];?>">
								</div>
								<div class="col-sm-12 col-md-3">
									<label>To Date</label>
									<input type="text" class="form-control dd" name="To_Date" id="To_Date" size="10" value="<?=$_REQUEST['To_Date'];?>">
								</div>
								<div class="form-group col-md-1">
									<label>&nbsp;</label><br>
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="chkGST" id="chkGST" value="4" <?=(!empty($_GET['chkGST'])) ? "checked" : "";?>>
										<label class="form-check-label">GST</label>
									</div>
								</div>
								<div class="form-group col-md-1">
									<label>&nbsp;</label><br>
									<div class="form-check">
										<input class="form-check-input" type="checkbox"  name="chkIGST" id="chkIGST" value="5" <?=(!empty($_GET['chkIGST'])) ? "checked" : "";?>>
										<label class="form-check-label">IGST</label>
									</div>
								</div>
								<div class="clearfix"></div>
                                <script language="javascript">
								function printservicetax()
								{
									var fdate = document.getElementById("From_Date").value;
									var tdate = document.getElementById("To_Date").value; 
									
									window.open('<?=BASE_URL;?>print_invoice_report.php?From_Date='+fdate+'&To_Date='+tdate+'&chkServiceTax=<?=$_GET['chkServiceTax'];?>&chkSBKKTax=<?=$_GET['chkSBKKTax'];?>&chkEduTax=<?=$_GET['chkEduTax'];?>&chkGST=<?=$_GET['chkGST'];?>&chkIGST=<?=$_GET['chkIGST'];?>');
								}
								</script>
								<div class="form-group col-md-4">
									<label>&nbsp;</label><br>
									<button class="btn btn-info btn-sm has-ripple" name="search" id="search" value="Search" >
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a onclick="javascript:printservicetax()" class="btn btn-info btn-sm has-ripple">Print</a>
                                    <? } ?>
									<a onclick="javascript:document.location='codeInvoiceReport.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
                                    <? if($_SESSION['act_prn']==1){ ?>
                                    <a class="btn btn-info btn-sm has-ripple" id="download">Export to Excel</a>
                                    <? } ?>
                                </div>
								<div class="clearfix"></div>
							</div>
                            <div style="clear:both;"></div>
                            <div id="error_searchvalidation" class="err_msg"></div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive">
							<table id="tblInvoiceReport" class="table table-striped table-bordered nowrap table-responsive table2excel tabular" width="100%">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Party</th>
										<th>GST No.</th>
										<th>Invoice No.</th>
										<th>Invoice Date</th>
										<th>Amount</th>
										<th>CGST (9%)</th>
										<th>SGST (9%)</th>
										<th>IGST (18%)</th>
										<th>Total Tax</th>
										<th>Total Amount</th>
									</tr>
								</thead>
								<tbody>
                                <? if(count($listRec)>0 && $listRec!="")
									 {
										for($e=0;$e<count($listRec);$e++)
										{
											$n++;
											$tax = 0;
											$total_amt+=$listRec[$e]['net_amount'];?>
                                        <tr>
                                            <td><?=$n;?></td>
                                            <td><?=$objParty->getPartyName($listRec[$e]['party']); ?></td>
                                            <td><?=$objParty->getGSTNo($listRec[$e]['party']); ?></td>
                                            <td><?=$listRec[$e]['invoice_no']; ?></td>
                                            <td><?=$listRec[$e]['bdt']; ?></td>
                                            <td class="text-right"><? echo number_format($listRec[$e]['net_amount'],2); ?></td>
                                            <td class="text-right">
                                            <? 	$tax+=($listRec[$e]['cgst_amt']+$listRec[$e]['sgst_amt']);
												$total_cgstamt+=$listRec[$e]['cgst_amt'];
												$total_sgstamt+=$listRec[$e]['sgst_amt'];
                                            	echo number_format($listRec[$e]['cgst_amt'],2);?></td>
                                            <td class="text-right"><?=number_format($listRec[$e]['sgst_amt'],2);?></td>
                                            <td class="text-right"><?
                                            	$tax+=$listRec[$e]['igst_amt'];
												$total_igstamt+=$listRec[$e]['igst_amt'];
												echo number_format($listRec[$e]['igst_amt'],2);?>
											</td>
                                            <td class="text-right">
                                            <?	//=======calculate all total amount========
												$total_tax = $tax;
												$total_tax_amt+=$total_tax;
												$tamount=$listRec[$e]['net_amount']+$tax;
												$total_amount+=$tamount;
												//==========end of total calculation amount=========
												echo number_format($total_tax,2);?>
                                            </td>
                                            <td class="text-right"><?=number_format($tamount,2);?></td>
                                        </tr>
                                    <? } ?>
                                    	<tr>
										<td class="text-right" colspan="5"><strong>Total Invoice Amount</strong></td>
										<td class="text-right"><strong><?=number_format($total_amt,2);?></strong></td>
										<td class="text-right"><strong><?=number_format($total_cgstamt,2);?></strong></td>
										<td class="text-right"><strong><?=number_format($total_sgstamt,2);?></strong></td>
										<td class="text-right"><strong><?=number_format($total_igstamt,2);?></strong></td>
										<td class="text-right"><strong><?=number_format($total_tax_amt,2);?></strong></td>
										<td class="text-right"><strong><?=number_format($total_amount,2);?></strong></td>
									</tr>
									<? } ?>
								</tbody>
								<?php /*?><tfoot>
									<tr>
										<td colspan="10" class="text-right">
											<strong>Total Invoice Amount</strong>
										</td>
										<td class="text-right"><strong><?=number_format($total_amount,2);?></strong></td>
									</tr>
								</tfoot><?php */?>
							</table>
						</div>
						<!-- Table -->		
						<!--========== List View ==========-->
						
                    </div>
                </div>
            </div>
        </div>		
		
	</div>
</div>
<script>
var $j = jQuery.noConflict();
$j(document).ready(function () {
	$j('#download').click(function() {
		$j(".table2excel").table2excel({
			exclude: ".noExl",
			name: "Excel Document Name",
			filename: "Invoice_Report",
			fileext: ".xls",
			exclude_img: true,
			exclude_links: false,
			exclude_inputs: true
		});
	});
});
</script>