<?php 
include("template.php");
function main()
{
	$heading="<span>Manage</span> Eexpense/Purchase Report ";
    include("inc/clsObj.php");	
	
	extract($_POST);	
	$cdate = explode("-",$Transaction_Date);
	$transaction_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

	$objExpPurchase->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objExpPurchase->company=$_SESSION['company'];
	$objExpPurchase->financial_year=$_SESSION['fyear'];
	$objExpPurchase->party=$Party_Name;
	if($Credit_Debit==0)
	{
		$objExpPurchase->credit_amount=$Amount;
		$objExpPurchase->debit_amount=0;
	}
	elseif($Credit_Debit==1)
	{
		$objExpPurchase->credit_amount=0;
		$objExpPurchase->debit_amount=$Amount;
	}
	$objExpPurchase->cheque_cash=$Cash_Cheque;
	$objExpPurchase->cheque_no=$Cheque_No;
	$objExpPurchase->cheque_detail=$Cheque_Detail;
	$objExpPurchase->transaction_detail=$Transaction_Detail;
	$objExpPurchase->transaction_date=$transaction_date;
	
	if(isset($_POST['btnAdd']))
	{
		$objExpPurchase->insert();
		redirect("codeManageCashbook.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objExpPurchase->update();
		redirect("codeManageCashbook.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objExpPurchase->deleteSelect($chkAction);
				break;
			case 1:
				$objExpPurchase->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objExpPurchase->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
					
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManageCashbook.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objExpPurchase->deleteById();
		$objExpPurchase->deleteItemsById();
		redirect("codeManageCashbook.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objExpPurchase->status();
		redirect("codeManageCashbook.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objExpPurchase->selectRecById();

		$objExpPurchase->cid = $listEdit[0]['id'];
		$listItems=$objExpPurchase->selectRecById();	
	}
	elseif($_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "";
		if($_GET['Party_Name']!="")
			$query.=" and party='".$_GET['Party_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
			$query.=" and `transaction_date` between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		
		// $objExpPurchase->party = $_GET['party'];
		$listRec=$objExpPurchase->selectRecBySearch($query);
    }
	else{
			if($_GET['month']!="")
				$month=$_GET['month'];
			else	
				$month=date("m");
				
		$objExpPurchase->month=$month;
		$listRec=$objExpPurchase->pagingCollection();

	}
	include("html/frmExpenseReport.php");
} 
?>
