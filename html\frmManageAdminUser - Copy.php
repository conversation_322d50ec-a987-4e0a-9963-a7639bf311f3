<script>
function ValidateForm(){
	if(document.getElementById("txtPassword").value!=document.getElementById("txtVerifyPassword").value){
		document.getElementById("error_msg").innerHTML='Passowrd and confirm password should be same.';
		return false;
	}
	return true;
}
function showPWD(){
	if(document.getElementById("txtPassword").type == "password"){
		document.getElementById("txtPassword").type="text";
		document.getElementById("hide_show_password").src='images/hide_password.png';
		document.getElementById("hide_show_password").title='Hide Password';
	}
	else{
		document.getElementById("txtPassword").type="password";
		document.getElementById("hide_show_password").src='images/show_password.png';
		document.getElementById("hide_show_password").title='Show Password';
	}
}
function showPWDVerify(){
	if(document.getElementById("txtVerifyPassword").type == "password"){
		document.getElementById("txtVerifyPassword").type="text";
		document.getElementById("hide_show_password_verify").src='images/hide_password.png';
		document.getElementById("hide_show_password_verify").title='Hide Password';
	}
	else{
		document.getElementById("txtVerifyPassword").type="password";
		document.getElementById("hide_show_password_verify").src='images/show_password.png';
		document.getElementById("hide_show_password_verify").title='Show Password';
	}
}
</script>

<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Admin</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5><?=$heading;?></h5>
							</div>
							<div class="col-md-6">
							<form action="<?=$pageName;?>" method="post" name="frmManage" id="frmManage">
								<? if(isset($_REQUEST['btnAddUser']) || $_REQUEST['id']!=''){ ?>
										<button type="button" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack" onclick="document.location='<?=$pagename;?>'"><i class="fas fa-angle-left"></i> Back</button>
								<?php } 
									elseif($_SESSION['act_add']==1) { ?>
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser"><i class="fas fa-plus"></i> Add <?=$heading;?></button>
								<? } ?>
							</form>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
					<?	if(isset($_REQUEST['btnAddUser']) || isset($_REQUEST['id'])){ ?>
						<!--========== Add User ==========-->
						<form action="<?=$pageName;?>" method="post" name="frmManageUser" id="frmManageUser" onsubmit="return ValidateForm()">
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label>Employee Name</label>
                                    <input  name="txtAdminName" type="text" id="txtAdminName"  value="<?=$listEdit[0]['adminName'];?>"  class="form-control" required>
                                </div>
								<div class="form-group col-md-4">
                                    <label>Email (Username)</label>
                                    <input name="txtEmail" type="email" id="txtEmail"  value="<?=$listEdit[0]['adminEmail'];?>"  class="form-control" required>
                                </div>
                                <div class="form-group col-md-4">
                                    <?php /*?><label>User Name</label>
                                    <input  name="txtUserName" type="text" id="txtUserName"  value="<?=$listEdit[0]['adminUsername'];?>" class="form-control" required><?php */?>
                                </div>
								<div class="form-group col-md-3">
                                    <label>Password</label>
                                    <input name="txtPassword" type="password" id="txtPassword" value="<?=decryptPassword($listEdit[0]['adminPassword']);?>" class="form-control" required>
                                </div>
                                <div class="form-group col-md-1">
                                    <label>&nbsp;</label><br/>
                                    <img src="images/show_password.png" width="25" style="cursor:pointer;" onClick="javascript:showPWD()" id="hide_show_password" title="Show Password" />
                                </div>
                                
								<div class="form-group col-md-3">
                                    <label>Verify Password</label>
                                    <input name="txtVerifyPassword" type="password" id="txtVerifyPassword" value="<?=decryptPassword($listEdit[0]['adminPassword']);?>" class="form-control" required>
                                </div>
                                <div class="form-group col-md-1">
                                    <label>&nbsp;</label><br/>
                                    <img src="images/show_password.png" width="25" style="cursor:pointer;" onClick="javascript:showPWDVerify()" id="hide_show_password_verify" title="Show Password" />
                                </div>
                                
								<?php /*?><div class="form-group col-md-4">
                                    <label>Company</label>
                                    <select name="company" id="company"  class="form-control" required>
                                        <option value="">Select</option>
                                        <option value="All" <?=($listEdit[0]['company']=="All") ? "selected" : "";?>>All</option>
                                        <option value="ERP Demo" <?=($listEdit[0]['company']=="ERP Demo") ? "selected" : "";?>>ERP Demo</option>
                                        <option value="Digital Solution" <?=($listEdit[0]['company']=="Digital Solution") ? "selected" : "";?>>Digital Solution</option>
                                    </select>
                                </div><?php */?>
    <!--===== User Rights =====-->
    <?	//===============create checkboxes for main category pages==========
        $user_permissions = explode(",",$listEdit[0]['adminRights']);
        $adminMenuRec=$objAdminMenu->menuCategoryList();	?>
    <div class="form-group col-md-6">
    <? 	for($a=0;$a<5;$a++)
        { 	$bc = ($a%2==0) ? "#edfdfd" : "#d5eaea";  ?>
            <div class="admin" style="background:<?=$bc;?>;">
                <div class="form-check">
                    <input class="form-check-input chkCat<?=$a;?>" type="checkbox" name="chkMain[]" id="chkMain<?=$adminMenuRec[$a]['id'];?>" value="<?=$adminMenuRec[$a]['id'];?>" <?=(in_array($adminMenuRec[$a]['id'],$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'mcat',<?=$a;?>,'')">
                    <label class="form-check-label f-bold clr-main1"><?=$adminMenuRec[$a]['adminMenuName'];?></label>
                </div>
				<? //===============create checkboxes for sub category pages==========
                    $objAdminMenu->id=$adminMenuRec[$a]['id'];
                    $adminSubMenuRec=$objAdminMenu->menuSubCategoryList();
                    if(count($adminSubMenuRec)>0){ 
                        for($s=0;$s<count($adminSubMenuRec);$s++){ ?>
                            <div class="form-check ml-20">
                                <input class="form-check-input chkSubCat<?=$a;?>" type="checkbox" name="chkMain[]" id="chkMain<?=$adminSubMenuRec[$s]['id'];?>" value="<?=$adminSubMenuRec[$s]['id'];?>" <?=(in_array($adminSubMenuRec[$s]['id'],$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'scat',<?=$a;?>,<?=$s;?>)">
                                <label class="form-check-label f-bold"><?=$adminSubMenuRec[$s]['adminMenuName'];?></label>
                            </div>
                            <div class="form-check ml-40">
                                <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminSubMenuRec[$s]['id'];?>_1" value="<?=($adminSubMenuRec[$s]['id']+1000);?>" <?=(in_array(($adminSubMenuRec[$s]['id']+1000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,<?=$s;?>)" class="form-check-input chkOpt_<?=$a;?>_<?=$s;?>">
                                <label class="form-check-label">Add</label>
                                
                                <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminSubMenuRec[$s]['id'];?>_2" value="<?=($adminSubMenuRec[$s]['id']+2000);?>" <?=(in_array(($adminSubMenuRec[$s]['id']+2000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,<?=$s;?>)" class="form-check-input chkOpt_<?=$a;?>_<?=$s;?>">
                                <label class="form-check-label">Update</label>
                                
                                <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminSubMenuRec[$s]['id'];?>_3" value="<?=($adminSubMenuRec[$s]['id']+3000);?>" <?=(in_array(($adminSubMenuRec[$s]['id']+3000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,<?=$s;?>)" class="form-check-input chkOpt_<?=$a;?>_<?=$s;?>">
                                <label class="form-check-label">Delete</label>
                                
                                <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminSubMenuRec[$s]['id'];?>_3" value="<?=($adminSubMenuRec[$s]['id']+4000);?>" <?=(in_array(($adminSubMenuRec[$s]['id']+4000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,<?=$s;?>)" class="form-check-input chkOpt_<?=$a;?>_<?=$s;?>">
                                <label class="form-check-label">View Detail</label>
                                
                                <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminSubMenuRec[$s]['id'];?>_3" value="<?=($adminSubMenuRec[$s]['id']+5000);?>" <?=(in_array(($adminSubMenuRec[$s]['id']+5000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,<?=$s;?>)" class="form-check-input chkOpt_<?=$a;?>_<?=$s;?>">
                                <label class="form-check-label">Print</label>
                            </div>    
                         <?	//==================end of add/edit/delete/view===========
                        }
                    }elseif($adminMenuRec[$a]['adminMenuName']!="Dashboard" && $adminMenuRec[$a]['adminMenuName']!="Database Backup"){ ?>
                        <div class="form-check ml-40">
                            <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminMenuRec[$a]['id'];?>_1" value="<?=($adminMenuRec[$a]['id']+1000);?>" <?=(in_array(($adminMenuRec[$a]['id']+1000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,'0')" class="form-check-input chkOpt_<?=$a;?>_0">
                            <label class="form-check-label">Add</label>
                            <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminMenuRec[$a]['id'];?>_2" value="<?=($adminMenuRec[$a]['id']+2000);?>" <?=(in_array(($adminMenuRec[$a]['id']+2000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,'0')" class="form-check-input chkOpt_<?=$a;?>_0">
                            <label class="form-check-label">Update</label>
                            <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminMenuRec[$a]['id'];?>_3" value="<?=($adminMenuRec[$a]['id']+3000);?>" <?=(in_array(($adminMenuRec[$a]['id']+3000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,'0')" class="form-check-input chkOpt_<?=$a;?>_0">
                            <label class="form-check-label">Delete</label>
                            <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminMenuRec[$a]['id'];?>_3" value="<?=($adminMenuRec[$a]['id']+4000);?>" <?=(in_array(($adminMenuRec[$a]['id']+4000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,'0')" class="form-check-input chkOpt_<?=$a;?>_0">
                            <label class="form-check-label">View Detail</label>
                            <input type="checkbox" name="chkMain[]" id="chkMain<?=$adminMenuRec[$a]['id'];?>_3" value="<?=($adminMenuRec[$a]['id']+5000);?>" <?=(in_array(($adminMenuRec[$a]['id']+5000),$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'opt',<?=$a;?>,'0')" class="form-check-input chkOpt_<?=$a;?>_0">
                            <label class="form-check-label">Print</label>
                        </div>
                    <? } ?>
            </div>
        <? } ?>
    </div>
								
	<div class="form-group col-md-6">
    <? 	for($a=5;$a<count($adminMenuRec);$a++)
        { 	$bc = ($a%2==0) ? "#edfdfd" : "#d5eaea";  ?>
            <div class="admin" style="background:<?=$bc;?>;">
                <div class="form-check">
                    <input class="form-check-input chkCat<?=$a;?>" type="checkbox" name="chkMain[]" id="chkMain<?=$adminMenuRec[$a]['id'];?>" value="<?=$adminMenuRec[$a]['id'];?>" <?=(in_array($adminMenuRec[$a]['id'],$user_permissions)) ? "checked" : "";?>  onclick="chkSub(this,'mcat',<?=$a;?>,'')">
                    <label class="form-check-label f-bold clr-main1"><?=$adminMenuRec[$a]['adminMenuName'];?></label>
                </div>
			</div>
        <? } ?>
    </div>
								<!--===== User Rights =====-->
                            </div>
                          <div style="clear:both!important;"></div>
                    <div id="error_msg" class="err_msg"></div>
                    <div style="clear:both;"></div>
						  <?php if(isset($_GET['id'])){?>
                                <input type="submit" name="btnUpdate" value="Update User" btn btn-success />
                          <?php } else {?>
                                <input type="submit" name="btnAdd" value="Add User" class="btn btn-success" />
                          <?php } ?>  
                        </form>
						<!--========== Add User ==========-->
						<? }else{ ?>
						<!--========== List View ==========-->
                        <form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                        <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
						<div id="error_frmvalidate" class="err_msg" style="float:right; font-weight:bold; font-style:italic; padding:3px;"></div><div style="clear:both;"></div>
						<div class="dt-responsive table-responsive">
							<table id="tblAdminUser" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th colspan="6" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select name="optAction" id="optAction" class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													<option value="">--Select Action--</option>
													<? if($_SESSION['act_del']==1){ ?>
                                                    <option value="0">Delete</option><? } ?>
                                                    <? if($_SESSION['act_edit']==1){ ?>
                                                    <option value="1">Published</option>
                                                    <option value="2">Unpublished</option> 
                                                    <?php /*?><option value="3">Update Sequence</option><?php */?><? } ?>
												</select>
											</label>
											<input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" value="Submit" name="btnAction" id="btnAction" />
										</th>											
									</tr>
									<tr>
										<th>Sr. No.</th>
										<th>Name</th>
										<th>Email</th>
										<th>
											<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"> Action
										</th>
									</tr>
								</thead>
								<tbody>
                                 <? for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
										<td><?=$listRec[$e]['adminName'];?></td>
										<td><?=$listRec[$e]['adminEmail'];?></td>
										<td class="table-action">
                                           <? if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
	                					<input type="checkbox" name="chkAction[]" id="chkAction[]" value="<? echo $listRec[$e]['id'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10 chkboxcontent"/>&nbsp;<? } ?>
                                           <? if($_SESSION['act_edit']==1){
											if($listRec[$e]['status']=='0'){ ?>
												<a href="?status=1&amp;uid=<? echo $listRec[$e]['id'];?>"><i class="fas fa-minus mr-10 clr-red"></i></a>
											<?php } else {?>
												<a href="?status=0&amp;uid=<? echo $listRec[$e]['id'];?>"><i class="fas fa-check mr-10 clr-green"></i></a>
											<?php }
											} 
											if($_SESSION['act_edit']==1){ ?>
                                            <a href="codeManageAdminUser.php?id=<?php echo $listRec[$e]['id'];?>"><i class="fas fa-pencil-alt mr-10"></i></a><? } ?>
											<?	if($listRec[$e]['id']!=1 && $_SESSION['act_del']==1){ ?><a onclick="deleteAdmin(<?php echo $listRec[$e]['id'];?>,'<?php echo $listRec[$e]['adminUsername'];?>','Admin User','codeManageAdminUser.php')"><i class="fas fa-trash-alt mr-10 clr-red"></i></a><? } ?>
										</td>
									</tr>
                                    <? } ?>
								</tbody>
							</table>
						</div>
                        </form>
						<!--========== List View ==========-->
						<? } ?>
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>