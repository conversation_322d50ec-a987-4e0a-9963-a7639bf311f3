<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Envelope Print</h5>
							</div>
							<div class="clearfix"></div>
						</div>	 
                    </div>
                    <div class="card-body">
						
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form action="#">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
                                	 <? $listParty=$objParty->selectStatus();?>
									<label>Party Name :</label>
									<select class="js-example-basic-single form-control col-sm-12" name="Client_Name" id="Client_Name" required>
                                    	<option value="">All</option>
										<? for($i=0;$i<count($listParty);$i++)
										{ ?>
										<option value="<?=$listParty[$i]['id'];?>" <?=($_GET['Client_Name']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
										<? } ?>
									</select>
								</div>
								<?php /*?><div class="col-sm-12 col-md-4">
                                <? $listArea=$objArea->selectStatus();?>
									<label>Area Name :</label>
									<select class="js-example-basic-single form-control col-sm-12" name="Area_Name" id="Area_Name">
                                    <option value="">All</option>
										<? for($i=0;$i<count($listArea);$i++)
                        { ?>
                        <option value="<?=$listArea[$i]['id'];?>" <?=($_GET['Area_Name']==$listArea[$i]['id']) ? "selected" : "";?>><?=$listArea[$i]['area_name'];?></option>
                        <? } ?>
									</select>
								</div><?php */?>
                                <script language="javascript">
								function printclient()
								{
									var pname = document.getElementById("Party_Name").value; 
									
									window.open(
								'<?=BASE_URL;?>/print_client_details.php?Party_Name='+pname,'_blank'
									  // http://erp.erpdemocompany123.com/demo 
									  // <- This is what makes it open in a new window.
									);
								}
								
								function printclientenvelope()
								{
									if(document.getElementById("Client_Name").value!=""){
										var pname = document.getElementById("Client_Name").value; 
										window.open('<?=BASE_URL;?>/print_client_detail_envelope.php?Party_Name='+pname,'_blank');
									}
									else
										alert("Please select Party");
								}
								</script>
                                
								<div class="form-group col-md-4">
									<label>&nbsp;</label><br>
									<button name="search" id="search" value="Search"  class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a onclick="javascript:printclientenvelope()" class="btn btn-info btn-sm has-ripple">Print</a><? } ?>
									<a onclick="javascript:document.location='codeManageClientPrintReport.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive">
							<table id="tblClientPrintReport" class="table table-striped table-bordered nowrap table-responsive" width="100%">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Party Name</th>
										<th>Address Details</th>
										<th>Contact Details</th>
										<th>Contact Person</th>
									</tr>
								</thead>
								<tbody>
                                 <?  
								if(count($listRec)>0)
								 {
								   for($e=0;$e<count($listRec);$e++){
								?>
									<tr>
										<td><?=$e+1;?></td>
                <td class="left-align"><?=$listRec[$e]['party_name'];?></td>                
                <td class="left-align"><?=$listRec[$e]['party_address'];?><br/>
                	<?=$listRec[$e]['city'];?> - <?=$listRec[$e]['zipcode'];?><br/>
                    <?=$objState->getNameById($listRec[$e]['state']);?>, <?=$listRec[$e]['country'];?>                </td>
                <td class="left-align"><?=$listRec[$e]['office_no1'];?><br/>
                	<?=$listRec[$e]['email'];?><br/>
                     <?=$listRec[$e]['domain_name'];?><br/>                </td>
                <td class="left-align"> <?=$listRec[$e]['contact_name1'];?><br/>
                     <?=$listRec[$e]['contact_no1'];?><br/>
                     <?=$listRec[$e]['email1'];?></td>
									</tr>
                                    <? }
									} ?>
									
								</tbody>
							</table>
						</div>
						<!-- Table -->		
						<!--========== List View ==========-->
                    </div>
                </div>
            </div>
        </div>		
	</div>
</div>