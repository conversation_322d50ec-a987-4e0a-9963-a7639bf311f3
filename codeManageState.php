<?php
include("template.php");
function main()
{
	include("inc/clsObj.php");
	$heading="<span>Manage</span> State ";
	$pageName="codeManageState.php";	
	extract($_POST);	
	$objState->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objState->state_name=$State_Name;
	$objState->state_tinno=$State_Tin;
	$objState->state_code=$State_Code;	
		
	$objState->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objState->insert();	
		redirect("codeManageState.php");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objState->update();
		 redirect("codeManageState.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objState->deleteSelect($chkAction);
					break;
			case 1:
					$objState->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objState->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageState.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objState->delete();
		redirect("codeManageState.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objState->status();
		redirect("codeManageState.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objState->selectRecById();						
	}	
	elseif($_GET['State_Name']!="")
	{
		$objState->id = $_GET['State_Name'];
		$listRec=$objState->selectRecById();
    }
	else
		$listRec=$objState->paging();
		
    include("html/frmManageState.php");
 } 
?>