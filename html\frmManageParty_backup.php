<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>View Client Detail</h5>
							</div>
							<div class="col-md-6">
								<? if(isset($_REQUEST['btnAddUser']) || isset($_GET['id']) || isset($_GET['pid'])){ ?>
									<form action="<?=$pageName;?>" method="post">	
										<button type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack">
											<i class="fas fa-angle-left"></i> Back
										</button>
									</form>
								<?php } elseif($_SESSION['act_add']==1) { ?>
									<form action="<?=$pageName;?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add Client
										</button>
									</form>									
								<? } ?>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						<?php if(isset($_REQUEST['btnAddUser']) || $_GET['id']!=''){ ?>
						<!--========== Add Client ==========-->
						<form action="<?=$pageName;?>" method="post" name="frmManage"  enctype="multipart/form-data">
                             <input name="hid" type="hidden" value="<?=$_GET['id'];?>" />
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label>Customer Name</label>
                                    <input type="text" name="Customer_Name" id="Customer_Name"  value="<?=$listEdit[0]['customer_name'];?>"  class="form-control" required>
                                </div>
								
								
									<div class="form-group col-md-4">
                                    <label>Phone Number</label>
                                    <input type="text" name="Contact_Number" id="Contact_Number"  value="<?=$listEdit[0]['contact_number'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                    <label>Alternative Number</label>
                                    <input type="text" name="Alternative_Number" id="Alternative_Number" value="<?=$listEdit[0]['alternate_number'];?>" class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>Email Address</label>
                                    <input type="text" name="Email_Address" id="Email_Address"  value="<?=$listEdit[0]['email'];?>"  class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>Address</label>
                                    <textarea name="Party_Address" type="text" id="Party_Address" class="form-control" required><?=$listEdit[0]['address'];?></textarea>
                                </div>
                                 <div class="form-group col-md-4">
                                    <label>Area</label>
                                    <input type="text" name="Area" id="Area" value="<?=$listEdit[0]['area'];?>" class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>City</label>
                                    <input type="text" name="City" id="City" value="<?=$listEdit[0]['city'];?>" class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>Gold Weight (grams)</label>
                                    <input type="text" name="Gold_Weight" id="Gold_Weight" value="<?=$listEdit[0]['gold_weight'];?>" class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>Loan Amount</label>
                                    <input type="text" name="Loan_Amount" id="Loan_Amount" value="<?=$listEdit[0]['loan_amount'];?>" class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>Bank / Pedi Name</label>
                                    <input type="text" name="Bank_Name" id="Bank_Name" value="<?=$listEdit[0]['bank_name'];?>" class="form-control">
                                </div>

								<div class="form-group col-md-4">
                                    <label>Additional Notes</label>
                                    <textarea name="Additional_Notes" id="Additional_Notes" class="form-control"><?=$listEdit[0]['additional_notes'];?></textarea>
                                </div>
                                 <?php if($_SESSION['memberrole']=="Tele Caller"){ ?>
                                <?php $salesPersons = $obj_admin->selectRecByRole('Sales Person');?>
                                <div class="form-group col-md-4">
                                    <label>Sales Person</label>
                                    <select name="Sales_Person" id="Sales_Person" class="form-control" required>
                                        <option value="">-- Select Sales Person --</option>
                                        <?php foreach($salesPersons as $person): ?>
                                            <option value="<?=$person['id'];?>" <?= ($person['id'] == $listEdit[0]['sales_person']) ? 'selected' : ''; ?>>
                                                <?=$person['adminName'];?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            <?php } ?>
                                
                                <div class="form-group col-md-4">
                                    <label>Star Ranking</label><br>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Star_Ranking" id="star1" value="1" 
                                            <?= ($listEdit[0]['star_ranking'] ?? '') == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="star1">1 Star</label>
                                    </div>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Star_Ranking" id="star2" value="2" 
                                            <?= ($listEdit[0]['star_ranking'] ?? '') == '2' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="star2">2 Star</label>
                                    </div>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Star_Ranking" id="star3" value="3" 
                                            <?= ($listEdit[0]['star_ranking'] ?? '') == '3' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="star3">3 Star</label>
                                    </div>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Star_Ranking" id="star4" value="4" 
                                            <?= ($listEdit[0]['star_ranking'] ?? '') == '4' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="star4">4 Star</label>
                                    </div>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Star_Ranking" id="star5" value="5" 
                                            <?= ($listEdit[0]['star_ranking'] ?? '') == '5' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="star5">5 Star</label>
                                    </div>
                                </div>

                                 <div class="form-group col-md-4">
                                    <label>Source</label><br>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Source" id="chkGoogle" value="Google" 
                                            <?= ($listEdit[0]['source'] ?? '') == 'Google' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="chkGoogle">Google</label>
                                    </div>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Source" id="chkFacebook" value="Facebook" 
                                            <?= ($listEdit[0]['source'] ?? '') == 'Facebook' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="chkFacebook">Facebook</label>
                                    </div>
                                    
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Source" id="chkInstagram" value="Instagram"
                                            <?= ($listEdit[0]['source'] ?? '') == 'Instagram' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="chkInstagram">Instagram</label>
                                    </div>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Source" id="chkShineBoard" value="Shine Board"
                                            <?= ($listEdit[0]['source'] ?? '') == 'Shine Board' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="chkShineBoard">Shine Board</label>
                                    </div>
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Source" id="chkAutoAd" value="Auto Ad"
                                            <?= ($listEdit[0]['source'] ?? '') == 'Auto Ad' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="chkAutoAd">Auto Ad</label>
                                    </div>
                                
                                
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="Source" id="chkOther" value="Other" 
                                            <?= ($listEdit[0]['source'] ?? '') == 'Other' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="chkOther">Other</label>
                                    </div>
                                </div>
                                
                                <div class="form-group col-md-4" id="OtherSourceBox" style="display: none;">
                                    <label>Please Specify</label>
                                    <textarea name="Other_Source" id="Other_Source" class="form-control"><?=$listEdit[0]['other_source'] ?? '';?></textarea>
                                </div>

                                <?php if($_SESSION['memberrole']=="Sales Person"){ ?>
                                <div class="form-group col-md-4">
                                    <label>Image 1</label>
                                    <input type="file" name="Image1" id="Image1" class="form-control">
                                    <img src="<?=IMAGE_PATH.$listEdit[0]['image1'];?>" alt="" width="100" />
                                    <input type="hidden" name="Image1hidden" id="Image1hidden" value="<?=$listEdit[0]['image1'];?>" class="form-control">
                                </div>
                                
                                <div class="form-group col-md-4">
                                    <label>Image 2</label>
                                    <input type="file" name="Image2" id="Image2" class="form-control">
                                    <img src="<?=IMAGE_PATH.$listEdit[0]['image2'];?>" alt="" width="100" />
                                    <input type="hidden" name="Image2hidden" id="Image2hidden" value="<?=$listEdit[0]['image2'];?>" class="form-control">
                                </div>
                                
                                <div class="form-group col-md-4">
                                    <label>Image 3</label>
                                    <input type="file" name="Image3" id="Image3" class="form-control">
                                    <img src="<?=IMAGE_PATH.$listEdit[0]['image3'];?>" alt="" width="100" />
                                    <input type="hidden" name="Image3hidden" id="Image3hidden" value="<?=$listEdit[0]['image3'];?>" class="form-control">
                                </div>
                                
                                <div class="form-group col-md-4">
                                    <label>Image 4</label>
                                    <input type="file" name="Image4" id="Image4" class="form-control">
                                    <img src="<?=IMAGE_PATH.$listEdit[0]['image4'];?>" alt="" width="100" />
                                    <input type="hidden" name="Image4hidden" id="Image4hidden" value="<?=$listEdit[0]['image4'];?>" class="form-control">
                                </div>

                                <div class="form-group col-md-4">
                                    <label>Status</label>
                                    <select name="Inquiry_Status" id="Inquiry_Status" class="form-control" required>
                                        <option value="">-- Select Status --</option>
                                        <option value="Open" <?= ($listEdit[0]['inquiry_status'] ?? '') == 'Open' ? 'selected' : ''; ?>>Open</option>
                                        <option value="Close" <?= ($listEdit[0]['inquiry_status'] ?? '') == 'Close' ? 'selected' : ''; ?>>Close</option>
                                    </select>
                                </div>
                            <?php } ?>

                                                                
                            </div>
                            <?php /*?><button type="submit" class="btn btn-success">Add Client</button><?php */?>
                             <?php if(isset($_GET['id'])){?>
            			<input type="submit" name="btnUpdate" value="Update Client"  class="btn btn-success"  style="margin:0 auto!important;" />
			         <?php } else {
                        if($_SESSION['memberrole']=="Tele Caller"){ ?>
            			 <input type="submit" name="btnAdd" value="Add Client"  class="btn btn-success" style="margin:0 auto!important;" />
			         <?php } 
                        } ?>  
                        </form>
						<!--========== Add Client ==========-->
						<? }
						else if(isset($_GET['pid']) && $_GET['pid']!=''){ ?>
							<div class="row">
							<div class="col-md-6">
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Company Name :</div>
									<div class="col-sm-8"><?=$listEdit[0]['party_name'];?></div>
								</div>	
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Address :</div>
									<div class="col-sm-8"><?=$listEdit[0]['party_address'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">City :</div>
									<div class="col-sm-8"><?=$listEdit[0]['city'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">State :</div>
									<div class="col-sm-8"><?=$objState->getNameById($listEdit[0]['state']);?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Zipcode :</div>
									<div class="col-sm-8"><?=$listEdit[0]['zipcode'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Country :</div>
									<div class="col-sm-8"><?=$listEdit[0]['country'];?></div>
								</div>
                                <div class="form-group row">
									<div class="col-sm-4 f-bold">GST No. :</div>
									<div class="col-sm-8"><?=$listEdit[0]['gstin'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Office Number1 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['office_no1'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Office Number2 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['office_no2'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Fax Number :</div>
									<div class="col-sm-8"><?=$listEdit[0]['fax_no'];?></div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Email Address :</div>
									<div class="col-sm-8"><?=$listEdit[0]['email'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Website Name :</div>
									<div class="col-sm-8"><?=$listEdit[0]['domain_name'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Contact Person1 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['contact_name1'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Contact Number1 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['contact_no1'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Email Address1 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['email1'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Contact Person2 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['contact_name2'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Contact Number2 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['contact_no2'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Email Address2 :</div>
									<div class="col-sm-8"><?=$listEdit[0]['email2'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Reference Name :</div>
									<div class="col-sm-8"><?=$listEdit[0]['reference_name'];?></div>
								</div>
								<div class="form-group row">
									<div class="col-sm-4 f-bold">Reference Contact Number :</div>
									<div class="col-sm-8"><?=$listEdit[0]['reference_no'];?></div>
								</div>
							</div>
							<div class="clearfix"></div>
						</div>
						
						<? }
						else{ ?>
						<!--========== List View ==========-->
						<div class="dt-responsive table-responsive">
                        <form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                         <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
						<div id="error_frmvalidate" class="err_msg" style="float:right; font-weight:bold; font-style:italic; padding:3px;"></div><div style="clear:both;"></div>
							<table id="tblParty" class="table table-striped table-bordered nowrap">
								<thead>
									<!-- <tr>
										<th colspan="2"></th>
										<th colspan="3" class="text-right">
    										<label class="mb-0 mr-15"> Action :
    											<select name="optAction" id="optAction" class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
    												<option value="">--Select Action--</option>
    												  <? if($_SESSION['act_del']==1){ ?>
                                                      <option value="0">Delete</option><? } ?>
                                                      <? if($_SESSION['act_edit']==1){ ?>
                                                      <option value="1">Published</option>
                                                      <option value="2">Unpublished</option><? } ?>  
    											</select>
    										</label>
    										<input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" value="Submit" name="btnAction" id="btnAction" />
										</th>			
									</tr> -->
									<tr>
										<th class="sorting_asc sorting_desc">Sr. No.</th>
										<th class="sorting_asc sorting_desc">Party Name</th>
                                        <th class="sorting_asc sorting_desc">Area</th>
                                        <th class="sorting_asc sorting_desc">Sales Person</th>
                                        <th class="sorting_asc sorting_desc">Date</th>
										<th class="sorting_asc sorting_desc">
											<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"> Action
										</th>
									</tr>
								</thead>
								<tbody>
                                 <?  if(count($listRec)>0){
									   for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
										<td><?=$listRec[$e]['customer_name'];?></td>
                                         <td><?=$listRec[$e]['area'];?></td>
                                            <td><?=$obj_admin->getNameById($listRec[$e]['sales_person']);?></td>
                                            <td><?=$listRec[$e]['cdt'];?></td>
                                        <td class="table-action">
										<? if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
	                					<input type="checkbox" name="chkAction[]" id="chkAction[]" value="<? echo $listRec[$e]['id'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10 chkboxcontent"/>&nbsp;<? } ?>
                                        <? if($_SESSION['act_edit']==1){
											if($listRec[$e]['status']=='1'){?>
                                            	<a href="?status=0&amp;uid=<?=$listRec[$e]['id'];?>"><i class="fas fa-check mr-10 clr-green"></i></a><? }else{ ?>
                                            	<a href="?status=1&amp;uid=<?=$listRec[$e]['id'];?>"><i class="fas fa-minus mr-10 clr-red"></i></a><? } ?>
											<a href="<?=$pageName;?>?id=<?=$listRec[$e]['id'];?>">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
                                        <? } 
											if($_SESSION['act_del']==1){?>
											<a onclick="deleteAll(<?php echo $listRec[$e]['id'];?>,'<?php echo $listRec[$e]['party_name'];?>','Party','<?=$pageName;?>')">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
                                            <? } ?>

											<?php /*?><a href="viewcodeManageParty.php" target="_blank" class="btn btn-info btn-sm has-ripple"><?php */?>
                                            <?php /*?><a href="<?=$pageName;?>?pid=<?=$listRec[$e]['id'];?>" class="btn btn-info btn-sm has-ripple">View</a><?php */?>
										</td>
									</tr>
									<? }
									} ?>
								</tbody>
							</table>
                            </form>
						</div>
						<!--========== List View ==========-->
						<? } ?>
						
						<script>
                            function toggleOtherSource() {
                                const isOtherChecked = document.getElementById('chkOther').checked;
                                document.getElementById('OtherSourceBox').style.display = isOtherChecked ? 'block' : 'none';
                            }
                        
                            // Attach change event to all Source radio buttons
                            document.querySelectorAll('input[name="Source"]').forEach((radio) => {
                                radio.addEventListener('change', toggleOtherSource);
                            });
                        
                            // Call once on page load to set initial visibility
                            window.addEventListener('DOMContentLoaded', toggleOtherSource);
                        </script>
						
						
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>