<?	session_start(); 
	error_reporting(0);
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>ERP Demo</title>
</head>
<?
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();	

	$objBill->id=$_GET['id'];

	$listEdit=$objBill->selectRecById();
		
	$objBill->bid = $listEdit[0]['id'];
	$listItems=$objBill->selectItemsByBillNo();	
	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>
<body>
<div style="width:100%; margin-top:100px;" id="print_header">
<div style="width:600px; margin:0 auto;">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
<tr>
  <td>
<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">
<tr><td colspan="2" style="text-align:center; font-size:25px; font-weight:bold;">Invoice</td></tr>
  <tr class="toprow">
      
<td width="50%" align="left" valign="top">Invoice No. : 
      <strong><?=$listEdit[0]['invoice_no'];?></strong></td>
    <td width="50%" align="left" valign="top">Invoice Date : 
      <strong><?=$listEdit[0]['bdt'];?></strong></td>
    </tr>
  <tr class="toprow">
    <td align="left" valign="top">PO/PI No: <strong><?=$listEdit[0]['po_no'];?></strong></td>
    <td align="left" valign="top">Mode/Terms of Payment : <strong>7 Days</strong></td>
  </tr>
<tr class="toprow">
    <td align="left" valign="top"><strong>M/s :</strong><br/>
		<? if(isset($_GET['id'])){
            $objParty->id=$listEdit[0]['party'];
            $partydet=$objParty->selectRecById();
        }  ?>
		<?=$partydet[0]['party_name']."<br/>".$partydet[0]['city'];?></td>
    <td align="left" valign="top">
    <strong>PAN No. : </strong><?=$objCompany->getPannoByCompany($listEdit[0]['company']);?><br/>
   <strong>GSTIN : </strong><?=$objCompany->getGstinByCompany($listEdit[0]['company']);?>
    </td>
  </tr>
  <tr class="toprow">
    <td colspan="3" align="left" valign="top">&nbsp;
    	</td>
    </tr>
  </table>
</td></tr>
<tr>
<td>
<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;">
    <tr>
      <td width="8%" style="border-right:#999999 1px solid;"><strong>No.</strong></td>
      <td  width="40%" style="border-right:#999999 1px solid;"><strong>Particulars</strong></td>
      <td width="20%" style="border-right:#999999 1px solid; text-align:right;"><strong>Rate</strong></td>
      <td width="12%" style="border-right:#999999 1px solid; text-align:right;"><strong>Quantity</strong></td>
      <td width="20%" style="border-right:#999999 1px solid; text-align:right;"><strong>Amount</strong></td>
    </tr>
  <? for($i=0;$i<count($listItems);$i++) { ?>
  <tr>
  	<td style="border-right:#999999 1px solid;"><?=$i+1;?></td>
   <td style="border-right:#999999 1px solid;">
   	<? 	$objItem->id=$listItems[$i]['item'];
		$itemdet=$objItem->selectRecById();
		
		echo "<strong>".$itemdet[0]['item_name']."</strong><br/>";
		echo $listItems[$i]['description'];
	?>
	</td>
    <td style="border-right:#999999 1px solid; text-align:right;"><?=$objCrn->getCurrencySymbol($listEdit[0]['currency']);?> <?=$listItems[$i]['rate_per_qty'];?></td>
    <td style="border-right:#999999 1px solid; text-align:right;"><?=$listItems[$i]['no_of_items'];?></td>
    <td style="border-right:#999999 1px solid; text-align:right;"><?=$objCrn->getCurrencySymbol($listEdit[0]['currency']);?> <?=$listItems[$i]['amount'];?></td>
  </tr>
<? } ?>  
  <tr class="toprow">
    <td colspan="4" style="text-align:right;">Total Amount :</td>
	<td style="text-align:right;"><?=$objCrn->getCurrencySymbol($listEdit[0]['currency']);?> <?=$listEdit[0]['net_amount'];?></td></tr>
    <?php /*?><tr>
    <td colspan="4" style="text-align:right;">Discount :</td>
	<td style="text-align:right;"><?=($listEdit[0]['discount']) ? $listEdit[0]['discount'] : 0;?></td></tr><?php */?>

    <tr>
    <td colspan="4" style="text-align:right;">Net Amount :</td>
    <td style="text-align:right;"><?=$objCrn->getCurrencySymbol($listEdit[0]['currency']);?> <?=$listEdit[0]['amount'];?></td>
  </tr>  
  <tr><td colspan="5">
<?
function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }

echo "<strong>Amount Chargeable (in words) :</strong> ".$objCrn->getCurrencyName($listEdit[0]['currency'])." ".ucwords(no_to_words($listEdit[0]['amount']))." Only";
?>
</td></tr>
</table>
</td>
</tr>

<tr>
<td>
<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;">
  <tr>
    <td width="348" valign="top" style="font-size:10px;">
<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
<ul>
  <li>By using the services of <strong>&quot;<?=$_SESSION['company'];?>&quot;</strong> you agree to be bound by the term & policies listed at <a href="http://www.erpdemocompany123.com/terms.php" target="_blank">www.erpdemocompany123.com/terms.php</a></li>
  <li>Interest @ 18 % per annum will be charged for delayed payment.</li>
  <li>All disputes are subject to Ahmedabad Jurisdiction</li>
</ul>
<?php /*?><ol>
  <li>Payment should be in favor of<br/><strong>&ldquo;<?=$_SESSION['company'];?>&rdquo;</strong> payable at Ahmedabad.</li>
  <li>Subject to Ahmedabad Jurisdiction.</li>
  <li>Service void if payment commitment       failed, bounced cheque is also failed commitment.</li>
  <li>Bounced Cheque Return will attract a charge  of Rs. 400/-.</li>
  <li>Bulk mailing is restricted.</li>
  <li>Domain &amp; Web Hosting Renewal charges       may change as per relevant market rate.</li>
  <li>Website Maintenance will be chargeable.</li>
  <li>Website Promotion Renewal will be yearly basis.</li>
  <li>All <?=$_SESSION['company'];?> Combo Package will be yearly basis.</li>
</ol><?php */?>

</td>
    <td width="226" valign="top">
    
      <p align="center">For, <?=$_SESSION['company'];?></p>
      <br/><br/><br/><br/><br/><br/><br/><br/>
      <p align="center">AUTHORISED    SIGNATORY</p></td>
  </tr>
</table>
</td>
</tr>
</table>
</div>
</div>
<script language="javascript">
	window.print();
</script>
</body>
</html>