<?php
include("template.php");

function main()
{
	$heading="<span>Manage</span> Quotation ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$bdate = explode("-",$Bill_Date);
	$bill_date=$bdate[2]."-".$bdate[1]."-".$bdate[0];

	//$rdate = explode("-",$Renewal_Date);
	//$renewal_date=$rdate[2]."-".$rdate[1]."-".$rdate[0];
	$renewal_date="";
	//$object = $objQuotation;
	$objQuote->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	
	$objQuote->company=$_SESSION['company'];
	$objQuote->financial_year=$_SESSION['fyear'];
	$objQuote->quote_for=$Quote_For;
	$objQuote->quote_no=$Quote_No;
	$objQuote->quote_type=strtoupper($chkgst);
	
	$quote_terms=str_replace("\&quot;","", $quote_terms);		
	$quote_terms=str_replace('\\', '', $quote_terms);				
	$quote_terms=str_replace("\'","'", $quote_terms);		
	$quote_terms=str_replace("'","\'", $quote_terms);
	$objQuote->quote_terms=$quote_terms;
	
	$quote_signature=str_replace("\&quot;","", $quote_signature);		
	$quote_signature=str_replace('\\', '', $quote_signature);				
	$quote_signature=str_replace("\'","'", $quote_signature);		
	$quote_signature=str_replace("'","\'", $quote_signature);
	$objQuote->quote_signature=$quote_signature;
	
	$objQuote->party=trim(strtoupper($Party_Name));
	$objQuote->party_address=$Party_Address;
	$objQuote->party_city=$Party_City;
	$objQuote->contact_no=$Contact_No;
	$objQuote->email_id=$Email_ID;
	$qdate = explode("-",$Quote_Date);
	$quote_date=$qdate[2]."-".$qdate[1]."-".$qdate[0];
	$objQuote->quote_date=$quote_date;
	$objQuote->net_amount=$Net_Amount;	
	$objQuote->cgst_rate=$cgst_rate;	
	$objQuote->cgst_amount=$cgst_amount;
	$objQuote->sgst_rate=$sgst_rate;	
	$objQuote->sgst_amount=$sgst_amount;
	$objQuote->igst_rate=$igst_rate;	
	$objQuote->igst_amount=$igst_amount;	
	$objQuote->discount=$Discount;	
	$objQuote->amount=$Total_Amount;
	$objQuote->status=isset($_GET['status']) ? $_GET['status'] : 1;

	if(isset($_POST['btnAdd']))
	{
		$qid = $objQuote->insert();	
		// echo $qid;
		//=============update last bill no in increment master======
		$ino = explode("/",$Quote_No);
		$objYear->company=$_SESSION['company'];
		$objYear->financial_year=$_SESSION['fyear'];
		$objYear->lqno = $ino[3];
		$objYear->updateCompanyQuoteNo();
		//========end of last updated bill no.================

		for($i=1;$i<=10;$i++)
		{
			$description="";
			$description = "itemDetail".$i;
			$description=$$description;
			$description=str_replace("\&quot;","", $description);		
			$description=str_replace('\\', '', $description);				
			$description=str_replace("\'","'", $description);		
			$description=str_replace("'","\'", $description);	
			$no_of_items = "No_of_Items".$i;
			$rateperqty = "Rate".$i;
			$amount = "Amount".$i;
			
			if($$rateperqty > 0 && $$no_of_items>0){
				$objQuote->qid=$qid;
				$objQuote->description=$description;
				$objQuote->no_of_items=$$no_of_items;
				$objQuote->rate_per_qty=$$rateperqty ;
				$objQuote->amount=$$amount;
				$objQuote->insert_items();	
			}						
		}
		//die; 
?>
		<script language="javascript">
			var msg="Want to Print for the Quotation"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				document.location="codeManageQuotation.php?id=<?=$qid;?>";
				// document.location="print_quotation.php?id=<?//$qid;?>";
			}
			else
			{	
				document.location="codeManageQuotation.php";			 
			}
		</script>			
<?  }

	if(isset($_POST['btnUpdate']))
	{
		$objQuote->update();
		$objQuote->delete_items();
		for($i=1;$i<=10;$i++)
		{
			$description="";
			$description = "itemDetail".$i;
			$description=$$description;
			$description=str_replace("\&quot;","", $description);		
			$description=str_replace('\\', '', $description);				
			$description=str_replace("\'","'", $description);		
			$description=str_replace("'","\'", $description);	
			$no_of_items = "No_of_Items".$i;
			$rateperqty = "Rate".$i;
			$amount = "Amount".$i;
			
			if($$rateperqty>0 && $$no_of_items>0){
				$objQuote->qid=$hid;
				$objQuote->description=$description;
				$objQuote->no_of_items=$$no_of_items;
				$objQuote->rate_per_qty=$$rateperqty ;
				$objQuote->amount=$$amount;
				$objQuote->insert_items();	
			}
		}
		//die;
			?>
        <script language="javascript">
			var msg="Want to Print for the Quotation"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				document.location="codeManageQuotation.php?id=<?=$hid;?>";
				//document.location="print_quotation.php?id=<?=$hid;?>";
			}
			else
			{	
				document.location="codeManageQuotation.php?msg=edit";			 
			}
		</script>
        <?
	}

	if(isset($_GET['delItem']))
	{	$total_amt = 0;
		$objQuote->id=$_GET['delItem'];
		$objQuote->delete_item();
		
		$objQuote->qid=$_GET['bid'];
		$listItems=$objQuote->selectItemsByBillNo();	
		for($i=0;$i<count($listItems);$i++)
		{
			$total_amt+=$listItems[$i]['amount'];
		}
		
		$objQuote->id=$_GET['bid'];
		$billRec=$objQuote->selectRecById();	
		
		//$objQuote->amount=$total_amt-$billRec[0]['discount'];
		$objQuote->net_amount=$total_amt;	
		if($billRec[0]['quote_type']=="GST"){
			$objQuote->cgst_amt = ($total_amt*$billRec[0]['cgst_rate'])/100;
			$objQuote->sgst_amt = ($total_amt*$billRec[0]['sgst_rate'])/100;
			$objQuote->igst_amt = 0;
		}
		else{
			$objQuote->cgst_amt = 0;
			$objQuote->sgst_amt = 0;
			$objQuote->igst_amt = ($total_amt*$billRec[0]['igst_rate'])/100;
		}
		// $objQuote->discount=$Discount;
		$objQuote->amount=$total_amt+$objQuote->cgst_amt+$objQuote->sgst_amt+$objQuote->igst_amt;
		$objQuote->update_amount();	
		
		redirect("codeManageQuotation.php?id=".$_GET['bid']);
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objQuote->deleteSelect($chkAction);
					break;
			case 1:
					$objQuote->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objQuote->statusUpdateUnPublish($chkAction);
					break;
			case 3:
					// $objQuote->sendEmail($chkAction);
					for($s=0;$s<count($chkAction);$s++)
					{
						$id = $chkAction[$s];
						
						//##################################################################
						//      send email function by quotation number 
						//##################################################################
					
						$objQuote->id=$id;
						$listEdit=$objQuote->selectRecById();

						$objQuote->qid = $listEdit[0]['id'];
						$listItems=$objQuote->selectItemsByBillNo();		
						
						$msg='<style>
						#print_header{
							font-size:12px;
							font-family:Verdana, Geneva, sans-serif;
						}
						</style>
						<div style="width:100%; margin-top:60px;" id="print_header">
						<div style="width:600px; margin:0 auto;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
						  <td>
						<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">
						<tr><td colspan="3" style="text-align:center; font-size:25px; font-weight:bold;">Quotation</td></tr>
						  <tr class="toprow">
							<td width="38%" rowspan="3" align="left" valign="top">
							<strong>'.$_SESSION['company'].'</strong><br />
							  304-A, Ashoka,<br/>
							  Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
							  Ahmedabad-380014<br />'; 

							  $msg.='Ph: +91-79-40391397<br />
							  E-Mail : <a href="mailto:<EMAIL>"><EMAIL></a><br />
							  Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>';
							 
							  $msg.='</td>  
						<td width="33%" align="left" valign="top">Quotation No. :<br/>
							  <strong>'.$listEdit[0]['quote_no'].'</strong></td>
							<td width="67%" align="left" valign="top">Quotation Date :<br/>
							  <strong>'.$listEdit[0]['bdt'].'</strong></td>
							</tr>
						  <tr class="toprow">
							<td align="left" valign="top">PO/PI No : <strong>'.$listEdit[0]['po_no'].'</strong></td>
							<td align="left" valign="top" rowspan="2">Mode/Terms of Payment<br/><strong>7 Days</strong></td>
						  </tr>
						  <tr class="toprow">
    <td align="left" valign="top"><strong>PAN No. : </strong>'.$objCompany->getPannoByCompany($listEdit[0]['company']);
		$msg.='<br/>';
   
		$msg.='<strong>GSTIN : </strong>'.$objCompany->getGstinByCompany($listEdit[0]['company']);
	
	$msg.='</td>
  </tr>
						  <tr class="toprow">
							<td colspan="3" align="left" valign="top">
								<strong>M/s :</strong><br/>';
								if(isset($id)){
									$objParty->id=$listEdit[0]['party'];
									$partydet=$objParty->selectRecById();
								}  
								$msg.=$partydet[0]['party_name']."<br/>".$partydet[0]['city'];
							$msg.='</td>
							</tr>
						  </table>
						</td></tr>
						<tr>
						<td>
						<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;">
							<tr>
							  <td width="8%" style="border-right:#999999 1px solid;"><strong>No.</strong></td>
							  <td width="51%" style="border-right:#999999 1px solid;"><strong>Particulars</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Rate</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Quantity</strong></td>
							  <td width="15%" style="border-right:#999999 1px solid; text-align:right;"><strong>Amount</strong></td>
							</tr>';
							// $srn=1;
						  for($i=0;$i<count($listItems);$i++) { 
						  $msg.='<tr>
							<td width="8%" style="border-right:#999999 1px solid;">'.($i+1).'</td>
						    <td width="51%" style="border-right:#999999 1px solid;">';
								$objItem->id=$listItems[$i]['item'];
								$itemdet=$objItem->selectRecById();
								
								$msg.='<strong>"'.$itemdet[0]['item_name'].'</strong><br/>';
								$msg.=$listItems[$i]['description'];
							
							$msg.='</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['rate_per_qty'].'</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['no_of_items'].'</td>
							<td width="15%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['amount'].'</td>
						  </tr>';
						}   
						  $msg.='<tr class="toprow">
							<td colspan="4" style="text-align:right;">Total Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['net_amount'].'</td></tr>';
						  
						   /* <tr>
							<td colspan="4" style="text-align:right;">Discount :</td>
							<td style="text-align:right;">';
							$msg.= ($listEdit[0]['discount']) ? $listEdit[0]['discount'] : 0;
							$msg.='</td></tr> */
							
							$msg.='<tr>
							<td colspan="4" style="text-align:right;">Net Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['amount'].'</td>
						  </tr>  
						  <tr><td colspan="5">';

						$msg.='<strong>Amount Chargeable (in words) :</strong> '.ucwords(no_to_words($listEdit[0]['amount'])).' Only';
						
						$msg.='</td></tr>
						</table>
						</td>
						</tr>
						
						<tr>
						<td>
						<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;">
						  <tr>
							<td width="348" valign="top" style="font-size:10px;">
						<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
						<ul>
						  <li>Cheque/Draft/Pay Order should be drawn in favor  of <strong>&quot;';
						  $msg.=$_SESSION['company'];
						  $msg.='&quot;</strong> payable at Ahmedabad.</li>
						  <li>Work on services shall commence only after clearance of Cheque/ Draft/ Pay Order.</li>
						  <li>Service void if payment commitment failed, bounced cheque is also failed commitment.</li>
						  <li>Bounced cheque will attract charge of Rs. 400/-</li>
						  <li>Bulk Mailing is restricted</li>
						  <li>Domain, Web Hosting and other services renewal charges may change after one year.</li>
						  <li>By using  the services of "';
						  $msg.=$_SESSION['company'];
						  $msg.='" you agree to be bound by the term &amp;  policies listed at ';
						 
						  $msg.='<a href="http://www.erpdemocompany123.com/terms.php">www.erpdemocompany123.com/terms.php</a>';
						   
						  $msg.='</li>
						  <li>All disputes are subject to Ahmedabad Jurisdiction</li>
						</ul>
						</td>
							<td width="226" valign="top">
							<p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p>
							  <p align="center">For, '.$_SESSION['company'].'</p>
							  <p align="center"><img width="169" height="85" src="http://erp.erpdemocompany123.com/print_invoice_clip_image002.jpg" /></p>
							  <p align="center">AUTHORISED SIGNATORY</p></td>
						  </tr>
						</table>
						<br/>
						<span style="display:block; text-align:center;">This is a Computer Generated Quotation</span>
									</td>
								</tr>
							</table>
						</div>
						</div>';
						 
						//##################################################################
						//      end of send email function by invoice number 
						//##################################################################
					
					// echo $msg."<br/><br/><br/>"; die;
					
					//=====generate pdf========
					// include_once('phpToPDF.php'); 
						// Assign html code into php variable:-
						// $filename = $listEdit[0]['invoice_no'].".pdf";
					// phptopdf_html($msg,'pdf/', 'invoice.pdf'); 
					//====end of pdf generating=====
					
					//=======send email with attachment========
					// reference - AddAttachment
					/* require_once('classes/class.phpmailer.php');
					require_once('classes/classPhpMailer.php');
					
					$email = new PHPMailer();
					$email->From      = '<EMAIL>';
					$email->FromName  = 'Omkar Management';
					$email->Subject   = 'Invoice';
					$email->Body      = 'Testing with attachment';
					$email->AddAddress('<EMAIL>');

					$file_to_attach = 'http://accounts.omkarmanagement.com/pdf/invoice.pdf';
					$email->AddAttachment($file_to_attach , 'invoice.pdf');
					return $email->Send(); */
					
						$to=$partydet[0]['email'];
						$emailIds = explode(",",$to);
						for($e=0;$e<count($emailIds);$e++){
						// $to="<EMAIL>";
						// echo $emailIds[$e]."<br/>";
						$headers  = "MIME-Version: 1.0\r\n";
						$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
						$headers .= "To:$to\r\n";
						$headers .= "From:<EMAIL>\r\n";
						// mail($emailIds[$e],"Invoice number : ".$listEdit[0]['invoice_no'],$msg,$headers); 
						}
					}
					// die;
		} 		  
		redirect("codeManageQuotation.php?msg=edit");
	}	

	if(isset($_GET['qid']))
	{
		//extract($_POST);
						$id = $_GET['qid'];
						
						//##################################################################
						//      send email function by invoice number 
						//##################################################################
					
							$objQuote->id=$id;
							$listEdit=$objQuote->selectRecById();

							$objQuote->qid = $listEdit[0]['id'];
							$listItems=$objQuote->selectItemsByBillNo();		
						
						$msg='<style>
						#print_header{
							font-size:12px;
							font-family:Verdana, Geneva, sans-serif;
						}
						</style>
						<div style="width:100%; margin-top:60px;" id="print_header">
						<div style="width:600px; margin:0 auto;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
						  <td>
						<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">
						<tr><td colspan="3" style="text-align:center; font-size:25px; font-weight:bold;">Invoice</td></tr>';
						 
						   $msg.='<tr class="toprow">
    <td width="38%" rowspan="3" align="left" valign="top">
    <strong>'.$_SESSION['company'].'</strong><br />
      304-A, Ashoka,<br/>
      Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
      Ahmedabad-380014<br />';
	  $msg.='Ph: +91-79-40391397<br />
      E-Mail: ';
      $msg.='<a href="mailto:<EMAIL>"><EMAIL></a><br />
      Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>';
 	   $msg.='</td>  
<td width="33%" align="left" valign="top">Invoice No. :<br/>
      <strong>'.$listEdit[0]['invoice_no'].'</strong></td>
    <td width="67%" align="left" valign="top">Invoice Date :<br/>
      <strong>'.$listEdit[0]['bdt'].'</strong></td>
    </tr>
  <tr class="toprow">
    <td align="left" valign="top">PO/PI No : <strong>'.$listEdit[0]['po_no'].'</strong></td>
    <td rowspan="2" align="left" valign="top">Mode/Terms of Payment<br/><strong>7 Days</strong></td>
  </tr>
  <tr class="toprow">
    <td align="left" valign="top"><strong>PAN No. : </strong>'.$objCompany->getPannoByCompany($listEdit[0]['company']);  
	$msg.='<br/>';
	 $msg.='<strong>GSTIN : </strong>'.$objCompany->getGstinByCompany($listEdit[0]['company']);
	  $msg.='</td>
  </tr>';
						 
						  $msg.='<tr class="toprow">
							<td colspan="3" align="left" valign="top">
								<strong>M/s :</strong><br/>';
								if(isset($id)){
									$objParty->id=$listEdit[0]['party'];
									$partydet=$objParty->selectRecById();
								}  
								$msg.=$partydet[0]['party_name']."<br/>".$partydet[0]['city'];
							$msg.='</td>
							</tr>
						  </table>
						</td></tr>
						<tr>
						<td>
						<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;">
							<tr>
							  <td width="8%" style="border-right:#999999 1px solid;"><strong>No.</strong></td>
							  <td width="51%" style="border-right:#999999 1px solid;"><strong>Particulars</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Rate</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Quantity</strong></td>
							  <td width="15%" style="border-right:#999999 1px solid; text-align:right;"><strong>Amount</strong></td>
							</tr>';
							// $srn=1;
						  for($i=0;$i<count($listItems);$i++) { 
						  $msg.='<tr>
							<td width="8%" style="border-right:#999999 1px solid;">'.($i+1).'</td>
						    <td width="51%" style="border-right:#999999 1px solid;">';
								$objItem->id=$listItems[$i]['item'];
								$itemdet=$objItem->selectRecById();
								
								$msg.='<strong>"'.$itemdet[0]['item_name'].'</strong><br/>';
								$msg.=$listItems[$i]['description'];
							
							$msg.='</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['rate_per_qty'].'</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['no_of_items'].'</td>
							<td width="15%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['amount'].'</td>
						  </tr>';
						}   
						  $msg.='<tr class="toprow">
							<td colspan="4" style="text-align:right;">Total Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['net_amount'].'</td></tr>';
							
						   /* <tr>
							<td colspan="4" style="text-align:right;">Discount :</td>
							<td style="text-align:right;">';
							$msg.= ($listEdit[0]['discount']) ? $listEdit[0]['discount'] : 0;
							$msg.='</td></tr> */
							
							$msg.='<tr>
							<td colspan="4" style="text-align:right;">Net Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['amount'].'</td>
						  </tr>  
						  <tr><td colspan="5">';

						$msg.='<strong>Amount Chargeable (in words) :</strong> '.ucwords(no_to_words($listEdit[0]['amount'])).' Only';
						
						$msg.='</td></tr>
						</table>
						</td>
						</tr>
						
						<tr>
						<td>
						<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;">
						  <tr>
							<td width="348" valign="top" style="font-size:10px;">
						<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
						<ul>
						  <li>Cheque/Draft/Pay Order should be drawn in favor  of <strong>&quot;'.$_SESSION['company'].'&quot;</strong> payable at Ahmedabad.</li>
						  <li>Work on services shall commence only after clearance of Cheque/ Draft/ Pay Order.</li>
						  <li>Service void if payment commitment failed, bounced cheque is also failed commitment.</li>
						  <li>Bounced cheque will attract charge of Rs. 400/-</li>
						  <li>Bulk Mailing is restricted</li>
						  <li>Domain, Web Hosting and other services renewal charges may change after one year.</li>
						  <li>By using  the services of "AM technologies" you agree to be bound by the term &amp;  policies listed at <a href="http://www.erpdemocompany123.com/terms.php">www.erpdemocompany123.com/terms.php</a> </li>
						  <li>Interest @ 18 % per annum will be charged for delayed payment.</li>
						  <li>All disputes are subject to Ahmedabad Jurisdiction</li>
						</ul>
						</td>
							<td width="226" valign="top">
							<p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p>
							  <p align="center">For, '.$_SESSION['company'].'</p>
							  <p align="center"><img width="169" height="85" src="http://erp.erpdemocompany123.com/print_invoice_clip_image002.jpg" /></p>
							  <p align="center">AUTHORISED SIGNATORY</p></td>
						  </tr>
						</table>
						<br/>
						<span style="display:block; text-align:center;">This is a Computer Generated Invoice</span>
						</td>
						</tr>
						</table>
						</div>
						</div>';
						 
						//##################################################################
						//      end of send email function by invoice number 
						//##################################################################
					
					// echo $msg."<br/><br/><br/>";	 die;
					
					//=====generate pdf========
					// include_once('phpToPDF.php'); 
						// Assign html code into php variable:-
						// $filename = $listEdit[0]['invoice_no'].".pdf";
					// phptopdf_html($msg,'pdf/', 'invoice.pdf'); 
					//====end of pdf generating=====
					
					//=======send email with attachment========
					// reference - AddAttachment
					/* require_once('classes/class.phpmailer.php');
					require_once('classes/classPhpMailer.php');
					
					$email = new PHPMailer();
					$email->From      = '<EMAIL>';
					$email->FromName  = 'Omkar Management';
					$email->Subject   = 'Invoice';
					$email->Body      = 'Testing with attachment';
					$email->AddAddress('<EMAIL>');

					$file_to_attach = 'http://accounts.omkarmanagement.com/pdf/invoice.pdf';
					$email->AddAttachment($file_to_attach , 'invoice.pdf');
					return $email->Send(); */
					
						$to=$partydet[0]['email'];
						$emailIds = explode(",",$to);
						for($e=0;$e<count($emailIds);$e++){
						// $to="<EMAIL>";
						// echo $emailIds[$e]."<br/>";
						$headers  = "MIME-Version: 1.0\r\n";
						$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
						$headers .= "To:$to\r\n";
						$headers .= "From:<EMAIL>\r\n";
						// mail($emailIds[$e],"Invoice number : ".$listEdit[0]['invoice_no'],$msg,$headers); 
						}
		  				// die;
		?>
        <script language="javascript">
			alert("Invoice sent to the client");
		</script>
        <? 
		redirect("codeManageQuotation.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objQuote->delete();
		$objQuote->deleteItemsById();
		
		redirect("codeManageQuotation.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objQuote->status();
		redirect("codeManageQuotation.php?msg=status");		
	}

	if(isset($_POST['btnPrint']))
	{			 
		// $objQuote->status();
		redirect("print_invoice.php?id=$hid");
	}

	if(isset($_GET['btnPrintAll']) && $_GET['from_srno']!="" && $_GET['to_srno']!="")
	{		
		// $objQuote->status();
		redirect("print_invoice_all.php?from_srno=".$_GET['from_srno']."&to_srno=".$_GET['to_srno']);
	}	

	if(isset($_GET['id']))
	{			
		$listEdit=$objQuote->selectRecById();
			
		$objQuote->qid = $listEdit[0]['id'];
		$listItems=$objQuote->selectItemsByBillNo();	
	}
	elseif($_GET['Invoice_Number']!="" || $_GET['Client_Name']!="" ||  ($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="" && $_GET['To_Date']!=""))
	{
		$query = "";
		if($_GET['Invoice_Number']!="")
			$query.=" and quote_no='".$_GET['Invoice_Number']."'";
		if($_GET['Client_Name']!="")
			$query.=" and party ='".$_GET['Client_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			
			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
		
			$query.=" and quote_date between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		
		$query.=" and quote_type!='' ";
		// echo $query; die;
		
		$objQuote->parameters="&Invoice_Number=".$_GET['Invoice_Number']."&Party_Name=".$_GET['Party_Name']."&From_Date=".$_GET['From_Date']."&To_Date=".$_GET['To_Date']."&btnSearch=Search";
		$listRec=$objQuote->paging($query);
    }
	else{	
		$query.=" and financial_year='".$_SESSION['fyear']."'";
		$listRec=$objQuote->paging($query);
	}


    include("html/frmManageQuotation.php");
}
 
 
					function no_to_words($no)
						{
							$words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
							if($no == 0)
							return ' ';
							else {
							$novalue='';
							$highno=$no;
							$remainno=0;
							$value=100;
							$value1=1000;
							while($no>=100) {
							if(($value <= $no) &&($no < $value1)) {
							$novalue=$words["$value"];
							$highno = (int)($no/$value);
							$remainno = $no % $value;
							break;
							}
							$value= $value1;
							$value1 = $value * 100;
							}
							if(array_key_exists("$highno",$words))
							return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
							else {
							$unit=$highno%10;
							$ten =(int)($highno/10)*10;
							return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
							}
							}
							} 
?>