<?php
global $db;
class partyMaster
{
   /* Variable Declaration*/		
	var $tablename='party_master';
	var $id;
	var $party_name;
	var $party_address;
	var $city;
	var $state;
	var $zipcode;
	var $country;
	var $office_no1;
	var $office_no2;
	var $fax_no;
	var $email;
	var $domain_name;
	var $contact_name1;
	var $contact_no1;
	var $email1;
	var $contact_name2;
	var $contact_no2;
	var $email2;
	var $reference_name;
	var $reference_no;

	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function partyMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "INSERT INTO `$this->tablename` (
		    customer_name,
		    contact_number,
		    alternate_number,
		    email,
		    address,
		    area,
		    city,
		    gold_weight,
		    loan_amount,
		    bank_name,
		    source,
		    other_source,
		    gross_weight,
		    net_weight,
		    bank_outstanding_amt,
		    difference_value,
		    total_value,
		    sales_person,
		    tele_caller,  
		    additional_notes,
		    star_ranking,
		    image1,
		    image2,
		    image3,
		    image4,
		    inquiry_status,
		    status
		) VALUES (
		    '$this->customer_name',
		    '$this->contact_number',
		    '$this->alternate_number',
		    '$this->email',
		    '$this->address',
		    '$this->area',
		    '$this->city',
		    '$this->gold_weight',
		    '$this->loan_amount',
		    '$this->bank_name',
		    '$this->source',
		    '$this->other_source',
		    '$this->gross_weight',
		    '$this->net_weight',
		    '$this->bank_outstanding_amt',
		    '$this->difference_value',
		    '$this->total_value',
		    '$this->sales_person',
		    '$this->tele_caller',
		    '$this->additional_notes',
		    '$this->star_ranking',
		    '$this->image1',
		    '$this->image2',
		    '$this->image3',
		    '$this->image4',
		    '$this->inquiry_status',
		    '$this->status'
		)";


		// echo $sql;die();							
		$id=$this->db->insert($sql);
		//$id=mysql_insert_id();
		return($id);
	} 			

	/* Fetch all the records */	
	function select()
	{
		$sql ="select  *, date_format(`created_at`, '%d-%m-%Y') as cdt from `$this->tablename` order by customer_name";
		// echo "<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>" . $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	
	
// 	function selectByQuery($from_date, $to_date)
// 	{
// 	    $where = '';
// 	    if(!empty($_GET['from_date']) && !empty($_GET['to_date'])) {
//             $from_date = $_GET['from_date'];
//             $to_date = $_GET['to_date'];
//             $where .= "where DATE(`created_at`) BETWEEN '$from_date' AND '$to_date'";
//         }
	    
// 		$sql ="select  *, date_format(`created_at`, '%d-%m-%Y') as cdt from `$this->tablename` $where order by customer_name";
// 		// echo "<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>" . $sql; die();
// 		$result=$this->db->select($sql);
// 		return($result);
// 	}	


    function selectByQuery($query)
	{
	   
		$sql ="select  *, date_format(`created_at`, '%d-%m-%Y') as cdt from `$this->tablename` $query order by customer_name";
		// echo "<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>" . $sql; die();
		$result=$this->db->select($sql);
		return($result);
	}

	function selectBySalesperson($salesid, $from_date, $to_date)
	{
	    $where = '';
	    if(!empty($_GET['from_date']) && !empty($_GET['to_date'])) {
            $from_date = $_GET['from_date'];
            $to_date = $_GET['to_date'];
            $where .= " AND DATE(`created_at`) BETWEEN '$from_date' AND '$to_date'";
        }

		$sql ="select  *, date_format(`created_at`, '%d-%m-%Y') as cdt from `$this->tablename` where sales_person = '$salesid' $where order by customer_name";
		// echo "<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>" . $sql; die();
		$result=$this->db->select($sql);
		return($result);
	}	
	
	/* Fetch all the records */	
	function selectStatusAll()
	{
		$sql ="select * from `$this->tablename` where status = 1 order by party_name";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}
	
	/* Fetch all the records */	
	function selectStatus()
	{
		$sql ="select * from `$this->tablename` where status = 1 and country='India' order by party_name";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}
	
	/* Fetch all the records */	
	function selectStatusExp()
	{
		$sql ="select * from `$this->tablename` where status = 1 and country!='India' order by party_name";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}		

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectRecByPartyName($party)
	{
		$sql ="select * from `$this->tablename` 
			   where party_name='$party'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function getPartyName($id)
	{
		$this->id = $id;
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['party_name']);
	}
	
	
	/*Fectch record by id from Database*/		
	function selectRecByContact()
	{
		$sql ="select * from `$this->tablename` 
			   where RIGHT(contact_number, 10) = '$this->contact_number'";
			   //echo $sql; // die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function getGSTNo($id)
	{
		$this->id = $id;
		$sql ="select id, gstin from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['gstin']);
	}
	
	function update()
	{
		$sql = "update `$this->tablename` set
						`customer_name`='$this->customer_name',
					    `contact_number`='$this->contact_number',
					    `alternate_number`='$this->alternate_number',
					    `area`='$this->area',
					    `city`='$this->city',
					    `gold_weight`='$this->gold_weight',
					    `loan_amount`='$this->loan_amount',
					    `bank_name`='$this->bank_name',
					    `source`='$this->source',
					    `other_source`='$this->other_source',
					    `additional_notes`='$this->additional_notes',
					    `star_ranking`='$this->star_ranking',
					    `inquiry_status`='$this->inquiry_status'
				 where `id`=$this->id";
						 // echo "<br/><br/><br/><br/><br/><br/><br/>" . $sql;die();	
		$this->db->edit($sql);		
		return true;
	}
	
	/* function update()
	{
		$sql = "update `$this->tablename` set
						`customer_name`='$this->customer_name',
					    `contact_number`='$this->contact_number',
					    `alternate_number`='$this->alternate_number',
					    `email`='$this->email',
					    `address`='$this->address',
					    `area`='$this->area',
					    `city`='$this->city',
					    `gold_weight`='$this->gold_weight',
					    `loan_amount`='$this->loan_amount',
					    `bank_name`='$this->bank_name',
					    `source`='$this->source',
					    `other_source`='$this->other_source',
					    `gross_weight`='$this->gross_weight',
            		    `net_weight`='$this->net_weight',
            		    `bank_outstanding_amt`='$this->bank_outstanding_amt',
            		    `difference_value`='$this->difference_value',
            		    `total_value`='$this->total_value',
					    `sales_person`='$this->sales_person',
					    `additional_notes`='$this->additional_notes',
					    `star_ranking`='$this->star_ranking',
					    `image1`='$this->image1',
					    `image2`='$this->image2',
					    `image3`='$this->image3',
					    `image4`='$this->image4',
					    `inquiry_status`='$this->inquiry_status'
				 where `id`=$this->id";
						 // echo "<br/><br/><br/><br/><br/><br/><br/>" . $sql;die();	
		$this->db->edit($sql);		
		return true;
	} */

	/* update the record in database*/		
	function updateByTelecaller()
	{
		$sql = "update `$this->tablename` set
						`customer_name`='$this->customer_name',
					    `contact_number`='$this->contact_number',
					    `alternate_number`='$this->alternate_number',
					    `email`='$this->email',
					    `address`='$this->address',
					    `area`='$this->area',
					    `city`='$this->city',
					    `gold_weight`='$this->gold_weight',
					    `loan_amount`='$this->loan_amount',
					    `bank_name`='$this->bank_name',
					    `source`='$this->source',
					    `other_source`='$this->other_source',
					    `sales_person`='$this->sales_person',
					    `additional_notes`='$this->additional_notes',
					    `star_ranking`='$this->star_ranking',
					    `inquiry_status`='$this->inquiry_status'
				 where `id`=$this->id";
						// echo "<br/><br/><br/><br/><br/><br/><br/>" . $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	function updateBySalesperson()
	{
		$sql = "update `$this->tablename` set
		                `gross_weight`='$this->gross_weight',
            		    `net_weight`='$this->net_weight',
            		    `bank_outstanding_amt`='$this->bank_outstanding_amt',
            		    `difference_value`='$this->difference_value',
            		    `total_value`='$this->total_value',
					    `image1`='$this->image1',
					    `image2`='$this->image2',
					    `image3`='$this->image3',
					    `image4`='$this->image4',
					    `inquiry_status`='$this->inquiry_status'
				 where `id`=$this->id";
						 // echo "<br/><br/><br/><br/><br/><br/><br/>" . $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";//echo $sql;die();
		$this->db->sql_query($sql);
	}		

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
		{
			for($i=0;$i<count($chk);$i++)
					{
						$id = $chk[$i];
						$sql="delete from `$this->tablename` where `id` = '$id'";
						$this->db->sql_query($sql);
					}
			return true;
		}	

	function selectRecBySearch($query)
	{
		$sql ="select * from `$this->tablename` where id!=''".$query;
		//echo "<br/><br/><br/><br/><br/>".$sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

					
	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` order by party_name";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	
		/*...paging...*/
	function paging_pending()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` where country = 'India' order by party_name";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 500;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   
	
	function paging_pending_exp()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` where country != 'India' order by party_name";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 500;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   
}		
?>   