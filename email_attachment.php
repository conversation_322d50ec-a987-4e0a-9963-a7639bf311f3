<?
//=======send email with attachment========
// reference - AddAttachment
//	require_once('classes/class.phpmailer.php');
	require_once('classes/classPhpMailer.php');
	
	$email = new PHPMailer();
	$email->From      = '<EMAIL>';
	$email->FromName  = 'Manish';
	$email->Subject   = 'Invoice';
	$email->Body      = 'Testing with attachment';
	$email->AddAddress('<EMAIL>');
	
	$file_to_attach = 'http://erp.erpdemocompany123.com/text.docx';
	$email->AddAttachment($file_to_attach , 'text.docx');
	return $email->Send();
?>					