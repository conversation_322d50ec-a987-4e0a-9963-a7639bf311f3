<?	session_start();
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	$objBill->invoice_type=$_GET['cid'];
	$invoice_det=$objBill->selectRecByBillType();
//	echo $invoice_no[0]['id'];

	$objCat->id=$_GET['cid'];
	$cat_name=$objCat->selectRecById();
	
	echo "AMT/2012-2013/".$cat_name[0]['cat_name']."/".sprintf('%03d',(count($invoice_det)+1));
	
/*	if(!$invoice_no)
		echo $cat_name[0]['cat_name'].sprintf('%05d',1);
	else
		echo $cat_name[0]['cat_name'].sprintf('%05d',($invoice_no[0]['id']+1)); 	*/
?>