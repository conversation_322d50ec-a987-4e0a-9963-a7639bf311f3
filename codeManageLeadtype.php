<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Lead Type ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$objLeadtype->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objLeadtype->lead_type=$Lead_Type;
		
	$objLeadtype->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objLeadtype->insert();	
		redirect("codeManageLeadtype.php?msg=add");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objLeadtype->update();
		 redirect("codeManageLeadtype.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objLeadtype->deleteSelect($chkAction);
					break;
			case 1:
					$objLeadtype->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objLeadtype->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageLeadtype.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objLeadtype->delete();
		redirect("codeManageLeadtype.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objLeadtype->status();
		redirect("codeManageLeadtype.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objLeadtype->selectRecById();						
	}	
	elseif($_GET['Lead_Type']!="")
	{
		$objLeadtype->id = $_GET['Lead_Type'];
		$listRec=$objLeadtype->selectRecById();
    }
	else
		$listRec=$objLeadtype->paging();
		
    include("html/frmManageLeadtype.php");
 } 
?>