<?php error_reporting(0);
ob_start();
session_start();
############################################
#	Database Server                        #
############################################
	include("inc/db.php");
##################################################################
	
##################################################################

// For  the Database file path
include("system/classmysql.inc.php");
require("system/commonfunc.php");
include("system/msg.inc.php");


##################################################################
# Global Varibles                                                #
##################################################################
//set_time_limit(0);
// for the row per pages
define('ROW_PER_PAGE',5);
//For the inc folders
define("INC","inc/");
//For the Image folder ( For image uplaod and the images put and fetch )
define("Images","images/");
//For the Function File of the pages folders
define("FUNC","func/");
//For the path of the system folder
define("SYSTEM","system/");
#####################################################################
#		Database Class                                              #    
#####################################################################
//global $edit;
$obj_db = new dbclass();
function homepage( $org , $hw) {
$img = $org;
$handle = fopen ($img, "rb");
$org = fread ($handle, filesize ($img));
fclose ($handle);
$tnsize = $hw;
$img = imagecreatefromstring( $org );
$w = imagesx( $img );
$h = imagesy( $img );
if($w<=$hw)
{
$hw=$w;
}
if($h<=$hw)
{
$hw=$h;
}
$tnsize = $hw;
if( $w > $h ){
$ratio = $w / $h;
$nw = $tnsize;
$nh = $nw / $ratio;
}
else{
$ratio = $h / $w;
$nh = $tnsize;
$nw = $nh /$ratio;
}
$nh=round($nh,0);
//$nh=$nh*3;
$nw=round($nw,0);
return "height='$nh' width='$nw'";
}