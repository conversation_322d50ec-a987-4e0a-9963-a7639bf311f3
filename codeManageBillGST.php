<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Bill ";
	$pageName='codeManageBillGST.php';
    include("inc/clsObj.php");	
	extract($_POST);	
	$bdate = explode("-",$Bill_Date);
	$bill_date=$bdate[2]."-".$bdate[1]."-".$bdate[0];

	//$rdate = explode("-",$Renewal_Date);
	//$renewal_date=$rdate[2]."-".$rdate[1]."-".$rdate[0];
	$renewal_date="";

	$objBill->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objBill->company=$_SESSION['company'];
	$objBill->financial_year=$_SESSION['fyear'];
	$objBill->invoice_no=$Invoice_No;
	$objBill->po_no=$PO_No;
	$objBill->invoice_type="";
	$objBill->bill_type=strtoupper($chkgst);
	// $objBill->party=$Party_Name;
	$objBill->bill_date=$bill_date;
	$objBill->renewal_date=$renewal_date;
	$invoice_terms=str_replace("\&quot;","", $invoice_terms);		
	$invoice_terms=str_replace('\\', '', $invoice_terms);				
	$invoice_terms=str_replace("\'","'", $invoice_terms);		
	$invoice_terms=str_replace("'","\'", $invoice_terms);
	$objBill->invoice_terms=$invoice_terms;
	$objBill->net_amount=$net_amount;
	//$objBill->discount=$Discount;
	if($chkgst=="gst"){
		$objBill->cgst_rate=$cgst_rate;
		$objBill->cgst_amt=$cgst_amount;
		$objBill->sgst_rate=$sgst_rate;
		$objBill->sgst_amt=$sgst_amount;
		$objBill->igst_rate=0;
		$objBill->igst_amt=0;
	}
	elseif($chkgst=="igst"){
		$objBill->cgst_rate=0;
		$objBill->cgst_amt=0;
		$objBill->sgst_rate=0;
		$objBill->sgst_amt=0;
		$objBill->igst_rate=$igst_rate;
		$objBill->igst_amt=$igst_amount;
	}
	$objBill->amount=$total_amount;
	$objBill->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']) || isset($_POST['btnUpdate']))
	{
		if($Party_Name!=""){
			if(is_numeric($Party_Name)){
				$objParty->id = trim($Party_Name);
				$party_list=$objParty->selectRecById();
				
				if(count($party_list)>0)
					$objBill->party=$party_list[0]['id'];
			}
			/* else{
				$party_list=$objParty->selectRecByPartyName(trim($Party_Name));
				if(count($party_list)>0)
					$objBill->party=$party_list[0]['id'];
				else
				{
					$objParty->party_name=trim($Party_Name);	
					$objParty->status=1;
					$objBill->party=$objParty->insert();
				}
			} */
		}
		elseif($Company_Name!=""){
			//=================Add New Client============
			$objParty->party_name=strtoupper($Company_Name);
				$Party_Address=str_replace("\&quot;","", $Party_Address);		
				$Party_Address=str_replace('\\', '', $Party_Address);				
				$Party_Address=str_replace("\'","'", $Party_Address);		
				$Party_Address=str_replace("'","\'", $Party_Address);
			$objParty->party_address=$Party_Address;
			$objParty->city=$City;
			$objParty->state=$State;
			$objParty->zipcode=$Zipcode;
			$objParty->country=$Country;
			$objParty->office_no1=$Office_Number1;
			$objParty->email=$Email_Address;
			$objParty->domain_name=$Website;
			$objParty->contact_name1=$Contact_Name1;
			$objParty->contact_no1=$Contact_Number1;
			$objParty->email1=$Email_Address1;
			$objParty->status=1;
			if(is_numeric($Area_Name)){
				$objArea->id = trim($Area_Name);
				$area_list=$objArea->selectRecById();
			
				if(count($area_list)>0)
					$objParty->area=$area_list[0]['id'];
			}
			else{
				$area_list=$objArea->selectRecByArea(trim($Area_Name));
				if(count($area_list)>0)
					$objParty->area=$area_list[0]['id'];
				else
				{
					$objArea->area_name=trim($Area_Name);	
					$objArea->status=1;
					$objParty->area=$objArea->insert();
				}
			}
			$objBill->party=$objParty->insert();
			//===========end of New client===============
		}
	}
	
	if(isset($_POST['btnAdd']))
	{
		$bid = $objBill->insert();	
		// echo $bid;
		//=============update last bill no in increment master======
		$ino = explode("/",$Invoice_No);
		$objYear->company=$_SESSION['company'];
		$objYear->financial_year=$_SESSION['fyear'];
		$objYear->lbno = $ino[2];
		$objYear->updateCompanyBillNo();
		//========end of last updated bill no.================
		
		for($i=1;$i<=10;$i++)
		{
		
			$description="";
			// $od = "Order_Date".$i;
			// $odate = explode("-",$$od);
			// $orddate=$odate[2]."-".$odate[1]."-".$odate[0];
			$no_of_items = "No_of_Items".$i;

			/*$editorName = "richEdit".($i-1);
			$$editorName = stripslashes($$editorName);
			
			if($$editorName!="")
				$description = $$editorName;
			else
				$description = "Item_Detail".$i; */
			$itemname = "Item_Name".$i;
			$newitemname = "New_Item".$i; // if new item
			$description = "Item_Detail".$i;
			$rateperqty = "Rate".$i;
			$amount = "Amount".$i;
			$discount = "Discount".$i;
			$taxable_amount = "Taxable_Amount".$i;
			$cgst = "cgst".$i;
			$sgst = "sgst".$i;
			$igst = "igst".$i;
			$total_amount = "total_amount".$i;
			
			if($$itemname=="" && $$newitemname!=""){
				//=======Add new item in master=======//
				$objItem->item_name=$$newitemname;	
				$objItem->price=$$rateperqty;	
				$objItem->outsource_price="";	
				$objItem->description=$$description;	
				$objItem->status=1;	
				$objBill->item=$objItem->insert();
				//========End of new item add in======//
			}
			else
				$objBill->item=$$itemname;
				
			if($$itemname!="" || $$newitemname!=""){
				$objBill->bid=$bid;
				$objBill->description=$$description;
				$objBill->no_of_items=$$no_of_items;
				$objBill->rate_per_qty=$$rateperqty ;
				$objBill->amount=$$amount;
				$objBill->discount=$$discount;
				$objBill->taxable_amount=$$taxable_amount;
				$objBill->cgst=$$cgst;
				$objBill->sgst=$$sgst;
				$objBill->igst=$$igst;
				$objBill->total_amount=$$total_amount;
				$objBill->insert_items();	
			}			
		}
		 
?>
		<script language="javascript">
			var msg="Want to Print for the invoice"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				document.location="codeManageBillGST.php?id=<?=$bid;?>";
				// document.location="print_invoice.php?id=<?//$bid;?>";
			}
			else
			{	
				document.location="codeManageBillGST.php";			 
			}
		</script>			
<?  }

	if(isset($_POST['btnUpdate']))
	{	
		$objBill->update();
		$objBill->delete_items();
		for($i=1;$i<=10;$i++)
		{
			$no_of_items = "No_of_Items".$i;
			/* if($$itemname!="")
			{
				$objBill->bid=$hid;
				$objBill->description=$$description;
				$objBill->no_of_items=$$no_of_items;
				$objBill->item=$$itemname;
				$objBill->rate_per_qty=$$rateperqty ;
				$objBill->amount=$$amount;
				$objBill->insert_items();
			} */
			$itemname = "Item_Name".$i;
			$newitemname = "New_Item".$i; // if new item
			$description = "Item_Detail".$i;
			$rateperqty = "Rate".$i;
			$amount = "Amount".$i;
			$discount = "Discount".$i;
			$taxable_amount = "Taxable_Amount".$i;
			$cgst = "cgst".$i;
			$sgst = "sgst".$i;
			$igst = "igst".$i;
			$total_amount = "total_amount".$i;
			
			if($$itemname=="" && $$newitemname!=""){
				//=======Add new item in master=======//
				$objItem->item_name=$$newitemname;	
				$objItem->price=$$rateperqty;	
				$objItem->outsource_price="";	
				$objItem->description=$$description;	
				$objItem->status=1;	
				$objBill->item=$objItem->insert();
				//========End of new item add in======//
			}
			else
				$objBill->item=$$itemname;
				
			if($$itemname!="" || $$newitemname!=""){
				$objBill->bid=$hid;
				$objBill->description=$$description;
				$objBill->no_of_items=$$no_of_items;
				$objBill->rate_per_qty=$$rateperqty ;
				$objBill->amount=$$amount;
				$objBill->discount=$$discount;
				$objBill->taxable_amount=$$taxable_amount;
				$objBill->cgst=$$cgst;
				$objBill->sgst=$$sgst;
				$objBill->igst=$$igst;
				$objBill->total_amount=$$total_amount;
				$objBill->insert_items();	
			}
		}	
		?>
        <script language="javascript">
			var msg="Want to Print for the invoice"+
			"\nPress [Esc] or click on [Cancel] to cancel the operation";
			if(confirm(msg))
			{ 
				document.location="codeManageBillGST.php?id=<?=$hid;?>";
				//document.location="print_invoice.php?id=<?=$hid;?>";
			}
			else
			{	
				document.location="codeManageBillGST.php?msg=edit";			 
			}
		</script>
        <?
	}

	if(isset($_GET['delItem']))
	{	
		$taxable_amt = 0;
		
		$objBill->id=$_GET['delItem'];
		$objBill->delete_item();
		
		$objBill->bid=$_GET['bid'];
		$listItems=$objBill->selectItemsByBillNo();	
		for($i=0;$i<count($listItems);$i++)
		{
			$taxable_amt+=$listItems[$i]['taxable_amount'];
		}
		$objBill->id=$_GET['bid'];
		$billRec=$objBill->selectRecById();	
		
		//$objBill->amount=$total_amt-$billRec[0]['discount'];
		$objBill->net_amount=$taxable_amt;
		$objBill->cgst_amt = ($taxable_amt*$billRec[0]['cgst_rate'])/100;
		$objBill->sgst_amt = ($taxable_amt*$billRec[0]['sgst_rate'])/100;
		$objBill->igst_amt = 0;
		
		$objBill->stax_amt = ($total_amt*$billRec[0]['stax_per'])/100;
		$objBill->edu_amt = ($objBill->stax_amt*$billRec[0]['edu_per'])/100;
		$objBill->hsedu_amt = ($objBill->stax_amt*$billRec[0]['hsedu_per'])/100;
		$objBill->amount=$taxable_amt + $objBill->cgst_amt + $objBill->sgst_amt + $objBill->stax_amt + $objBill->edu_amt + $objBill->hsedu_amt;
		
		$objBill->update_amount();	
		//die;
		redirect("codeManageBillGST.php?id=".$_GET['bid']);
	}

	if(isset($_POST['btnAction']))
	{
	
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objBill->deleteSelect($chkAction);
					break;
			case 1:
					$objBill->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objBill->statusUpdateUnPublish($chkAction);
					break;
			case 3:
					// $objBill->sendEmail($chkAction);
					for($s=0;$s<count($chkAction);$s++)
					{
						$id = $chkAction[$s];
						
						//##################################################################
						//      send email function by invoice number 
						//##################################################################
					
						$objBill->id=$id;
						$listEdit=$objBill->selectRecById();

						$objBill->bid = $listEdit[0]['id'];
						$listItems=$objBill->selectItemsByBillNo();		
						
						$msg='<style>
						#print_header{
							font-size:12px;
							font-family:Verdana, Geneva, sans-serif;
						}
						</style>
						<div style="width:100%; margin-top:60px;" id="print_header">
						<div style="width:600px; margin:0 auto;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
						  <td>
						<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">
						<tr><td colspan="3" style="text-align:center; font-size:25px; font-weight:bold;">Invoice</td></tr>
						  <tr class="toprow">
							<td width="38%" rowspan="3" align="left" valign="top">
							<strong>'.$_SESSION['company'].'</strong><br />
							  304-A, Ashoka,<br/>
							  Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
							  Ahmedabad-380014<br />'; 
							  $msg.='Ph: +91-79-40391397<br />
							  E-Mail : <a href="mailto:<EMAIL>"><EMAIL></a><br />
							  Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>';
							  $msg.='</td>  
						<td width="33%" align="left" valign="top">Invoice No. :<br/>
							  <strong>'.$listEdit[0]['invoice_no'].'</strong></td>
							<td width="67%" align="left" valign="top">Invoice Date :<br/>
							  <strong>'.$listEdit[0]['bdt'].'</strong></td>
							</tr>
						  <tr class="toprow">
							<td align="left" valign="top">PO/PI No : <strong>'.$listEdit[0]['po_no'].'</strong></td>
							<td align="left" valign="top" rowspan="2">Mode/Terms of Payment<br/><strong>7 Days</strong></td>
						  </tr>
						  <tr class="toprow">
    <td align="left" valign="top"><strong>PAN No. : </strong>'.$objCompany->getPannoByCompany($listEdit[0]['company']);
	$msg.='<br/>';
	$msg.='<strong>GSTIN : </strong>'.$objCompany->getGstinByCompany($listEdit[0]['company']);
	$msg.='</td>
  </tr>
						  <tr class="toprow">
							<td colspan="3" align="left" valign="top">
								<strong>M/s :</strong><br/>';
								if(isset($id)){
									$objParty->id=$listEdit[0]['party'];
									$partydet=$objParty->selectRecById();
								}  
								$msg.=$partydet[0]['party_name']."<br/>".$partydet[0]['city'];
							$msg.='</td>
							</tr>
						  </table>
						</td></tr>
						<tr>
						<td>
						<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;">
							<tr>
							  <td width="8%" style="border-right:#999999 1px solid;"><strong>No.</strong></td>
							  <td width="51%" style="border-right:#999999 1px solid;"><strong>Particulars</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Rate</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Quantity</strong></td>
							  <td width="15%" style="border-right:#999999 1px solid; text-align:right;"><strong>Amount</strong></td>
							</tr>';
							// $srn=1;
						  for($i=0;$i<count($listItems);$i++) { 
						  $msg.='<tr>
							<td width="8%" style="border-right:#999999 1px solid;">'.($i+1).'</td>
						    <td width="51%" style="border-right:#999999 1px solid;">';
								$objItem->id=$listItems[$i]['item'];
								$itemdet=$objItem->selectRecById();
								
								$msg.='<strong>"'.$itemdet[0]['item_name'].'</strong><br/>';
								$msg.=$listItems[$i]['description'];
							
							$msg.='</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['rate_per_qty'].'</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['no_of_items'].'</td>
							<td width="15%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['amount'].'</td>
						  </tr>';
						}   
						  $msg.='<tr class="toprow">
							<td colspan="4" style="text-align:right;">Total Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['net_amount'].'</td></tr>';
						   /* <tr>
							<td colspan="4" style="text-align:right;">Discount :</td>
							<td style="text-align:right;">';
							$msg.= ($listEdit[0]['discount']) ? $listEdit[0]['discount'] : 0;
							$msg.='</td></tr> */
							
							$msg.='<tr>
							<td colspan="4" style="text-align:right;">Net Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['amount'].'</td>
						  </tr>  
						  <tr><td colspan="5">';

						$msg.='<strong>Amount Chargeable (in words) :</strong> '.ucwords(no_to_words($listEdit[0]['amount'])).' Only';
						
						$msg.='</td></tr>
						</table>
						</td>
						</tr>
						<tr>
						<td>
						<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;">
						  <tr>
							<td width="348" valign="top" style="font-size:10px;">
						<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
						<ul>
						  <li>Cheque/Draft/Pay Order should be drawn in favor  of <strong>&quot;';
						  $msg.='ERP Demo';
						  $msg.='&quot;</strong> payable at Ahmedabad.</li>
						  <li>Work on services shall commence only after clearance of Cheque/ Draft/ Pay Order.</li>
						  <li>Service void if payment commitment failed, bounced cheque is also failed commitment.</li>
						  <li>Bounced cheque will attract charge of Rs. 400/-</li>
						  <li>Bulk Mailing is restricted</li>
						  <li>Domain, Web Hosting and other services renewal charges may change after one year.</li>
						  <li>By using  the services of "';
						  $msg.='ERP Demo';
						  $msg.='" you agree to be bound by the term &amp;  policies listed at ';

						  $msg.='<a href="http://www.erpdemocompany123.com/terms.php">www.erpdemocompany123.com/terms.php</a>';
						  $msg.='</li>
						  <li>All disputes are subject to Ahmedabad Jurisdiction</li>
						</ul>
						</td>
							<td width="226" valign="top">
							<p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p>
							  <p align="center">For, '.$_SESSION['company'].'</p>
							  <p align="center"><img width="169" height="85" src="http://erp.erpdemocompany123.com/print_invoice_clip_image002.jpg" /></p>
							  <p align="center">AUTHORISED SIGNATORY</p></td>
						  </tr>
						</table>
						<br/>
						<span style="display:block; text-align:center;">This is a Computer Generated Invoice</span>
									</td>
								</tr>
							</table>
						</div>
						</div>';
						 
						//##################################################################
						//      end of send email function by invoice number 
						//##################################################################
					
					// echo $msg."<br/><br/><br/>"; die;
					
					//=====generate pdf========
					// include_once('phpToPDF.php'); 
						// Assign html code into php variable:-
						// $filename = $listEdit[0]['invoice_no'].".pdf";
					// phptopdf_html($msg,'pdf/', 'invoice.pdf'); 
					//====end of pdf generating=====
					
					//=======send email with attachment========
					// reference - AddAttachment
					/* require_once('classes/class.phpmailer.php');
					require_once('classes/classPhpMailer.php');
					
					$email = new PHPMailer();
					$email->From      = '<EMAIL>';
					$email->FromName  = 'Omkar Management';
					$email->Subject   = 'Invoice';
					$email->Body      = 'Testing with attachment';
					$email->AddAddress('<EMAIL>');

					$file_to_attach = 'http://accounts.omkarmanagement.com/pdf/invoice.pdf';
					$email->AddAttachment($file_to_attach , 'invoice.pdf');
					return $email->Send(); */
					
						$to=$partydet[0]['email'];
						$emailIds = explode(",",$to);
						for($e=0;$e<count($emailIds);$e++){
						// $to="<EMAIL>";
						// echo $emailIds[$e]."<br/>";
						$headers  = "MIME-Version: 1.0\r\n";
						$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
						$headers .= "To:$to\r\n";
						$headers .= "From:<EMAIL>\r\n";
						// mail($emailIds[$e],"Invoice number : ".$listEdit[0]['invoice_no'],$msg,$headers); 
						}
					}
					// die;
		} 		  
		redirect("codeManageBillGST.php?msg=edit");
	}	

	if(isset($_GET['bid']))
	{
		//extract($_POST);
						$id = $_GET['bid'];
						
						//##################################################################
						//      send email function by invoice number 
						//##################################################################
					
							$objBill->id=$id;
							$listEdit=$objBill->selectRecById();

							$objBill->bid = $listEdit[0]['id'];
							$listItems=$objBill->selectItemsByBillNo();		
						
						$msg='<style>
						#print_header{
							font-size:12px;
							font-family:Verdana, Geneva, sans-serif;
						}
						</style>
						<div style="width:100%; margin-top:60px;" id="print_header">
						<div style="width:600px; margin:0 auto;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
						  <td>
						<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">
						<tr><td colspan="3" style="text-align:center; font-size:25px; font-weight:bold;">Invoice</td></tr>';
						 
						   $msg.='<tr class="toprow">
    <td width="38%" rowspan="3" align="left" valign="top">
    <strong>'.$_SESSION['company'].'</strong><br />
      304-A, Ashoka,<br/>
      Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
      Ahmedabad-380014<br />';
	  $msg.='Ph: +91-79-40391397<br />
      E-Mail: ';
      $msg.='<a href="mailto:<EMAIL>"><EMAIL></a><br />
      Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>';
	   $msg.='</td>  
<td width="33%" align="left" valign="top">Invoice No. :<br/>
      <strong>'.$listEdit[0]['invoice_no'].'</strong></td>
    <td width="67%" align="left" valign="top">Invoice Date :<br/>
      <strong>'.$listEdit[0]['bdt'].'</strong></td>
    </tr>
  <tr class="toprow">
    <td align="left" valign="top">PO/PI No : <strong>'.$listEdit[0]['po_no'].'</strong></td>
    <td rowspan="2" align="left" valign="top">Mode/Terms of Payment<br/><strong>7 Days</strong></td>
  </tr>
  <tr class="toprow">
    <td align="left" valign="top"><strong>PAN No. : </strong>'.$objCompany->getPannoByCompany($listEdit[0]['company']);
	$msg.='<br/>';
	$msg.='<strong>GSTIN : </strong>'.$objCompany->getGstinByCompany($listEdit[0]['company']);
	$msg.='</td>
  </tr>';
						 
						  $msg.='<tr class="toprow">
							<td colspan="3" align="left" valign="top">
								<strong>M/s :</strong><br/>';
								if(isset($id)){
									$objParty->id=$listEdit[0]['party'];
									$partydet=$objParty->selectRecById();
								}  
								$msg.=$partydet[0]['party_name']."<br/>".$partydet[0]['city'];
							$msg.='</td>
							</tr>
						  </table>
						</td></tr>
						<tr>
						<td>
						<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;">
							<tr>
							  <td width="8%" style="border-right:#999999 1px solid;"><strong>No.</strong></td>
							  <td width="51%" style="border-right:#999999 1px solid;"><strong>Particulars</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Rate</strong></td>
							  <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Quantity</strong></td>
							  <td width="15%" style="border-right:#999999 1px solid; text-align:right;"><strong>Amount</strong></td>
							</tr>';
							// $srn=1;
						  for($i=0;$i<count($listItems);$i++) { 
						  $msg.='<tr>
							<td width="8%" style="border-right:#999999 1px solid;">'.($i+1).'</td>
						    <td width="51%" style="border-right:#999999 1px solid;">';
								$objItem->id=$listItems[$i]['item'];
								$itemdet=$objItem->selectRecById();
								
								$msg.='<strong>"'.$itemdet[0]['item_name'].'</strong><br/>';
								$msg.=$listItems[$i]['description'];
							
							$msg.='</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['rate_per_qty'].'</td>
							<td width="13%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['no_of_items'].'</td>
							<td width="15%" style="border-right:#999999 1px solid; text-align:right;">'.$listItems[$i]['amount'].'</td>
						  </tr>';
						}   
						  $msg.='<tr class="toprow">
							<td colspan="4" style="text-align:right;">Total Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['net_amount'].'</td></tr>';
						   /* <tr>
							<td colspan="4" style="text-align:right;">Discount :</td>
							<td style="text-align:right;">';
							$msg.= ($listEdit[0]['discount']) ? $listEdit[0]['discount'] : 0;
							$msg.='</td></tr> */
							
							$msg.='<tr>
							<td colspan="4" style="text-align:right;">Net Amount :</td>
							<td style="text-align:right;">'.$listEdit[0]['amount'].'</td>
						  </tr>  
						  <tr><td colspan="5">';

						$msg.='<strong>Amount Chargeable (in words) :</strong> '.ucwords(no_to_words($listEdit[0]['amount'])).' Only';
						
						$msg.='</td></tr>
						</table>
						</td>
						</tr>
						
						<tr>
						<td>
						<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;">
						  <tr>
							<td width="348" valign="top" style="font-size:10px;">
						<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
						<ul>
						  <li>Cheque/Draft/Pay Order should be drawn in favor  of <strong>&quot;ERP Demo&quot;</strong> payable at Ahmedabad.</li>
						  <li>Work on services shall commence only after clearance of Cheque/ Draft/ Pay Order.</li>
						  <li>Service void if payment commitment failed, bounced cheque is also failed commitment.</li>
						  <li>Bounced cheque will attract charge of Rs. 400/-</li>
						  <li>Bulk Mailing is restricted</li>
						  <li>Domain, Web Hosting and other services renewal charges may change after one year.</li>
						  <li>By using  the services of "AM technologies" you agree to be bound by the term &amp;  policies listed at <a href="http://www.erpdemocompany123.com/terms.php">www.erpdemocompany123.com/terms.php</a> </li>
						  <li>Interest @ 18 % per annum will be charged for delayed payment.</li>
						  <li>All disputes are subject to Ahmedabad Jurisdiction</li>
						</ul>
						</td>
							<td width="226" valign="top">
							<p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p>
							  <p align="center">For, '.$_SESSION['company'].'</p>
							  <p align="center"><img width="169" height="85" src="http://erp.erpdemocompany123.com/print_invoice_clip_image002.jpg" /></p>
							  <p align="center">AUTHORISED SIGNATORY</p></td>
						  </tr>
						</table>
						<br/>
						<span style="display:block; text-align:center;">This is a Computer Generated Invoice</span>
						</td>
						</tr>
						</table>
						</div>
						</div>';
						 
						//##################################################################
						//      end of send email function by invoice number 
						//##################################################################
					
					// echo $msg."<br/><br/><br/>";	 die;
					
					//=====generate pdf========
					// include_once('phpToPDF.php'); 
						// Assign html code into php variable:-
						// $filename = $listEdit[0]['invoice_no'].".pdf";
					// phptopdf_html($msg,'pdf/', 'invoice.pdf'); 
					//====end of pdf generating=====
					
					//=======send email with attachment========
					// reference - AddAttachment
					/* require_once('classes/class.phpmailer.php');
					require_once('classes/classPhpMailer.php');
					
					$email = new PHPMailer();
					$email->From      = '<EMAIL>';
					$email->FromName  = 'Omkar Management';
					$email->Subject   = 'Invoice';
					$email->Body      = 'Testing with attachment';
					$email->AddAddress('<EMAIL>');

					$file_to_attach = 'http://accounts.omkarmanagement.com/pdf/invoice.pdf';
					$email->AddAttachment($file_to_attach , 'invoice.pdf');
					return $email->Send(); */
					
						$to=$partydet[0]['email'];
						$emailIds = explode(",",$to);
						for($e=0;$e<count($emailIds);$e++){
						// $to="<EMAIL>";
						// echo $emailIds[$e]."<br/>";
						$headers  = "MIME-Version: 1.0\r\n";
						$headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
						$headers .= "To:$to\r\n";
						$headers .= "From:<EMAIL>\r\n";
						// mail($emailIds[$e],"Invoice number : ".$listEdit[0]['invoice_no'],$msg,$headers); 
						}
		  				// die;
		?>
        <script language="javascript">
			alert("Invoice sent to the client");
		</script>
        <? 
		redirect("codeManageBillGST.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objBill->delete();
		$objBill->deleteItemsById();
		
		redirect("codeManageBillGST.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objBill->status();
		redirect("codeManageBillGST.php?msg=status");		
	}

	if(isset($_POST['btnPrint']))
	{			 
		// $objBill->status();
		redirect("print_invoice.php?id=$hid");
	}

	if(isset($_GET['btnPrintAll']) && $_GET['from_srno']!="" && $_GET['to_srno']!="")
	{		
		// $objBill->status();
		redirect("print_invoice_all.php?from_srno=".$_GET['from_srno']."&to_srno=".$_GET['to_srno']);
	}	

	if(isset($_GET['id']))
	{			
		$listEdit=$objBill->selectRecById();
			
		$objBill->bid = $listEdit[0]['id'];
		$listItems=$objBill->selectItemsByBillNo();	
	}
	elseif($_GET['Invoice_Number']!="" || $_GET['Client_Name']!="" ||  ($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="" && $_GET['To_Date']!=""))
	{
		$query = " and bm.bill_type!=''";
		if($_GET['Invoice_Number']!="")
			$query.=" and bm.invoice_no like '%".$_GET['Invoice_Number']."%'";
		if($_GET['Client_Name']!="")
			$query.=" and bm.party ='".$_GET['Client_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			
			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
		
			$query.=" and bm.bill_date between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and bm.financial_year='".$_SESSION['fyear']."'";
		
		// echo $query; die;
		
		$objBill->parameters="&Invoice_Number=".$_GET['Invoice_Number']."&Party_Name=".$_GET['Party_Name']."&From_Date=".$_GET['From_Date']."&To_Date=".$_GET['To_Date']."&btnSearch=Search";
		$listRec=$objBill->paging($query);
    }
	else	
	{	
		$query.=" and bm.financial_year='".$_SESSION['fyear']."'";
		$listRec=$objBill->selectAll($query);
	}
    $totalAmount = $objBill->getTotalAll($query);
	include("html/frmManageBillGST.php");
}
 
function no_to_words($no)
{
	$words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
	if($no == 0)
		return ' ';
	else{
		$novalue='';
		$highno=$no;
		$remainno=0;
		$value=100;
		$value1=1000;
		while($no>=100) {
			if(($value <= $no) &&($no < $value1)) {
				$novalue=$words["$value"];
				$highno = (int)($no/$value);
				$remainno = $no % $value;
				break;
			}
			$value= $value1;
			$value1 = $value * 100;
		}
		if(array_key_exists("$highno",$words))
			return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
		else{
			$unit=$highno%10;
			$ten =(int)($highno/10)*10;
			return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
		}
	}
} 
?>