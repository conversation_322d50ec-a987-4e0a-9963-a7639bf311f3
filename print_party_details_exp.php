<?	session_start();
	error_reporting(0);
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();
//include("template.php");
//function main()
//{
    include("inc/clsObj.php");	
/*	$objBill->id=$_GET['id'];

	$listEdit=$objBill->selectRecById();
		
	$objBill->bid = $listEdit[0]['id'];
	$listItems=$objBill->selectItemsByBillNo();		*/
	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}

td
{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
</style>

<div style="width:100%;" id="print_header">
<? if(isset($_GET['pid']) && $_GET['pid']!=""){ ?>
<div style="width:600px; margin:0 auto;">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
<tr>
  <td>
	<? if(isset($_GET['pid'])){
        $objParty->id=$_GET['pid'];
        $partydet=$objParty->selectRecById();
    }  ?>
<table width="100%" border="1" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 1px solid;">
<tr><td colspan="3" align="center">
<strong style="font-size:25px; font-weight:bold;"><?=strtoupper($_SESSION['company']);?></strong><br />
     <?=$objCompany->getInvoiceHeaderByCompany($_SESSION['company']);?>
       <strong>PAN No. : </strong><?=$objCompany->getPannoByCompany($_SESSION['company']);?>
   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>GSTIN : </strong><?=$objCompany->getGstinByCompany($_SESSION['company']);?>
</td></tr>
<tr><td colspan="2" style="text-align:left; font-weight:bold;">Ledger Account : <?=$partydet[0]['party_name'];?><br/><?=$partydet[0]['city'];?></td>
  <td width="48%" style="text-align:left; font-weight:bold;">
  <? if($_GET['From_Date']!="")
  		echo "Date From : <br/>".$_GET['From_Date']." To : ".$_GET['To_Date'];
	else
		echo "Financial Year : <br/>".$_SESSION['fyear'];
	?>
  </td>
</tr>
</table>
</td></tr>
<tr>
<td>
<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;">
    <tr style="background:#CCC;">
      <td width="8%" style="border-right:#999999 1px solid;"><strong>Sr No.</strong></td>
      <td width="36%" style="border-right:#999999 1px solid;"><strong>Particulars</strong></td>
      <td width="15%" style="border-right:#999999 1px solid;"><strong>Date</strong></td>
      <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Debit</strong></td>
      <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><strong>Credit</strong></td>
      <td width="15%" style="border-right:#999999 1px solid; text-align:right;"><strong>Balance</strong></td>
    </tr>
<?	
	$cfcredit = 0;
	$cfdebit = 0;
	$total_debit = 0;
	$total_credit = 0;
	$total_amount = 0;
	
	if($_GET['From_Date']=="" && $_GET['To_Date']=="")
	{
	 	$fy = explode("-",$_SESSION['fyear']);
		// $_GET['From_Date'] = "01-04-".$fy[0];
		// $_GET['To_Date'] = "31-03-".$fy[1];
		$fdate = "01-04-".$fy[0];
		$tdate = "31-03-".$fy[1];
	}
	else
	{
		$fdate = $_GET['From_Date'];
		$tdate = $_GET['To_Date'];
	}

	$fd_format = explode("-",$fdate);
	$fd = $fd_format[2]."-".$fd_format[1]."-".$fd_format[0];
	$fdtime = strtotime($fd);
	// echo $fdtime."<br/>";

	$td_format = explode("-",$tdate);
	$td = $td_format[2]."-".$td_format[1]."-".$td_format[0];
	$tdtime = strtotime($td);
	// echo $tdtime."<br/><br/>";

	$currency_symbol = "";
//=========calculation for carry forward amount===========
	$objBill->bdate=$fd;
	$objBill->party=$_GET['pid'];
	$bd=$objBill->selectSumByPartyBeforeDate();
//	echo $bilDate[0]['totamt']."<br/>";
	if(count($bd) > 0)
		$currency_symbol = $objCrn->getCurrencySymbol($bd[0]['currency']);
	
	/*$objPurchase->bdate=$fd;
	$objPurchase->party=$_GET['pid'];
	$pd=$objPurchase->selectSumByPartyBeforeDate(); */
//	echo $purchaseDate[0]['totamt']."<br/>";
	
	$objCash->bdate=$fd;
	$objCash->party=$_GET['pid'];
	$cd=$objCash->selectSumByPartyBeforeDate();
//	echo $cashDate[0]['credit_amount']."-".$cashDate[0]['debit_amount']."<br/>";
	if(count($cd)>0 && $currency_symbol=="")
		$currency_symbol = $objCrn->getCurrencySymbol($cd[0]['currency']);

	$objTds->bdate=$fd;
	$objTds->party=$_GET['pid'];
	$tds=$objTds->selectSumByPartyBeforeDate();
	if(count($tds) > 0 && $currency_symbol=="")
		$currency_symbol = $objCrn->getCurrencySymbol($tds[0]['currency']);
	
	$final_amount = $bd[0]['totamt'] - $pd[0]['totamt'] - ($cd[0]['credit_amount'] - $cd[0]['debit_amount'])-$tds[0]['credit_amount'];
//	echo $final_amount;

	if($final_amount <= 0) 
		$cfcredit=-($final_amount); 	
	else
		$cfdebit=$final_amount; 	
		
//==============end of carry forward amount===============	

	//======select transaction date from bill master===
	$objBill->party=$_GET['pid'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `bill_date` between '".$fd."' and '".$td."'";
		$bilDate=$objBill->selectSearchDateByParty($query);
	}
	else
		$bilDate=$objBill->selectDateByParty();

	for($b=0;$b<count($bilDate);$b++)
	{
		$adt[] = $bilDate[$b]['tdate'];
	}

	//======select transaction date from purchase master===
	$objPurchase->party=$_GET['pid'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `date` between '".$fd."' and '".$td."'";
		$purchaseDate=$objPurchase->selectSearchDateByParty($query);
	}
	else
		$purchaseDate=$objPurchase->selectDateByParty();
	
	
	for($p=0;$p<count($purchaseDate);$p++)
	{
		$adt[] = $purchaseDate[$p]['tdate'];
	}
	
	//======select transaction date from Cashbook===
	$objCash->party=$_GET['pid'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `transaction_date` between '".$fd."' and '".$td."'";
		$cashDate=$objCash->selectSearchDateByParty($query);
	}
	else
		$cashDate=$objCash->selectDateByParty();
	
	for($c=0;$c<count($cashDate);$c++)
	{
		$adt[] = $cashDate[$c]['tdate'];
	}

	//======select transaction date from TDS===
	$objTds->party=$_GET['pid'];
	if(isset($_GET['From_Date']) && isset($_GET['To_Date']) && $_GET['From_Date']!="" && $_GET['To_Date']!="")
	{
		$query = "and `transaction_date` between '".$fd."' and '".$td."'";
		$tdsDate=$objTds->selectSearchDateByParty($query);
	}
	else
		$tdsDate=$objTds->selectDateByParty();
	
	for($t=0;$t<count($tdsDate);$t++)
	{
		$adt[] = $tdsDate[$t]['tdate'];
	}

	for($a=0;$a<count($adt);$a++) 
	{
		$dt_format = explode("-",$adt[$a]);
		$dl = $dt_format[0]."-".$dt_format[1]."-".$dt_format[2];
			// echo $dl."<br/>";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			if(strtotime($dl)<=$tdtime && strtotime($dl)>=$fdtime)
				$datelist[$a] = strtotime($dl);
		}
		else
			$datelist[$a] = strtotime($dl);
	}

	array_multisort($datelist);
	$datelist = array_values(array_unique($datelist));

	for($d=0;$d<count($datelist);$d++)
	{
		$adate[$d] = date("Y-m-d",$datelist[$d]);
//		echo $adate[$d]."<br/>";
	}

if($_GET['From_Date']=="" && $_GET['To_Date']=="")
{ ?>
<tr>
              <td width="8%" style="border-right:#999999 1px solid;">&nbsp;</td>
              <td width="36%" style="border-right:#999999 1px solid;">C/F
              </td>
              <td width="15%" style="border-right:#999999 1px solid;">&nbsp;</td>
              <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=$currency_symbol;?> <?=($cfdebit>0) ? number_format($cfdebit,2) : "-"; ?></td>
              <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($cfcredit!=0) ? $currency_symbol : "";?> <?=($cfcredit>0) ? number_format($cfcredit,2) : "-"; ?></td>
              <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
              <?=$currency_symbol;?>
              <? 
                if($cfdebit>0) 
				{
                    echo number_format($cfdebit,2)." Dr.";
					$balance = $cfdebit;
					$total_debit+=$cfdebit;
				}
                else
				{
                    echo number_format($cfcredit,2)." Cr.";
					$balance = -$cfcredit;
					$total_credit+=$cfcredit;
				}
					
				$balance = ($billRec[$b]['amount']>0) ? $balance + $billRec[$b]['amount'] : $balance;					
              ?>
              </td>
            </tr>
	<? }
	
	
	for($i=0;$i<count($adate);$i++) 
	{
		// echo $adate[$i]."<br/>";
		//=============check records from invoice===============
		$objBill->party=$_GET['pid'];
		$objBill->tdt = $adate[$i];
		$billRec=$objBill->selectRecByPartyDate();
		for($b=0;$b<count($billRec);$b++)
		{ 
			if($currency_symbol=="")
				$currency_symbol = $objCrn->getCurrencySymbol($billRec[$b]['currency']);
			?>
            <tr>
              <td width="8%" style="border-right:#999999 1px solid;"><?=$sr+=1;?></td>
              <td width="36%" style="border-right:#999999 1px solid;">
              <? echo $billRec[$b]['invoice_no']; ?>
              </td>
              <td width="15%" style="border-right:#999999 1px solid;"><?=$billRec[$b]['bdt'];?></td>
              <td width="13%" style="border-right:#999999 1px solid; text-align:right;"><?=($billRec[$b]['amount']>0) ? $currency_symbol  : "";?> <?=($billRec[$b]['amount']>0) ? $billRec[$b]['amount'] : "-";
              $total_debit+=$billRec[$b]['amount'];
              ?></td>
              <td width="13%" style="border-right:#999999 1px solid; text-align:right;">-</td>
              <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
              <?=$currency_symbol;?>&nbsp;
              <? $balance = ($billRec[$b]['amount']>0) ? $balance + $billRec[$b]['amount'] : $balance; 
                if($balance >= 0) 
                    echo $balance." Dr.";
                else
                    echo -($balance)." Cr.";
              ?>
              </td>
            </tr>
<? 		}
//====================end of checking bill invoice========================

		//=============check records from purchase===============
		$objPurchase->party=$_GET['pid'];
		$objPurchase->tdt = $adate[$i];
		$purchaseRec=$objPurchase->selectRecByPartyDate();
		for($p=0;$p<count($purchaseRec);$p++)
		{ 
			$sr+=1; 
			if($currency_symbol=="")
				$currency_symbol = $objCrn->getCurrencySymbol($purchaseRec[$p]['currency']);
		?>
        <tr>
          <td width="8%" style="border-right:#999999 1px solid;"><?=$sr;?></td>
          <td width="36%" style="border-right:#999999 1px solid;">
          <? echo $purchaseRec[$p]['invoice_no']; ?>
          </td>
          <td width="15%" style="border-right:#999999 1px solid;"><?=$purchaseRec[$p]['bdt'];?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;">-</td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;">
		  <?=($purchaseRec[$p]['amount']>0) ? $currency_symbol : "";?>&nbsp;
		  <?=($purchaseRec[$p]['amount']>0) ? $purchaseRec[$p]['amount'] : "-" ;
		   $total_credit+=$purchaseRec[$p]['amount'];?></td>
          <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
          <?=$currency_symbol;?>&nbsp;
			<? $balance = ($purchaseRec[$p]['amount']>0) ? $balance - $purchaseRec[$p]['amount'] : $balance; 
		  	if($balance >= 0) 
				echo $balance." Dr.";
			else
				echo -($balance)." Cr.";
		  ?>
          </td>
        </tr>
<? 	}
	//============end of checking purchase===============
	
	//=============check records from cashbook===============
		$objCash->party=$_GET['pid'];
		$objCash->tdt = $adate[$i];
		$cbRec=$objCash->selectRecByPartyDate();

		for($c=0;$c<count($cbRec);$c++)
		{ $sr+=1; ?>
        <tr>
          <td width="8%" style="border-right:#999999 1px solid;"><?=$sr;?></td>
          <td width="36%" style="border-right:#999999 1px solid;"><? 
		  if($cbRec[$c]['transaction_detail']!="") echo $cbRec[$c]['transaction_detail']."<br/>";
		  if($cbRec[$c]['cheque_cash']==1 && $cbRec[$c]['cheque_no']!="")
	  			echo "Cheque : ".$cbRec[$c]['cheque_no']."<br/>".$cbRec[$c]['cheque_detail'];
		  elseif($cbRec[$c]['cheque_cash']==0)
				echo "Cash";?></td>
          <td width="15%" style="border-right:#999999 1px solid;"><?=$cbRec[$c]['bdt'];?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;">
		  <?=($cbRec[$c]['debit_amount']>0) ? $currency_symbol : "";?>&nbsp;
		  <?=($cbRec[$c]['debit_amount']>0) ? $cbRec[$c]['debit_amount'] : "-" ;
		  $total_debit+=$cbRec[$c]['debit_amount'];
		  ?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;">
		  <?=($cbRec[$c]['credit_amount']>0) ? $currency_symbol : "";?>&nbsp;
		  <?=($cbRec[$c]['credit_amount']>0) ? $cbRec[$c]['credit_amount'] : "-";
		  $total_credit+=$cbRec[$c]['credit_amount'];
		  ?></td>
          <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
          <?=$currency_symbol;?>&nbsp;
		<? $balance = $balance + $cbRec[$c]['debit_amount'] - $cbRec[$c]['credit_amount']; 
		  	if($balance >= 0) 
				echo $balance." Dr.";
			else
				echo -($balance)." Cr.";
		  ?>
          </td>
        </tr>
	<? 	}
		//====================end of checking cashbook========================


//=============check records from TDS===============
		$objTds->party=$_GET['pid'];
		$objTds->tdt = $adate[$i];
		$tdsRec=$objTds->selectRecByPartyDate();

		for($t=0;$t<count($tdsRec);$t++)
		{ $sr+=1; ?>
        <tr>
          <td width="8%" style="border-right:#999999 1px solid;"><?=$sr;?></td>
          <td width="36%" style="border-right:#999999 1px solid;"><? 
		  if($tdsRec[$t]['transaction_detail']!="") echo $tdsRec[$t]['transaction_detail']."<br/>";
		  if($tdsRec[$t]['cheque_cash']==1 && $tdsRec[$t]['cheque_no']!="")
	  			echo "Cheque : ".$tdsRec[$t]['cheque_no']."<br/>".$tdsRec[$t]['cheque_detail'];
		  elseif($tdsRec[$t]['cheque_cash']==0)
				echo "TDS";?></td>
          <td width="15%" style="border-right:#999999 1px solid;"><?=$tdsRec[$t]['bdt'];?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;">
		  <?=($tdsRec[$t]['debit_amount']>0) ? $currency_symbol : "";?>
		  <?=($tdsRec[$t]['debit_amount']>0) ? $tdsRec[$t]['debit_amount'] : "-" ;
		  $total_debit+=$tdsRec[$t]['debit_amount'];
		  ?></td>
          <td width="13%" style="border-right:#999999 1px solid; text-align:right;">
		  <?=($tdsRec[$t]['credit_amount']>0) ? $currency_symbol : "";?>&nbsp;
		  <?=($tdsRec[$t]['credit_amount']>0) ? $tdsRec[$t]['credit_amount'] : "-";
		  $total_credit+=$tdsRec[$t]['credit_amount'];
		  ?></td>
          <td width="15%" style="border-right:#999999 1px solid; text-align:right;">
          <?=($tdsRec[$t]['credit_amount']>0) ? $currency_symbol : "";?>&nbsp;
		<? $balance = $balance + $tdsRec[$t]['debit_amount'] - $tdsRec[$t]['credit_amount'];
			if($balance >= 0) 
				echo $balance." Dr.";
			else
				echo -($balance)." Cr.";
		  ?>
          </td>
        </tr>
	<? 	}
		//====================end of checking cashbook========================

} ?>  
  <tr class="toprow">
    <td colspan="3" style="text-align:right;"><strong>Total Amount :</strong></td>
    <td style="text-align:right;"><strong><?=$currency_symbol;?>&nbsp;<?=number_format($total_debit,2);?></strong></td>
    <td style="text-align:right;"><strong><?=$currency_symbol;?>&nbsp;<?=number_format($total_credit,2);?></strong></td>
    <td style="text-align:right;"><strong><?=$currency_symbol;?>&nbsp;<? $total_amount = $total_debit-$total_credit;
		echo ($total_amount>=0) ? $total_amount." Dr." : -($total_amount)." Cr."; ?></strong>
    </td></tr>
    </table>
</td>
</tr>
</table>
</div>
<? }
else{ 
	$listRec=$objParty->paging_pending();
?>
<div style="width:800px; margin:0 auto;">
<table width="100%" border="1" cellspacing="0" cellpadding="3" style="border-collapse:collapse; border:#CCCCCC 1px solid;">
  <tr><td colspan="6" align="center">
<strong style="font-size:25px; font-weight:bold;"><?=$_SESSION['company'];?></strong><br />
      304-A,  Ashoka,<br/>
      Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
      Ahmedabad-380014<br />
      
      Ph: +91-79-40391397<br />
      E-Mail: 
      <a href="mailto:<EMAIL>"><EMAIL></a><br />
      Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>
      
  </td></tr>
  
  <tr style="background:#666666; color:#FFFFFF;">
    <td width="9%"><strong>Sr. No. </strong></th>
    <td width="29%"><strong>Party</strong></th>
	<td width="15%"><strong>Contact Person</strong></th>
	<td width="18%"><strong>Contact No.</strong></th>
	<td width="18%"><strong>Contact Email</strong></th>
	<td width="11%" align="right"><strong>Pending Amount</strong></th>
  </tr>
  
   <?  $n=0;
   		$total_amt = 0;
    if(count($listRec)>0)
	 {
	 	$currency_symbol="";
      	$colorflg=0;
	   	for($e=0;$e<count($listRec);$e++)
		{
		
			$objBill->party=$listRec[$e]['id'];
			$bilDate=$objBill->selectSumByParty();
			if(count(bilDate) > 0)
				$currency_symbol = $objCrn->getCurrencySymbol($bilDate[0]['currency']);
		
			/* $objPurchase->party=$listRec[$e]['id'];
			$purchaseDate=$objPurchase->selectSumByParty(); */
			
			$objCash->party=$listRec[$e]['id'];
			$cashDate=$objCash->selectSumByParty();
			if(count(cashDate) > 0 && $currency_symbol=="")
				$currency_symbol = $objCrn->getCurrencySymbol($cashDate[0]['currency']);
			
			$objTds->party=$listRec[$e]['id'];
			$partyTds=$objTds->selectSumByParty();
			if(count(partyTds) > 0 && $currency_symbol=="")
				$currency_symbol = $objCrn->getCurrencySymbol($partyTds[0]['currency']);
			
			$final_amount = $bilDate[0]['totamt'] - $purchaseDate[0]['totamt'] - ($cashDate[0]['credit_amount'] - $cashDate[0]['debit_amount'])-$partyTds[0]['credit_amount'];
		if($final_amount > 0)
		{ $n++;
			$total_amt+=$final_amount;
		
		  if ($colorflg==1){ $colorflg=0;?>
  		  <tr class="odd" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='odd'">
   <? }	else {	$colorflg=1;?>
		 <tr class="even" onmouseover="Javascript:this.className='rowhover'" onmouseout="Javascript:this.className='even'">
   <? } ?>
                <td><?=$n;?></td>
                <td>
                <a href="codePartyStatusReport.php?Party_Name=<? echo $listRec[$e]['id']; ?>" style="font-size:12px; text-decoration:none; color:#333333;"><? echo $listRec[$e]['party_name']; ?></a></td>
                <td><? echo $listRec[$e]['contact_name1']; ?></td>
                <td><? echo $listRec[$e]['contact_no1']; ?></td>
                <td><? echo $listRec[$e]['email1']; ?></td>
                <td align="right"><?=($final_amount > 0) ? number_format($final_amount,2) : 0; ?></td>
	     </tr>
  <? }
  } ?>
  <tr style="background:#333333; color:#FFFFFF; height:30px;"><td colspan="5" style="font-weight:bold;">Total Pending Amount : </td><td align="right" style="font-weight:bold;"><?=number_format($total_amt,2);?></td></tr> 
  <?
  } else { ?>
  		<tr>
        	<td colspan="11" align="center">
            	No Records Found...</td>
		</tr>
    <?php }?>    
  </table>
</div>
<? } ?>
</div>
<script language="javascript">
	window.print();
</script>
<? // } ?>