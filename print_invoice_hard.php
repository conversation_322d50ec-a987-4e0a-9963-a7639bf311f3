<?	session_start(); 
	error_reporting(0);	
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>ERP Demo</title>
</head>
<?
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	checkLogin();	

	$objBill->id=$_GET['id'];

	$listEdit=$objBill->selectRecById();
		
	$objBill->bid = $listEdit[0]['id'];
	$listItems=$objBill->selectItemsByBillNo();	
	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
.tbl_invoice td{
	font-size:13px!important;
	padding:3px!important;
}

.tbl_invoice td ul, .tbl_invoice td ol{
	padding:0px 20px!important;
	margin-top:0px!important;
}

.tbl_invoice td ul li, .tbl_invoice td ol li{
	font-size:12px!important;
}


.tbl_invoice td strong{
	font-size:13px!important;
}
table {
    border:solid #999 !important;
    border-width:1px !important;
}
th, td {
    border:solid #999 !important;
    border-width:1px !important;
}
</style>
<body>
<div style="width:100%; margin-top:60px;" id="print_header">
<div style="width:600px; margin:0 auto;">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
<tr>
  <td>
<table width="100%" border="1" cellspacing="0" cellpadding="5">
<tr><td colspan="3" style="text-align:center; font-size:25px; font-weight:bold;">Invoice</td></tr>
  <tr class="toprow">
    <td width="38%" rowspan="3" align="left" valign="top">
    <strong><?=$_SESSION['company'];?></strong><br />
      304-A, Ashoka,<br/>
      Opp. Abhushan Complex,<br/>S.P. Stadium Road,  Navrangpura,<br />
      Ahmedabad-380014<br />
      
      Ph: +91-79-40391397<br />
      E-Mail: 
      <a href="mailto:<EMAIL>"><EMAIL></a><br />
      Web: <a href="http://www.erpdemocompany123.com">www.erpdemocompany123.com</a>
      </td>  
<td width="33%" align="left" valign="top">Invoice No. :<br/>
      <strong><?=$listEdit[0]['invoice_no'];?></strong></td>
    <td width="67%" align="left" valign="top">Invoice Date :<br/>
      <strong><?=$listEdit[0]['bdt'];?></strong></td>
    </tr>
  <tr class="toprow">
    <td align="left" valign="top">PO/PI No : <strong><?=$listEdit[0]['po_no'];?></strong></td>
    <td rowspan="2" align="left" valign="top">Mode/Terms of Payment<br/><strong>7 Days</strong></td>
  </tr>
  <tr class="toprow">
    <td align="left" valign="top"><strong>PAN No. : </strong><?=$objCompany->getPannoByCompany($listEdit[0]['company']);?><br/>
    <strong>GSTIN : </strong><?=$objCompany->getGstinByCompany($listEdit[0]['company']);?></td>
  </tr>
  <tr class="toprow">
    <td colspan="3" align="left" valign="top">
    	<strong>M/s :</strong><br/>
		<? if(isset($_GET['id'])){
            $objParty->id=$listEdit[0]['party'];
            $partydet=$objParty->selectRecById();
        }  ?>
		<?=$partydet[0]['party_name']."<br/>".$partydet[0]['city'];?>    </td>
    </tr>
  </table>
</td></tr>
<tr>
<td>
<table width="100%" border="1" cellspacing="0" cellpadding="5">
    <tr>
      <td width="5%" style=" text-align:center;" ><strong>No.</strong></td>
      <td width="50%" style=" text-align:center;" ><strong>Particulars</strong></td>
      <td width="17%" style="text-align:center;"><strong>Rate</strong></td>
      <td width="10%" style="text-align:center;"><strong>Quantity</strong></td>
      <td width="18%" style="text-align:center;"><strong>Amount</strong></td>
    </tr>
  <? for($i=0;$i<count($listItems);$i++) { ?>
  <tr>
  	<td  valign="top" style=" text-align:center;"><?=$i+1;?></td>
   	<td  valign="top">
   	<? 	$objItem->id=$listItems[$i]['item'];
		$itemdet=$objItem->selectRecById();
		
		echo "<strong>".$itemdet[0]['item_name']."</strong><br/>";
		echo $listItems[$i]['description'];	?>
	</td>
    <td style="text-align:center;" valign="top"><img src="images/inr_img.png" width="8" height="10">  <?=$listItems[$i]['rate_per_qty'];?></td>
    <td style="text-align:center;" valign="top"><?=$listItems[$i]['no_of_items'];?></td>
    <td  style="text-align:right;" valign="top"><img src="images/inr_img.png" width="8" height="10">  <?=$listItems[$i]['amount'];?></td>
  </tr>
<? } ?>  
  <tr class="toprow">
    <td colspan="4" style="text-align:right;">Total Amount :</td>
	<td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10">  <?=$listEdit[0]['net_amount'];?></td></tr>
<?php /*?>    <tr>
    <td colspan="4" style="text-align:right;">Discount :</td>
	<td style="text-align:right;"><?=($listEdit[0]['discount']) ? $listEdit[0]['discount'] : 0;?></td></tr><?php */?>
   
    <tr>
    <td colspan="4" style="text-align:right;">Net Amount :</td>
    <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10">  <?=$listEdit[0]['amount'];?></td>
  </tr>  
  <tr><td colspan="5">
<?
function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }

echo "<strong>Amount Chargeable (in words) :</strong> Rs. ".ucwords(no_to_words($listEdit[0]['amount']))." Only";
?>
</td></tr>
</table>
</td>
</tr>

<tr>
<td>
<table border="1" cellspacing="0" cellpadding="5" width="100%" class="tbl_invoice">
  <tr>
    <td width="348" valign="top">
<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
<ul>
  <li>Cheque/Draft/Pay Order should be drawn in favor  of <strong>&quot;<?=$_SESSION['company'];?>&quot;</strong> payable at Ahmedabad.</li>
  <li>Service void if payment commitment failed, bounced cheque is also failed commitment.</li>
  <li>Bounced cheque will attract charge of Rs. 400/-</li>
  <li>Bulk Mailing is restricted</li>
  <li>Domain, Web Hosting and other services renewal charges may change after one year.</li>
  <li>By using  the services of "<?=$_SESSION['company'];?>" you agree to be bound by the term &amp;  policies listed at <a href="http://www.erpdemocompany123.com/terms.php">www.erpdemocompany123.com/terms.php</a></li>
  <li>Interest @ 18 % per annum will be charged for delayed payment.</li>
  <li>All  disputes are subject to Ahmedabad Jurisdiction</li>
</ul>

<?php /*?><ul>
  <li>All services are for one year duration only,  unless specified otherwise.</li>
  <li>All information including text &amp; picture to  be provided by the client in soft copy who should also be the legal copyright  owner for the same.</li>
  <li><strong>&ldquo;<?=$_SESSION['company'];?>&rdquo;</strong> shall not be liable for any  claims / damages arising out of content posted in your website.</li>
  <li>Cheque/Draft/Pay Order should be drawn in favor  of <strong>&ldquo;<?=$_SESSION['company'];?>&rdquo;</strong> payable at Ahmedabad.</li>
  <li>Work on services shall commence only after  clearance of Cheque/ Draft/ Pay Order.</li>
  <li>Service void if payment commitment failed,  bounced cheque is also failed commitment.</li>
  <li>Bounced cheque will attract charge of Rs. 400/-</li>
  <li>Bulk Mailing is restricted</li>
  <li>Domain, Web Hosting and other services renewal  charges may change after one year.</li>
  <li>For any addition/modification in website, web  hosting packages and other services provided by <strong>&ldquo;<?=$_SESSION['company'];?>&rdquo;</strong> charges will  be extra.</li>
  <li>All disputes are subject to Ahmedabad  Jurisdiction</li>
</ul><?php */?>

<?php /*?><ol>
  <li>Payment should be in favor of<br/><strong>&ldquo;<?=$_SESSION['company'];?>&rdquo;</strong> payable at Ahmedabad.</li>
  <li>Subject to Ahmedabad Jurisdiction.</li>
  <li>Service void if payment commitment       failed, bounced cheque is also failed commitment.</li>
  <li>Bounced Cheque Return will attract a charge  of Rs. 400/-.</li>
  <li>Bulk mailing is restricted.</li>
  <li>Domain &amp; Web Hosting Renewal charges       may change as per relevant market rate.</li>
  <li>Website Maintenance will be chargeable.</li>
  <li>Website Promotion Renewal will be yearly basis.</li>
  <li>All <?=$_SESSION['company'];?> Combo Package will be yearly basis.</li>
</ol><?php */?>

</td>
    <td width="226" valign="top">
      <br/>
      <p align="center">For, <?=$_SESSION['company'];?></p>
      <br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>
      <p align="center">AUTHORISED SIGNATORY</p></td>
  </tr>
</table>

</td>
</tr>
</table>
</div>
</div>
<script language="javascript">
	window.print();
</script>
</body>
</html>