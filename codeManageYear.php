<?php
include("template.php");
function main()
{
	include("inc/clsObj.php");	
	$heading="<span>Manage</span> Financial Year ";
	$pageName="codeManageYear.php";	
	extract($_POST);	
	$objYear->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	
	$objYear->year_name=$Year_Name;	
	$yearDet = $objYear->selectByYear();
	// echo count($yearDet); die;
	$objYear->current_year=(!empty($Current_Year)) ? 1 : 0;
	
	$objYear->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		if(count($yearDet)>0)
			redirect("codeManageYear.php?msg=exist");
		else{
			$objYear->insert();
			
			$objYear->company_name=$_SESSION['company'];
			$objYear->last_bill_id=0;
			$objYear->last_bill_exp_id=0;
			$objYear->last_cashbook_id=0;
			$objYear->insertIncrement();

			redirect("codeManageYear.php?msg=add");			
		}
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objYear->update();
		 redirect("codeManageYear.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objYear->deleteSelect($chkAction);
					break;
			case 1:
					$objYear->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objYear->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageYear.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objYear->delete();
		redirect("codeManageYear.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objYear->status();
		redirect("codeManageYear.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objYear->selectRecById();						
	}	
	elseif($_GET['Year_Name']!="")
	{
		$objYear->id = $_GET['Year_Name'];
		$listRec=$objYear->selectRecById();
    }
	else
		$listRec=$objYear->paging();
		
    include("html/frmManageYear.php");
 } 
?>