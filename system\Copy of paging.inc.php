<?
class Paging
	{
	   var $sql;
	   var $page; // current page;
	   var $limit; // total pages to show in the list
	   var $pagination; // pagination links...
	   var $CONN;
	   var $parameters;
		
	function Paging() { //constructor
		$conn = mysql_connect(SERVER_NAME,USER_NAME,PASSWORD);	
		if(!$conn) 
		{	
			$this->error("Connection attempt failed");		}

	if(!mysql_select_db(DB_NAME,$conn)) 
	{	$this->error("Database Selection failed");		}
		$this->CONN = $conn;
		return true;
	}
				
			
//////////////////////////////////
//  function paging START --//
//////////////////////////////////
	function GeneratePaging(){
		
	    $list=split("/", $_SERVER['PHP_SELF']);
		$targetpage=$list[count($list)-1];;	
		$parameters = $this->parameters;
		if(empty($this->sql)) { return false; }
	
		if(!eregi("^select",$this->sql))
		{
			echo "Wrong Query<hr>SQL<p>";
			echo "<H2>Wrong function silly!</H2>\n";
			return false;
		}		
	
		if(empty($this->CONN)) { return false; }
		$conn = $this->CONN;
		
		// How many adjacent pages should be shown on each side?
		$adjacents = 3;

		/* 
		   First get total number of rows in data table. 
		   If you have a WHERE clause in your query, make sure you mirror it here.
		*/
		/* Setup vars for query. */
		if($this->page) 
			$start = ($this->page - 1) * $this->limit; 			
			//first item to display on this page
		else
			$start = 0;
			//if no page var is given, set start to 0
		
		/* Get data. */
		$total_pages=mysql_query($this->sql,$conn);
		$total_pages =mysql_num_rows($total_pages);
   
		$this->sql.= " LIMIT $start, $this->limit";
		$result = mysql_query($this->sql);
	
		/* Setup page vars for display. */
		if ($this->page == 0) $this->page = 1;					
		//if no page var is given, default to 1.
		$prev = $this->page - 1;							
		//previous page is page - 1
		$next = $this->page + 1;							
		//next page is page + 1

		$lastpage = ceil($total_pages/$this->limit);		
		//lastpage is=total pages/items per page, rounded up.
		 $lpm1 = $lastpage - 1;						
		//last page minus 1
		
		/* 
			Now we apply our rules and draw the pagination object. 
			We're actually saving the code to a variable in case we want to draw it more than once.
		*/
		$this->pagination = "";
		if($lastpage > 1)
		{	
			$this->pagination .= "<div class=\"pagination\">";
			//previous button
			if ($this->page > 1) 
				$this->pagination.= "<a href=\"$targetpage?page=$prev$parameters\">Previous</a>";
			else
				$this->pagination.= "<span class=\"disabled\">Previous</span>";	
	
			//pages	
			if ($lastpage < 3 + ($adjacents * 2))	//not enough pages to bother breaking it up
			{	
				for ($counter = 1; $counter <= $lastpage; $counter++)
				{
					if ($counter == $this->page)
						$this->pagination.= "<span class=\"current\">$counter</span>";
					else
						$this->pagination.= "<a href=\"$targetpage?page=$counter$parameters\">$counter</a>";					
				}
			}
			elseif($lastpage > 5 + ($adjacents * 2))	//enough pages to hide some
			{
				//close to beginning; only hide later pages
				if($this->page < 1 + ($adjacents * 2))		
				{
					for ($counter = 1; $counter < 4 + ($adjacents * 2); $counter++)
					{
						if ($counter == $this->page)
							$this->pagination.= "<span class=\"current\">$counter</span>";
						else
							$this->pagination.= "<a href=\"$targetpage?page=$counter$parameters\">$counter</a>";					
					}
					$this->pagination.= "...";
					$this->pagination.= "<a href=\"$targetpage?page=$lpm1$parameters\">$lpm1</a>";
					$this->pagination.= "<a href=\"$targetpage?page=$lastpage$parameters\">$lastpage</a>";		
				}
				//in middle; hide some front and some back
				elseif($lastpage - ($adjacents * 2) > $this->page && $this->page > ($adjacents * 2))
				{
					$this->pagination.= "<a href=\"$targetpage?page=1$parameters\">1</a>";
					$this->pagination.= "<a href=\"$targetpage?page=2$parameters\">2</a>";
					$this->pagination.= "...";
					for ($counter = $this->page - $adjacents; $counter <= $this->page + $adjacents; $counter++)
					{
						if ($counter == $this->page)
							$this->pagination.= "<span class=\"current\">$counter</span>";
						else
							$this->pagination.= "<a href=\"$targetpage?page=$counter$parameters\">$counter</a>";					
					}
					$this->pagination.= "...";
					$this->pagination.= "<a href=\"$targetpage?page=$lpm1$parameters\">$lpm1</a>";
					$this->pagination.= "<a href=\"$targetpage?page=$lastpage$parameters\">$lastpage</a>";		
				} // close to end; only hide early pages
				else
				{
					$this->pagination.= "<a href=\"$targetpage?page=1$parameters\">1</a>";
					$this->pagination.= "<a href=\"$targetpage?page=2$parameters\">2</a>";
					$this->pagination.= "...";
					for ($counter = $lastpage - (2 + ($adjacents * 2)); $counter <= $lastpage; $counter++)
					{ 
						if ($counter == $this->page)
							$this->pagination.= "<span class=\"current\">$counter</span>";
						else
							$this->pagination.= "<a href=\"$targetpage?page=$counter$parameters\">$counter</a>";					
					}
				}
			}			
			//next button
			if ($this->page < $counter - 1) 
				$this->pagination.= "<a href=\"$targetpage?page=$next$parameters\">Next</a>";
			else
				$this->pagination.= "<span class=\"disabled\">Next</span>"; 
			    $this->pagination.= "</div>\n";				
		}		
	}	
}
///////////////////////////////////////////////////////
//   function paging END 
////////////////////////////////////////////////////////

?>