<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Department ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$objDept->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objDept->dept_name=$Dept_Name;
		
	$objDept->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objDept->insert();	
		redirect("codeManageDepartment.php?msg=add");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objDept->update();
		 redirect("codeManageDepartment.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objDept->deleteSelect($chkAction);
					break;
			case 1:
					$objDept->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objDept->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageDepartment.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objDept->delete();
		redirect("codeManageDepartment.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objDept->status();
		redirect("codeManageDepartment.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objDept->selectRecById();						
	}	
	elseif($_GET['Dept_Name']!="")
	{
		$objDept->id = $_GET['Dept_Name'];
		$listRec=$objDept->selectRecById();
    }
	else
		$listRec=$objDept->paging();
		
    include("html/frmManageDepartment.php");
 } 
?>