<?php 
include("template.php");
function main()
{
	$heading="Manage Purchase ";
    include("inc/clsObj.php");	
	
	extract($_POST);	
	$cdate = explode("-",$Date);
	$date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

	$objPurchase->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objPurchase->company=$_SESSION['company'];
	$objPurchase->financial_year=$_SESSION['fyear'];
	$objPurchase->party=$Party_Name;
	$objPurchase->invoice_no=$Invoice_No;
	$objPurchase->amount=$Amount;
	$objPurchase->cheque_cash="";
	$objPurchase->cheque_detail="";
	$objPurchase->transaction_detail=$Transaction_Detail;
	$objPurchase->transaction_date=$date;
	
	if(isset($_POST['btnAdd']))
	{
		$objPurchase->insert();
		redirect("codeManagePurchase.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objPurchase->update();
		redirect("codeManagePurchase.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objPurchase->deleteSelect($chkAction);
				break;
			case 1:
				$objPurchase->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objPurchase->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
					
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManagePurchase.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objPurchase->deleteById();
		$objPurchase->deleteItemsById();
		redirect("codeManagePurchase.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objPurchase->status();
		redirect("codeManagePurchase.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objPurchase->selectRecById();

		$objPurchase->cid = $listEdit[0]['id'];
		$listItems=$objPurchase->selectRecById();	
	}
	elseif($_GET['Party_Name']!="" ||  ($_GET['From_Date']!="" && $_GET['To_Date']!=""))
	{
		$query = "";
		if($_GET['Party_Name']!="")
			$query.=" and prm.party='".$_GET['Party_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
		
			$query.=" and prm.date between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		
		$objPurchase->party = $_GET['party'];
		$listRec=$objPurchase->paging($query);
    }
	else
	{
		$query.=" and financial_year='".$_SESSION['fyear']."'";
		$listRec=$objPurchase->paging($query);
	}
	include("html/frmManagePurchase.php");
} 
?>
