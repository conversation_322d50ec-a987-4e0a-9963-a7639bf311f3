<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Inquiry Type ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$objInqtype->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objInqtype->inquiry_type=$Inquiry_Type;
		
	$objInqtype->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objInqtype->insert();	
		redirect("codeManageInquirytype.php?msg=add");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objInqtype->update();
		 redirect("codeManageInquirytype.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objInqtype->deleteSelect($chkAction);
					break;
			case 1:
					$objInqtype->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objInqtype->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageInquirytype.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objInqtype->delete();
		redirect("codeManageInquirytype.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objInqtype->status();
		redirect("codeManageInquirytype.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objInqtype->selectRecById();						
	}	
	elseif($_GET['Inquiry_Type']!="")
	{
		$objInqtype->id = $_GET['Inquiry_Type'];
		$listRec=$objInqtype->selectRecById();
    }
	else
		$listRec=$objInqtype->paging();
		
    include("html/frmManageInquirytype.php");
 } 
?>