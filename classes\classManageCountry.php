<?php
global $db;
class countries
	{
	   var $tablename='countries';
	   var $id;		   
	   var $country;		 
	   var $status;
	   var $limit;
	   var $start;
       function countries()
			{
						$this->db = new dbclass();
			}
	   function insert()
			{	
				$sql = "insert into `$this->tablename` values('',							   
							   '$this->country',
							   '$this->code',							 						   							   							   $this->status)";
								//echo $sql;die();		
					$this->db->insert($sql);
					$id=mysql_insert_id();
					return($id);
			}
		
	function insertFromExcel()
			{	
				$sql = "insert into `$this->tablename` values('',							   
							   '$this->country',							 						   								'$this->code',	
							   1)";
								// echo $sql;die();		
					$this->db->insert($sql);
					$id=mysql_insert_id();
					return($id);
			}
			
	   function update()
			{
				$sql = "update `$this->tablename` set					
								`country`='$this->country'
						    	 where `id`=$this->id";
								 //echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}					
		function select()
			{
				 $sql ="select * from `$this->tablename`";//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}	
	    function selectStatus()
			{
				 $sql ="select * from `$this->tablename` where status=1";
				 	// echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}	
	
		function selectRecById()
			{
				$sql ="select * from `$this->tablename` 
					   where id='$this->id'";//echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result);
			}
		
		function selectCodeByCountry($country)
			{
				$sql ="select code from `$this->tablename` 
					   where country='$country'";//echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result[0]['code']);
			}	
		function selectRecByIdStatus()
			{
				$sql ="select * from `$this->tablename` 
					   where id='$this->id' and status=1";//echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result);
			}
		function selectOurProject()
			{
				$sql ="select * from `$this->tablename` 
					   where our_project=1 and status=1";//echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result);
			}
		function selectRecentProject()
			{
				$sql ="select * from `$this->tablename` 
					   where recent_project=1 and status=1";//echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result);
			}			
		function selectRecByCategoryId()
			{
				$sql ="select * from `$this->tablename` 
					   where categoryid='$this->categoryid' and status=1";
					   //echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result);
			}		
							
		function status()
			{
				$sql = "update `$this->tablename` set
							   `status`='$this->status'
								where `id`=$this->id";//echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}	
		function delete()
			{							 
					$sql="delete from `$this->tablename` 
						  where `id`=$this->id";//echo $sql;die();
					mysql_query($sql);	  
			}	
		function sequenceUpdate()
			{
				$sql = "update `$this->tablename` set
	  						   `seqno`='$this->seqno'
							    where `id`=$this->id";//echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}			
		/*update selected status publish*/	
		function statusUpdatePublish($chk)
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql = "update `$this->tablename` set
									`status`=1
									 where `id`='$id'";//echo $sql;die();	
									$this->db->edit($sql);		
	  				    }
			    return true;
			}	
		/*update selected status unpublish*/	
		function statusUpdateUnPublish($chk)
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql = "update `$this->tablename` set
										   `status`=0
											where `id`='$id'";//echo $sql;die();	
									$this->db->edit($sql);		
	  				    }
			    return true;
			}							
		/*delete the selected record*/	
		function deleteSelect($chk) 
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql="delete from `$this->tablename` where `id` = '$id'";
	     					$res = mysql_query($sql);
						}
				return true;
			}	
		/*...paging...*/
	function paging()
	{
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename`";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		
		$pages->limit = $this->limit;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}		 
	function paging_front()
	{
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` where status=1";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->parameters= "&id=4";
		$pages->limit = 2;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}		 
}		
?>   