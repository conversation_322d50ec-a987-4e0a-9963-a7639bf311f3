<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>Manage Company</h5>
							</div>
							<div class="col-md-6">
								<form action="<?=$pageName;?>" method="post">
								<? if(isset($_REQUEST['btnAddUser']) or isset($_REQUEST['id'])){ ?>
										<button type="button" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack" onclick="document.location='<?=$pageName;?>'">
											<i class="fas fa-angle-left"></i> Back
										</button>
								<? }
								/* elseif($_SESSION['act_add']==1){?>
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add Company
										</button>
								<? } */ ?>
								</form>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<? if(isset($_REQUEST['btnAddUser'])  or isset($_REQUEST['id']) or ($_REQUEST['msg']=="add")){ ?>
						<!--========== Add Company ==========-->
						<form action="codeManageCompany.php" method="post" name="frmManage" >
                        	<input name="hid" type="hidden" value="<?=$_GET['id'];?>" />
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label>Company Name</label>
                                    <input name="company_name" type="text" id="company_name"  value="<?=$listEdit[0]['company_name'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                    <label>GSTIN</label>
                                    <input name="gstin" type="text" id="gstin"  value="<?=$listEdit[0]['gstin'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                    <label>PAN No.</label>
                                    <input name="pan_no" type="text" id="pan_no"  value="<?=$listEdit[0]['pan_no'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-8">
									<label>Bank Details :</label>
                                    <textarea name="bank_details" id="classic-editor"><?=$listEdit[0]['bank_details'];?></textarea>
                                </div>
								<div class="form-group col-md-8">
									<label>Invoice Header :</label>
                                    <textarea name="invoice_header" id="classic-editor1"><?=$listEdit[0]['invoice_header'];?></textarea>
                                </div>
								<div class="form-group col-md-8">
									<label>Quotation Header :</label>
                                    <textarea  name="quotation_header" id="classic-editor2"><?=$listEdit[0]['quotation_header'];?></textarea>
                                </div>
								<div class="form-group col-md-8">
									<label>Quotation Signature :</label>
                                    <textarea  name="quotation_signature" id="classic-editor3"><?=$listEdit[0]['quotation_signature'];?></textarea>
                                </div>
								<div class="form-group col-md-8">
									<label>Terms & Conditions (Invoice) :</label>
                                    <textarea  name="terms_conditions_invoice" id="classic-editor4"><?=$listEdit[0]['terms_conditions_invoice'];?></textarea>
                                </div>
								<div class="form-group col-md-8">
									<label>Terms & Conditions (Quotation) :</label>
                                    <textarea  name="terms_conditions_quotation" id="classic-editor5"><?=$listEdit[0]['terms_conditions_quotation'];?></textarea>
                                </div>
								
                            </div>
                            
                            <?php if(isset($_GET['id'])){?>
                                    <input type="submit" name="btnUpdate" value="Update Company" class="btn btn-success" />
                              <?php } else {?>
                                    <input type="submit" name="btnAdd" value="Add Company" class="btn btn-success"/>
                              <?php } ?> 
                        </form>
						<!--========== Add Company ==========-->
						<? }else{ ?>
						<!--========== List View ==========-->
						<div class="dt-responsive table-responsive">
                        	<form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                             <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
                            <div id="error_frmvalidate" class="err_msg" style="float:right; font-weight:bold; font-style:italic; padding:3px;"></div><div style="clear:both;"></div>
							<table id="tblCompany" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th colspan="2"></th>
										<th colspan="3" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select name="optAction" id="optAction" class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													 <option value="">--Select Action--</option>
													  <? if($_SESSION['act_del']==1){ ?>
                                                      <option value="0">Delete</option><? } ?>
                                                      <? if($_SESSION['act_edit']==1){ ?>
                                                      <option value="1">Published</option>
                                                      <option value="2">Unpublished</option><? } ?>    
												</select>
											</label>
											 <input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right"  value="Submit" name="btnAction" id="btnAction" />
                                            
										</th>			
									</tr>
									<tr>
										<th>Sr. No.</th>
										<th>Company Name</th>
										<th>
											<?php /*?><input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"> Action<?php */?>
										</th>
									</tr>
								</thead>
								<tbody>
                                <? if(count($listRec)>0)
									 {
									   for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
										<td><?=$listRec[$e]['company_name'];?></td>
										<td class="table-action">
                                        <? /* if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
	                <input type="checkbox" name="chkAction[]" id="chkAction[]"  value="<? echo $listRec[$e]['id'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10"/>&nbsp;<? } */ ?>
     		 <?php  if($_SESSION['act_edit']==1){
					/* if($listRec[$e]['status']=='0'){?>
					<a href="?status=1&amp;uid=<?=$listRec[$e]['id'];?>"><i class="fas fa-minus mr-10 clr-red"></i></a>
				<?php } else {?>
					<a href="?status=0&amp;uid=<?=$listRec[$e]['id'];?>"><i class="fas fa-check mr-10 clr-green"></i></a>
				<?php } */	?>
					<a href="<?=$pageName;?>?id=<?=$listRec[$e]['id'];?>"><i class="fas fa-pencil-alt mr-10"></i></a><?  } 
					/* if($_SESSION['act_del']==1){ ?><a onclick="deleteAll(<?php echo $listRec[$e]['id'];?>,'<?php echo $listRec[$e]['company_name'];?>','Item','<?=$pageName;?>')">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
<? } */ ?>
										</td>
									</tr>
                                    <? }
									} ?>
								</tbody>
							</table>
                            </form>
						</div>
						<!--========== List View ==========-->
						<? } ?>
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>