<?php 
	class dbclass {	
		var $CONN;
		function dbclass() { //constructor
			$conn = mysqli_connect(SERVER_NAME,USER_NAME,PASSWORD);	
			if(!$conn) 
				{	$this->error("Connection attempt failed");		}
	if(!mysqli_select_db($conn,DB_NAME)) 
				{	$this->error("Database Selection failed");		}
			$this->CONN = $conn;
			return true;
		}
		//_____________close connection____________//
		function close(){
			$conn = $this->CONN ;
			$close = mysqli_close($conn);
			if(!$close){
			  $this->error("Close Connection Failed");	}
			return true;
		}
		function error($text) {
			$no = mysqli_errno();
			$msg = mysqli_error();
			echo "<hr><font face=verdana size=2>";
			echo "<b>Custom Message :</b> $text<br><br>";
			echo "<b>Error Number :</b> $no<br><br>";
			echo "<b>Error Message	:</b> $msg<br><br>";
			echo "<hr></font>";
			exit;
		}
		//_____________select records___________________//
		function select ($sql=""){
			
			if(empty($sql)) { return false; }
			
			if(preg_match("^select",$sql)){	
			  echo "Wrong Query<hr>$sql<p>";
					return false;		} 
			
			if(empty($this->CONN)) { return false; }
			$conn = $this->CONN;
			$results = @mysqli_query($conn,$sql);			
			if((!$results) or empty($results))	{	return false;		}
			$count = 0;
			$data  = array();
			while ( $row = mysqli_fetch_array($results))	{	
				$data[$count] = $row;
				$count++;		}
			mysqli_free_result($results);
			return $data;
		}
	 
	    //________insert record__________________//
		function insert ($sql=""){
			if(empty($sql)) { return false; }
			if(preg_match("^insert",$sql)){	return false;		}
			if(empty($this->CONN)){	return false;		}
			$conn = $this->CONN;			
			$results = @mysqli_query($conn,$sql);			
			if(!$results){
				$this->error("Insert Operation Failed..<hr>$sql<hr>");
				return false;		}
			$id = mysqli_insert_id($conn);
			return $id;
		}
	    //___________edit and modify record___________________//
		function edit($sql="")	{
			if(empty($sql)) { 	return false; 		}
			if(preg_match("^update",$sql)){	return false;		}
			if(empty($this->CONN)){	return false;		}
			$conn = $this->CONN;
			$results = @mysqli_query($conn,$sql);
			$rows = 0;
			$rows = @mysqli_affected_rows();
			return $rows;
		}
		//____________generalize for all queries___________//
		function sql_query($sql="")	{	
			
			if(empty($sql)) { return false; }
			if(empty($this->CONN)) { return false; }
			$conn = $this->CONN;
			$results = mysqli_query($conn,$sql) or $this->error("Something wrong in query<hr>$sql<hr>");
			
			if(!$results){
			   $this->error("Query went bad ! <hr>$sql<hr>");
					return false;		}		
			if(preg_match("^select",$sql)){return true; 		}
			else {
		  	    $count = 0;
				$data = array();
				while ( $row = mysqli_fetch_array($results))
				{	$data[$count] = $row;
					$count++;				}
				mysqli_free_result($results);
				return $data;
		 	}
		}	
		
	function extraqueries($sql="")	{	
			
			if(empty($sql)) { return false; }
			if(empty($this->CONN)) { return false; }
			$conn = $this->CONN;
			$results = mysqli_query($sql,$conn) or $this->error("Something wrong in query<hr>$sql<hr>");
			
			if(!$results){
			   $this->error("Query went bad ! <hr>$sql<hr>");
					return false;		}		
			else {
		  	    $count = 0;
				$data = array();
				while ( $row = mysqli_fetch_array($results))
				{	$data[$count] = $row;
					$count++;				}
				mysqli_free_result($results);
				return $data;
		 	}
		}	
	
	} 
?>