<?
	session_start();
	include("inc/fileInclude.php");
	include("inc/clsObj.php");

	$objBill->party=$_GET['cid'];
	$partyBil=$objBill->selectSumByParty();

	$objPurchase->party=$_GET['cid'];
	$partyPurchase=$objPurchase->selectSumByParty();
	
	$objTds->party=$_GET['cid'];
	$partyTds=$objTds->selectSumByParty();
	
	$partyAmount = $partyBil[0]['totamt']-($partyPurchase[0]['totamt']+$partyTds[0]['credit_amount']);

	//===================================================================
	$objCash->party=$_GET['cid'];
	$partyBal=$objCash->selectRecByParty();
	$total_debit = 0;
	$total_credit = 0;
	
	for($c=0;$c<count($partyBal);$c++)
	{
		if($partyBal[$c]['credit_amount']==0 && $partyBal[$c]['debit_amount']>0)
			$total_debit+=$partyBal[$c]['debit_amount'];
		elseif($partyBal[$c]['credit_amount']>0 && $partyBal[$c]['debit_amount']==0)
			$total_credit+=$partyBal[$c]['credit_amount'];
	}

	$balance_amount = $partyAmount-($total_credit-$total_debit);
	
	if($balance_amount < 0)
		echo "Credit Amount : ".-($balance_amount);
	elseif($balance_amount >= 0)
		echo "Debit Amount : ".$balance_amount;
/* Pending		
	echo "*****";
	$invoiceList=$objBill->selectByParty($_GET['cid']);
	//echo count($invoiceList);
	for($i=0;$i<count($invoiceList);$i++){
		$invoice_paid_amt = $objCash->getInvoicePaidAmt($invoiceList[$i]['id']);
		if($invoice_paid_amt < $invoiceList[$i]['amount']){
			echo "<div style='margin-bottom:5px;'><strong style='font-weight:bold!important; font-size:12px!important;'>".($i+1)." : ".$invoiceList[$i]['invoice_no']."</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"; ?>
			Invoice Amount : <span style="width:70px!important;"><?=$invoiceList[$i]['amount'];?></span>
			</div>
			Paid Amount : 
			<input type="text" name="paid_invoice_amount[]" id="paid_invoice_amount<?=$i;?>" value="<?=$invoice_paid_amt;?>" style="width:70px;  padding:5px;">
			Pay Amount : 
			<input type="text" name="pay_invoice_amount[]" id="pay_invoice_amount<?=$i;?>" value="" style="width:70px;  padding:5px;">
			<input type="hidden" name="invoice_id[]" id="invoice_id<?=$i;?>" value="<?=$invoiceList[$i]['id'];?>">
			<input type="hidden" name="invoice_no[]" id="invoice_no<?=$i;?>" value="<?=$invoiceList[$i]['invoice_no'];?>"><br/><br/>
			<?
		}
	} */
?>