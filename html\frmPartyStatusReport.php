<script src="js/jquery.table2excel.js"></script>
<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Reports</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Pending Payment Report</h5>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form name="frmsearch" id="frmsearch" action="" method="get">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-6">
                                	 <? $listParty=$objParty->selectStatus();?>
									<label>Search Party Name :</label>
									<select name="Client_Name" id="Client_Name" class="js-example-basic-single form-control col-sm-12">
                                    <option value="">--Select Party--</option>
                                    <? for($i=0;$i<count($listParty);$i++)
                                    { ?>
                                    <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                    <? } ?>
                                    </select>
								</div>
								<div class="form-group col-md-4">
									<label>&nbsp;</label><br>
									<button name="search" id="search" value="Search" class="btn btn-info btn-sm has-ripple">
										<i class="fas fa-search" aria-hidden="true"></i>
									</button>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a href="javascript:void(0)" onclick="javascript:window.open('print_party_details.php?client=<?=$_GET['Client_Name'];?>&From_Date=<?=$_GET['From_Date'];?>&To_Date=<?=$_GET['To_Date'];?>','_blank')" class="btn btn-info btn-sm has-ripple">Print</a><? } ?>
									<a href="javascript:void(0)" onclick="javascript:document.location='codePartyStatusReport.php'" class="btn btn-info btn-sm has-ripple">Reset</a>
                                    <? if($_SESSION['act_prn']==1){ ?>
									<a class="btn btn-info btn-sm has-ripple" id="download">Export to Excel</a><? } ?>
                                </div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
						<div class="dt-responsive">
							<table id="tblPartyStatusReport" class="table table-striped table-bordered nowrap table-responsive table2excel tabular" style="width: 100% !important;">
								<thead>
									<tr>
										<th>Sr. No.</th>
										<th>Party</th>
										<th>Contact Person</th>
										<th>Contact No.</th>
										<th>Contact Email</th>
										<th>Pending Amount</th>
									</tr>
								</thead>
								<tbody>
                                 <?  $n=0;
									$total_amt = 0;
									if(count($listRec)>0)
									 {
										$colorflg=0;
										for($e=0;$e<count($listRec);$e++)
										{
											$objBill->party=$listRec[$e]['id'];
											$bilDate=$objBill->selectSumByParty();
										
											$objPurchase->party=$listRec[$e]['id'];
											$purchaseDate=$objPurchase->selectSumByParty();
											
											$objCash->party=$listRec[$e]['id'];
											$cashDate=$objCash->selectSumByParty();
											
											$objTds->party=$listRec[$e]['id'];
											$partyTds=$objTds->selectSumByParty();
									
											$final_amount = $bilDate[0]['totamt'] - $purchaseDate[0]['totamt'] - ($cashDate[0]['credit_amount'] - $cashDate[0]['debit_amount'])-$partyTds[0]['credit_amount'];
										if($final_amount > 0)
										{ $n++;
											$total_amt+=$final_amount; ?>
									<tr>
										<td><?=$n;?></td>
										<td><? echo $listRec[$e]['party_name']; ?></td>
										<td><? echo $listRec[$e]['contact_name1']; ?></td>
                                        <td><? $contacts = str_replace(",","<br/>",$listRec[$e]['contact_no1']);
											   echo $contacts; ?>
                                        </td>
										<td><? $emails = str_replace(",","<br/>",$listRec[$e]['email1']);
											   echo $emails; ?></td>
										<td class="text-right"><?=($final_amount > 0) ? number_format($final_amount,2) : 0; ?></td>
									</tr>
									<? } 
									} 
									} ?>
								</tbody>
								<tfoot>
									<tr>
										<td colspan="5" class="text-right">
											<strong>Total Pending Amount</strong>
										</td>
										<td class="text-right"><strong><?=number_format($total_amt,2);?></strong></td>
									</tr>
								</tfoot>
							</table>
						</div>
						<!-- Table -->			
						<!--========== List View ==========-->
					
                    </div>
                </div>
            </div>
        </div>		
	</div>
</div>
<script>
var $j = jQuery.noConflict();
$j(document).ready(function () {
	$j('#download').click(function() {
		$j(".table2excel").table2excel({
			exclude: ".noExl",
			name: "Excel Document Name",
			filename: "Pending_Payment_Report",
			fileext: ".xls",
			exclude_img: true,
			exclude_links: false,
			exclude_inputs: true
		});
	});
});
</script>