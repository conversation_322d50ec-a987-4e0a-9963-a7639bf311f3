<?php
include("template.php");
function main()
{
	include("inc/clsObj.php");	
	$heading="<span>Manage</span> Currency ";
	$pageName="codeManageCurrency.php";	
	extract($_POST);	
	$objCrn->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	
	$objCrn->currency_name=$Currency_Name;
	$objCrn->currency_inr=$Currency_INR;
	$objCrn->currency_symbol=$Currency_Symbol;
	$objCrn->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objCrn->insert();
		redirect("codeManageCurrency.php");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objCrn->update();
		 redirect("codeManageCurrency.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objCrn->deleteSelect($chkAction);
					break;
			case 1:
					$objCrn->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objCrn->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageCurrency.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{		
		$objCrn->delete();
		redirect("codeManageCurrency.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objCrn->status();
		redirect("codeManageCurrency.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objCrn->selectRecById();						
	}		
	elseif($_GET['Currency_Name']!="")
	{
		$objCrn->id = $_GET['Currency_Name'];
		$listRec=$objCrn->selectRecById();
    }
	else
		$listRec=$objCrn->paging();
		
    include("html/frmManageCurrency.php");
 } 
?>