<?php
global $db;
class adminMaster
	{
	   /* Variable Declaration*/		
	   var $tablename='adminmaster';
	   var $id;
	   var $adminName;
	   var $adminUsername;
	   var $adminPassword;
	   var $adminEmail;
	   var $adminJoinDate;
	   var $adminRights;
	   var $adminLastLogin;
	   var $adminIp;
	   var $status;
       var $pagination;
	   var $limit;
	   var $start;
	    /*Constructor to intialize the Dabatabase*/
        function adminMaster()
			{
						$this->db = new dbclass();
			}
		/*Insert Record into Database*/		
		function insert() 
			{
            			$sql = "INSERT INTO `$this->tablename` (
                		    adminName,
                		    adminUsername,
                		    adminPassword,
                		    adminEmail,
                		    adminContact,
                		    company,
                		    adminJoinDate,
                		    adminRights,
                		    adminLastLogin,
                		    adminRole,
                		    adminIp,
                		    status
                		) VALUES (
                		    '$this->adminName',
                		    '$this->adminUsername',
                		    '$this->adminPassword',
                		    '$this->adminEmail',
                		    '$this->adminContact',
                		    '$this->company',
                		    now(),
                		    '$this->adminRights',
                		    '$this->adminLastLogin',
                		    '$this->adminRole',
                		    '$this->adminIp',
                		    '$this->status'
                		)";
							
					$id=$this->db->insert($sql);
					//$id=mysql_insert_id();
					return($id);
			} 
		/* Check the login  for the user*/	
		function loginCheck()
			{
				   $sql ="select * from `$this->tablename` 
				   		  where adminUsername='$this->adminUsername' and 
						        adminPassword='$this->adminPassword' and 
								status=1";//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}
		
		/* Check the login  for the user*/	
		/* function loginCheckCompany()
			{
				   $sql ="select * from `$this->tablename` 
				   		  where adminUsername='$this->adminUsername' and 
						        adminPassword='$this->adminPassword' and 
								(company='$this->company' or company='All') and 
								status=1";//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			} */
			function loginCheckCompany()
			{
				   $sql ="select * from `$this->tablename` 
				   		  where adminEmail='$this->adminEmail' and 
						        adminPassword='$this->adminPassword' and 
								(company='$this->company' or company='All') and 
								status=1";
								//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}
		
		/* Check the login  for the user*/	
		function loginCheckCompanyById()
			{
				   $sql ="select * from `$this->tablename` 
				   		  	where id='$this->id' and 
							(company='$this->company' or company='All') and 
							status=1";//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}

		/* Login updates last login time*/	
		function loginUpdate()
			{
				$sql = "update `$this->tablename` set
	  							`adminLastLogin`= now()										
						    	 where `id`=".$_SESSION['memberid'];
								 //echo $sql;die();				  
						$this->db->edit($sql);		
						return true;
			}	

		/* Login updates last login time*/	
		function updateRights()
		{
			$sql = "update `$this->tablename` set
							`adminRights`= '$this->adminRights'
							 where `id`='$this->id'";
							 // echo $sql;die();				  
					$this->db->edit($sql);		
					return true;
		}	

		/* Fetch all the records */	
		function select()
		{
			$sql ="select * from `$this->tablename` order by adminName";
			//echo $sql;die();
			   $result=$this->db->select($sql);
			   return($result);
		}	
		/*Fectch record by id from Database*/		
		function selectRecById()
			{
				$sql ="select * from `$this->tablename` 
					   where id='$this->id'";	
				   $result=$this->db->select($sql);
	 		       return($result);
			}

		/*Fectch record by id from Database*/		
		function selectRecByRole($role)
			{
				$sql ="select * from `$this->tablename` 
					   where adminRole='$role'";
					  // echo $sql; exit;	
				   $result=$this->db->select($sql);
	 		       return($result);
			}
		
		function getnameById($id)
			{
				$sql ="select * from `$this->tablename` 
					   where id='$id'";
				   $result=$this->db->select($sql);
	 		       return($result[0]['adminName']);
			}	


		/*Fectch record by id from Database*/		
		function getEmailById($id)
			{
				$sql ="select * from `$this->tablename` 
					   where id='$id'";
				   $result=$this->db->select($sql);
	 		       return($result[0]['adminEmail']);
			}
		
		function getContactById($id)
			{
				$sql ="select * from `$this->tablename` 
					   where id='$id'";
				   $result=$this->db->select($sql);
	 		       return($result[0]['adminContact']);
			}
		
		/*Fectch record by id from Database*/		
		function setRights($uid)
			{
				$sql ="select *  from `$this->tablename` where id='$uid'";
			   	$result=$this->db->select($sql);
				$_SESSION['uper']=explode(",",trim($result[0]['adminRights']));
			}
		
		/* update the record in database*/		
		function update()
			{
				$sql = "update `$this->tablename` set
	  							`adminName`='$this->adminName',
								`adminUsername`='$this->adminUsername',
								`adminPassword`='$this->adminPassword',
								`adminEmail`='$this->adminEmail',
								`adminContact`='$this->adminContact',
								`adminRights`='$this->adminRights',
								`company`='$this->company',
								`adminRole`='$this->adminRole'										
						    	 where `id`=$this->id";//echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}
		/*delete a record from database*/	
		function delete()
			{
				$sql="delete from `$this->tablename` 
					  where `id`=$this->id";//echo $sql;die();
				$this->db->sql_query($sql);
			}
		/*update password*/	
		function adminUpdatePassword()
			{
				$sql = "update `$this->tablename` set
	  						   `adminPassword`='$this->adminPassword'
							    where `id`=$this->id";
								//echo "<br/><br/><br/><br/><br/>".$sql;
						$this->db->edit($sql);		
						return true;
			}
		/*update single status*/	
		function status()
			{
				$sql = "update `$this->tablename` set
	  						   `status`='$this->status'
							    where `id`=$this->id";//echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}
		/*update selected status publish*/	
		function statusUpdatePublish($chk)
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql = "update `$this->tablename` set
										   `status`=1
											where `id`='$id'";//echo $sql;die();	
									$this->db->edit($sql);		
	  				    }
			    return true;
			}	
		/*update selected status unpublish*/	
		function statusUpdateUnPublish($chk)
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql = "update `$this->tablename` set
										   `status`=0
											where `id`='$id'";//echo $sql;die();	
									$this->db->edit($sql);		
	  				    }
			    return true;
			}	
		/*delete the selected record*/	
		function deleteSelect($chk) 
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql="delete from `$this->tablename` where `id` = '$id'";
	     					//$res = mysql_query($sql);
							$this->db->sql_query($sql);
						}
				return true;
			}	
		function forgotPassoword()
		   {
				$sql="select * from `$this->tablename` where adminEmail='$this->adminEmail'";
				$result=$this->db->select($sql);
				return($result);
		   }							
		/*...paging...*/
		function paging()
			{
				$pages = new Paging();
				$pages->sql ="select * from `$this->tablename`";
				$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
				$pages->limit = 500;
				$pages->GeneratePaging();
                $this->pagination=$pages->pagination; 
				$result=$this->db->select($pages->sql);
	 		    return($result);
			}   
}		
?>   