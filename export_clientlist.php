<?php
	header( "Content-Type: application/vnd.ms-excel" );
	header( "Content-disposition: attachment; filename=client_list.xls" );
	include("inc/fileInclude.php");
	include("config.php");
	include("inc/clsObj.php");
	require_once 'excel/reader.php';
	$table = 'party_master';
	$object=$objParty;

	$listRec = $object->select();
	
	echo "Sr. No. \t";
	echo "Party Name \t";
	//echo "Address Details \t";
	echo "City/Zopcode \t";
	echo "State \t";
	echo "Country \t";
	echo "Office Number \t";
	echo "Email \t";
	echo "Website \t";
	echo "Contact Person \t";
	echo "Contact Number \t";
	echo "Contact Email \n";
	
	for($e=0;$e<count($listRec);$e++){
		echo $e+1 ." \t";
		echo $listRec[$e]['party_name']." \t";
		//echo $listRec[$e]['party_address']." \t";
		echo $listRec[$e]['city']."/".$listRec[$e]['zipcode']." \t";
		echo $objState->getNameById($listRec[$e]['state'])." \t";
		echo $listRec[$e]['country']." \t";
		echo $listRec[$e]['office_no1']." \t";
		echo $listRec[$e]['email']." \t";
		echo $listRec[$e]['domain_name']." \t";
		echo $listRec[$e]['contact_name1']." \t";
		echo $listRec[$e]['contact_no1']." \t";
		echo $listRec[$e]['email1']." \n";
	}
?>