var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; };

/* src/PNotifyCallbacks.html generated by Svelte v2.16.1 */
(function (global, factory) {
	(typeof exports === "undefined" ? "undefined" : _typeof(exports)) === "object" && typeof module !== "undefined" ? module.exports = factory(require('./PNotify')) : typeof define === "function" && define.amd ? define('PNotifyCallbacks', ["./PNotify"], factory) : global.PNotifyCallbacks = factory(PNotify);
})(this, function (PNotify) {
	"use strict";

	PNotify = PNotify && PNotify.__esModule ? PNotify["default"] : PNotify;

	var _open = PNotify.prototype.open;
	var _close = PNotify.prototype.close;

	var callbacks = function callbacks(notice, options, name) {
		var modules = notice ? notice.get().modules : options.modules;
		var cbs = modules && modules.Callbacks ? modules.Callbacks : {};
		return cbs[name] ? cbs[name] : function () {
			return true;
		};
	};

	PNotify.prototype.open = function () {
		var ret = callbacks(this, null, 'beforeOpen')(this);
		if (ret !== false) {
			for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {
				args[_key] = arguments[_key];
			}

			_open.apply(this, args);
			callbacks(this, null, 'afterOpen')(this);
		}
	};

	PNotify.prototype.close = function (timerHide) {
		var ret = callbacks(this, null, 'beforeClose')(this, timerHide);
		if (ret !== false) {
			for (var _len2 = arguments.length, args = Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
				args[_key2 - 1] = arguments[_key2];
			}

			_close.apply(this, [timerHide].concat(args));
			callbacks(this, null, 'afterClose')(this, timerHide);
		}
	};

	function setup(Component) {
		Component.key = 'Callbacks';

		Component.getCallbacks = callbacks;

		var _alert = PNotify.alert;
		var _notice = PNotify.notice;
		var _info = PNotify.info;
		var _success = PNotify.success;
		var _error = PNotify.error;

		var init = function init(original, options) {
			callbacks(null, options, 'beforeInit')(options);
			var notice = original(options);
			callbacks(notice, null, 'afterInit')(notice);
			return notice;
		};

		PNotify.alert = function (options) {
			return init(_alert, options);
		};
		PNotify.notice = function (options) {
			return init(_notice, options);
		};
		PNotify.info = function (options) {
			return init(_info, options);
		};
		PNotify.success = function (options) {
			return init(_success, options);
		};
		PNotify.error = function (options) {
			return init(_error, options);
		};

		// Register the module with PNotify.
		PNotify.modules.Callbacks = Component;
	};

	function create_main_fragment(component, ctx) {

		return {
			c: noop,

			m: noop,

			p: noop,

			d: noop
		};
	}

	function PNotifyCallbacks(options) {
		init(this, options);
		this._state = assign({}, options.data);
		this._intro = true;

		this._fragment = create_main_fragment(this, this._state);

		if (options.target) {
			this._fragment.c();
			this._mount(options.target, options.anchor);
		}
	}

	assign(PNotifyCallbacks.prototype, {
		destroy: destroy,
		get: get,
		fire: fire,
		on: on,
		set: set,
		_set: _set,
		_stage: _stage,
		_mount: _mount,
		_differs: _differs
	});

	PNotifyCallbacks.prototype._recompute = noop;

	setup(PNotifyCallbacks);

	function noop() {}

	function init(component, options) {
		component._handlers = blankObject();
		component._slots = blankObject();
		component._bind = options._bind;
		component._staged = {};

		component.options = options;
		component.root = options.root || component;
		component.store = options.store || component.root.store;

		if (!options.root) {
			component._beforecreate = [];
			component._oncreate = [];
			component._aftercreate = [];
		}
	}

	function assign(tar, src) {
		for (var k in src) {
			tar[k] = src[k];
		}return tar;
	}

	function destroy(detach) {
		this.destroy = noop;
		this.fire('destroy');
		this.set = noop;

		this._fragment.d(detach !== false);
		this._fragment = null;
		this._state = {};
	}

	function get() {
		return this._state;
	}

	function fire(eventName, data) {
		var handlers = eventName in this._handlers && this._handlers[eventName].slice();
		if (!handlers) return;

		for (var i = 0; i < handlers.length; i += 1) {
			var handler = handlers[i];

			if (!handler.__calling) {
				try {
					handler.__calling = true;
					handler.call(this, data);
				} finally {
					handler.__calling = false;
				}
			}
		}
	}

	function on(eventName, handler) {
		var handlers = this._handlers[eventName] || (this._handlers[eventName] = []);
		handlers.push(handler);

		return {
			cancel: function cancel() {
				var index = handlers.indexOf(handler);
				if (~index) handlers.splice(index, 1);
			}
		};
	}

	function set(newState) {
		this._set(assign({}, newState));
		if (this.root._lock) return;
		flush(this.root);
	}

	function _set(newState) {
		var oldState = this._state,
		    changed = {},
		    dirty = false;

		newState = assign(this._staged, newState);
		this._staged = {};

		for (var key in newState) {
			if (this._differs(newState[key], oldState[key])) changed[key] = dirty = true;
		}
		if (!dirty) return;

		this._state = assign(assign({}, oldState), newState);
		this._recompute(changed, this._state);
		if (this._bind) this._bind(changed, this._state);

		if (this._fragment) {
			this.fire("state", { changed: changed, current: this._state, previous: oldState });
			this._fragment.p(changed, this._state);
			this.fire("update", { changed: changed, current: this._state, previous: oldState });
		}
	}

	function _stage(newState) {
		assign(this._staged, newState);
	}

	function _mount(target, anchor) {
		this._fragment[this._fragment.i ? 'i' : 'm'](target, anchor || null);
	}

	function _differs(a, b) {
		return a != a ? b == b : a !== b || a && (typeof a === "undefined" ? "undefined" : _typeof(a)) === 'object' || typeof a === 'function';
	}

	function blankObject() {
		return Object.create(null);
	}

	function flush(component) {
		component._lock = true;
		callAll(component._beforecreate);
		callAll(component._oncreate);
		callAll(component._aftercreate);
		component._lock = false;
	}

	function callAll(fns) {
		while (fns && fns.length) {
			fns.shift()();
		}
	}

	return PNotifyCallbacks;
});
//# sourceMappingURL=PNotifyCallbacks.js.map