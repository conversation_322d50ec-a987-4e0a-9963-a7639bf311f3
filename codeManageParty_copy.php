<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Client ";
	$pageName="codeManageParty.php";
	
    include("inc/clsObj.php");	
	extract($_POST);	

	$objParty->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objParty->customer_name=$Customer_Name;
	
	$Party_Address=str_replace("\&quot;","", $Party_Address);		
		$Party_Address=str_replace('\\', '', $Party_Address);				
		$Party_Address=str_replace("\'","'", $Party_Address);		
		$Party_Address=str_replace("'","\'", $Party_Address);	

		$objParty->address=''; // $Party_Address;
		$objParty->contact_number=$Contact_Number;
		$objParty->alternate_number=$Alternative_Number;
		$objParty->email=''; // $Email_Address;
		$objParty->area=$Area;
		$objParty->city=$City;
		$objParty->gold_weight=$Gold_Weight;
		$objParty->loan_amount=$Loan_Amount;
		$objParty->bank_name=$Bank_Name;
		$objParty->source=$Source;
		$objParty->other_source=$Other_Source;
		
// 		$objParty->gross_weight=$Gross_Weight;
// 		$objParty->net_weight=$Net_Weight;
// 		$objParty->bank_outstanding_amt=$Bank_Outstanding_Amt;
// 		$objParty->difference_value=$Difference_Value;
// 		$objParty->total_value=$Total_Value;

        $objParty->gross_weight = (is_numeric($Gross_Weight) && $Gross_Weight !== '') ? floatval($Gross_Weight) : 0;
        $objParty->net_weight = (is_numeric($Net_Weight) && $Net_Weight !== '') ? floatval($Net_Weight) : 0;
        $objParty->bank_outstanding_amt = (is_numeric($Bank_Outstanding_Amt) && $Bank_Outstanding_Amt !== '') ? floatval($Bank_Outstanding_Amt) : 0;
        $objParty->difference_value = (is_numeric($Difference_Value) && $Difference_Value !== '') ? floatval($Difference_Value) : 0;
        $objParty->total_value = (is_numeric($Total_Value) && $Total_Value !== '') ? floatval($Total_Value) : 0;
		
		$objParty->sales_person=$Sales_Person;
		$objParty->tele_caller =$_SESSION['memberid'];
		$objParty->additional_notes=$Additional_Notes;
		$objParty->star_ranking=$Star_Ranking;
		$objParty->inquiry_status=$Inquiry_Status;
		$objParty->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
		for ($i = 1; $i <= 4; $i++) {
	    $imageField = 'Image' . $i;
	    $hiddenField = 'Image' . $i . 'hidden';

	    if (!empty($_FILES[$imageField]['name'])) {
	        $objParty->{'image' . $i} = uploadImage($imageField, $rnd . $_FILES[$imageField]['name'], IMAGE_PATH, "", "");
	    } else {
	        if (!empty($$hiddenField)) {
	            $objParty->{'image' . $i} = $$hiddenField;
	        } else {
	            $objParty->{'image' . $i} = NULL;
	        }
	    }
	}
	
	if(isset($_POST['btnAdd'])){
		$objParty->insert();
		// =============== Send SMS to the customer ==================
		$obj_admin->id=$Sales_Person;
        $salesPerDet = $obj_admin->selectRecById();
        $sales_contact_no = "91" . $salesPerDet[0]['adminContact'];
        $sales_person = $salesPerDet[0]['adminName'];
        // echo "Sales person contact : " . $sales_contact_no;
        
        $obj_admin->id=$_SESSION['memberid'];
        $teleCallerDet = $obj_admin->selectRecById();
        
        $telecaller_contact_no = "91" . $teleCallerDet[0]['adminContact'];
        // echo "Telecaller No. : " . $telecaller_contact_no . "<br/>";
        $telecaller_name = $teleCallerDet[0]['adminName'];
        // echo "Telecaller Name : " . $telecaller_name;
		
		$contact_no = "91" . $Contact_Number;

		$curl = curl_init();
		curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS =>'{
			"to": "' . $contact_no . '",
			"recipient_type": "individual",
			"type": "template",
			"template": {
				"language": {
					"policy": "deterministic",
					"code": "en"
				},
				"name": "appreciate",
				"components": [
					{
						"type": "body",
						"parameters": [
							{
								"type": "text",
								"text": "' . $sales_person . '"
							},
							{
								"type": "text",
								"text": "' . $sales_contact_no . '"
							}
						]
					}
				]
			}
		}',
		CURLOPT_HTTPHEADER => array(
			'API-KEY: 64d0e43cfff6e0d950e3be05',
			'Content-Type: application/json',
			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
		),
		));

		$response = curl_exec($curl);
		curl_close($curl);
		
        // echo $response;
        // echo "<hr/>";
		// ===== End - Send SMS to the customer ===========

		// =============== Send SMS to the Sales Executive ==================

        // $salesContact = $obj_admin->getContactById($Sales_Person);
		// $sales_contact_no = "91" . $salesContact;

		$curl = curl_init();
		curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS =>'{
			"to": "' . $sales_contact_no . '",
			"recipient_type": "individual",
			"type": "template",
			 "template": {
                    "language": {
                        "policy": "deterministic",
                        "code": "en"
                    },
                    "name": "customer_detail",
                    "components": [
                        {
                            "type": "body",
                            "parameters": [
                                {
                                    "type": "text",
                                    "text":  "' . $Customer_Name . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Contact_Number . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Alternative_Number . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Area . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $City . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Gold_Weight . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Loan_Amount . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Bank_Name . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Additional_Notes . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $Source . '"
                                },
                                {
                                    "type": "text",
                                    "text":  "' . $telecaller_name . '"
                                }
                            ]
                        }
                    ]
                }
		}',
		CURLOPT_HTTPHEADER => array(
			'API-KEY: 64d0e43cfff6e0d950e3be05',
			'Content-Type: application/json',
			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
		),
		));
        // "text": "Name : ' . $Customer_Name . ', Contact No. : ' . $Contact_Number . ', Email : ' . $Email_Address . '"
		$response = curl_exec($curl);
 		curl_close($curl);
        // echo $response; exit;
		// ===== End - Send SMS to the Sales Person ===========

		redirect("codeManageParty.php?msg=add");			
	}

	if(isset($_POST['btnUpdate'])){

		if($_SESSION['memberrole']=="Tele Caller"){
			$objParty->updateByTelecaller();
		}elseif($_SESSION['memberrole']=="Sales Person"){
			$objParty->updateBySalesperson();
			
    		if($Inquiry_Status == "Close"){
    		    // =============== Send SMS to the customer ==================
        		$contact_no = "91" . $Contact_Number;
        
        		$curl = curl_init();
        		curl_setopt_array($curl, array(
        		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
        		CURLOPT_RETURNTRANSFER => true,
        		CURLOPT_ENCODING => '',
        		CURLOPT_MAXREDIRS => 10,
        		CURLOPT_TIMEOUT => 0,
        		CURLOPT_FOLLOWLOCATION => true,
        		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        		CURLOPT_CUSTOMREQUEST => 'POST',
        		CURLOPT_POSTFIELDS =>'{
        			"to": "' . $contact_no . '",
        			"recipient_type": "individual",
        			"type": "template",
        			"template": {
        				"language": {
        					"policy": "deterministic",
        					"code": "en"
        				},
        				"name": "thank_you_message",
        				"components": []
        			}
        		}',
        		CURLOPT_HTTPHEADER => array(
        			'API-KEY: 64d0e43cfff6e0d950e3be05',
        			'Content-Type: application/json',
        			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
        		),
        		));
        
        		$response = curl_exec($curl);
        		echo $response;
        		curl_close($curl);
        		// =============== End - Send SMS to the customer ===========
        
        		// =============== Send SMS to Perticular Number==================
                $obj_admin->id=$Sales_Person;
                $salesPerDet = $obj_admin->selectRecById();
                $sales_contact_no = $salesPerDet[0]['adminContact'];
                $sales_person = $salesPerDet[0]['adminName'];
                
                $to_contact_no = "919737622522";
                // $to_contact_no = "919879537100";
                
                // $salesContact = $obj_admin->getContactById($Sales_Person);
        		// $sales_contact_no = "91" . $salesContact;
        
        		$curl = curl_init();
        		curl_setopt_array($curl, array(
        		CURLOPT_URL => 'https://crm.officialwa.com/api/meta/v19.0/657259720805792/messages',
        		CURLOPT_RETURNTRANSFER => true,
        		CURLOPT_ENCODING => '',
        		CURLOPT_MAXREDIRS => 10,
        		CURLOPT_TIMEOUT => 0,
        		CURLOPT_FOLLOWLOCATION => true,
        		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        		CURLOPT_CUSTOMREQUEST => 'POST',
        		CURLOPT_POSTFIELDS =>'{
        			"to": "' . $to_contact_no . '",
        			"recipient_type": "individual",
        			"type": "template",
        			 "template": {
                        "language": {
                            "policy": "deterministic",
                            "code": "en"
                        },
                        "name": "21_june_contact_details",
                        "components": [
                            {
                                "type": "body",
                                "parameters": [
                                    {
                                        "type": "text",
                                        "text": "' . $Customer_Name . '"
                                    },
                                    {
                                        "type": "text",
                                        "text": "' . $Contact_Number . '"
                                    }
                                ]
                            }
                        ]
                    }
        		}',
        		CURLOPT_HTTPHEADER => array(
        			'API-KEY: 64d0e43cfff6e0d950e3be05',
        			'Content-Type: application/json',
        			'Authorization: Bearer pYYyZ0LOzTUamDvm3vkH5wVkLF4PXVU5ERVJTQ09SRQFRrcpy1OCY7RLnLvePYnF9Xjgox1NgtVU5ERVJTQ09SRQcREFTSAvoUVPUflGYANOdGMa3WOyUiVU5ERVJTQ09SRQd8XXXKESTUqG9V9GXxqmmDhloYIrT4NqGVQ'
        		),
        		));
                // "text": "Name : ' . $Customer_Name . ', Contact No. : ' . $Contact_Number . ', Email : ' . $Email_Address . '"
        		$response = curl_exec($curl);
        		curl_close($curl);
        		// echo $response; exit;
        		// ===== End - Send SMS to the Sales Person ===========
    		}
		}
	    elseif($_SESSION['memberrole']=="Admin"){
	        $objParty->updateBySalesperson();
	    }else{
			$objParty->update();
		}
		
		redirect("codeManageParty.php?msg=edit");
	}

	if(isset($_POST['btnAction'])){
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objParty->deleteSelect($chkAction);
					break;
			case 1:
					$objParty->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objParty->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageParty.php?msg=edit");
	}	

	if(isset($_GET['delete'])){			 
		$objParty->delete();
		redirect("codeManageParty.php?msg=del");
	}	

	if(isset($_GET['status'])){			 
		$objParty->status();
		redirect("codeManageParty.php?msg=status");		
	}

	if(isset($_GET['id'])){			
		$listEdit=$objParty->selectRecById();						
	}	
	
	if(isset($_GET['pid'])){	
		$objParty->id=$_GET['pid'];
		$listEdit=$objParty->selectRecById();						
	}
	
	// if($_GET['Client_Name']!="" || $_GET['Area_Name']){
	// 	if($_GET['Client_Name']!="")
	// 		$query.=" and id='".$_GET['Client_Name']."'";
	// 	if($_GET['State_Name']!="")
	// 		$query.=" and state='".$_GET['State_Name']."'";
				
	// 	$listRec=$objParty->selectRecBySearch($query);
	// }
	// else
	//echo "<pre>"; print_r($_SESSION); echo "</pre>"; exit;
	$from_date = '';
	$to_date = '';
	if(!empty($_GET['from_date']) && !empty($_GET['to_date'])) {
        $from_date = $_GET['from_date'];
        $to_date = $_GET['to_date'];
        // $where .= " AND DATE(cdt) BETWEEN '$from_date' AND '$to_date'";
    }

    // $sql = "SELECT * FROM party_master $where ORDER BY id DESC";
    // $listRec = $db->select($sql);
	
	if($_SESSION['memberrole']=="Sales Person"){
		$listRec=$objParty->selectBySalesperson($_SESSION['memberid'], $from_date, $to_date);
	}else{
		$listRec=$objParty->selectByQuery($from_date, $to_date);
	}

	include("html/frmManageParty.php");
} 
?>