/*
 Highcharts JS v7.2.1 (2019-10-31)

 3D features for Highcharts JS

 License: www.highcharts.com/license
*/
(function(r){"object"===typeof module&&module.exports?(r["default"]=r,module.exports=r):"function"===typeof define&&define.amd?define("highcharts/highcharts-3d",["highcharts"],function(B){r(B);r.Highcharts=B;return r}):r("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(r){function B(b,m,z,t){b.hasOwnProperty(m)||(b[m]=t.apply(null,z))}r=r?r._modules:{};B(r,"parts-3d/Math.js",[r["parts/Globals.js"],r["parts/Utilities.js"]],function(b,m){var z=m.pick,t=b.deg2rad;b.perspective3D=function(b,
g,n){g=0<n&&n<Number.POSITIVE_INFINITY?n/(b.z+g.z+n):1;return{x:b.x*g,y:b.y*g}};b.perspective=function(m,g,n){var y=g.options.chart.options3d,u=n?g.inverted:!1,x={x:g.plotWidth/2,y:g.plotHeight/2,z:y.depth/2,vd:z(y.depth,1)*z(y.viewDistance,0)},w=g.scale3d||1,h=t*y.beta*(u?-1:1);y=t*y.alpha*(u?-1:1);var a=Math.cos(y),d=Math.cos(-h),k=Math.sin(y),q=Math.sin(-h);n||(x.x+=g.plotLeft,x.y+=g.plotTop);return m.map(function(c){var e=(u?c.y:c.x)-x.x;var l=(u?c.x:c.y)-x.y;c=(c.z||0)-x.z;e={x:d*e-q*c,y:-k*
q*e+a*l-d*k*c,z:a*q*e+k*l+a*d*c};l=b.perspective3D(e,x,x.vd);l.x=l.x*w+x.x;l.y=l.y*w+x.y;l.z=e.z*w+x.z;return{x:u?l.y:l.x,y:u?l.x:l.y,z:l.z}})};b.pointCameraDistance=function(b,g){var n=g.options.chart.options3d,m=g.plotWidth/2;g=g.plotHeight/2;n=z(n.depth,1)*z(n.viewDistance,0)+n.depth;return Math.sqrt(Math.pow(m-b.plotX,2)+Math.pow(g-b.plotY,2)+Math.pow(n-b.plotZ,2))};b.shapeArea=function(b){var g=0,n;for(n=0;n<b.length;n++){var m=(n+1)%b.length;g+=b[n].x*b[m].y-b[m].x*b[n].y}return g/2};b.shapeArea3d=
function(m,g,n){return b.shapeArea(b.perspective(m,g,n))}});B(r,"parts-3d/SVGRenderer.js",[r["parts/Globals.js"],r["parts/Utilities.js"]],function(b,m){function z(a,c,f,F,C,d,b,l){var p=[],G=d-C;return d>C&&d-C>Math.PI/2+.0001?(p=p.concat(z(a,c,f,F,C,C+Math.PI/2,b,l)),p=p.concat(z(a,c,f,F,C+Math.PI/2,d,b,l))):d<C&&C-d>Math.PI/2+.0001?(p=p.concat(z(a,c,f,F,C,C-Math.PI/2,b,l)),p=p.concat(z(a,c,f,F,C-Math.PI/2,d,b,l))):["C",a+f*Math.cos(C)-f*e*G*Math.sin(C)+b,c+F*Math.sin(C)+F*e*G*Math.cos(C)+l,a+f*
Math.cos(d)+f*e*G*Math.sin(d)+b,c+F*Math.sin(d)-F*e*G*Math.cos(d)+l,a+f*Math.cos(d)+b,c+F*Math.sin(d)+l]}var t=m.defined,x=m.extend,g=m.objectEach,n=m.pick,y=Math.cos,u=Math.PI,r=Math.sin,w=b.animObject,h=b.charts,a=b.color,d=b.deg2rad,k=b.merge,q=b.perspective,c=b.SVGElement;m=b.SVGRenderer;var e=4*(Math.sqrt(2)-1)/3/(u/2);m.prototype.toLinePath=function(a,c){var f=[];a.forEach(function(a){f.push("L",a.x,a.y)});a.length&&(f[0]="M",c&&f.push("Z"));return f};m.prototype.toLineSegments=function(a){var c=
[],f=!0;a.forEach(function(a){c.push(f?"M":"L",a.x,a.y);f=!f});return c};m.prototype.face3d=function(a){var e=this,f=this.createElement("path");f.vertexes=[];f.insidePlotArea=!1;f.enabled=!0;f.attr=function(a){if("object"===typeof a&&(t(a.enabled)||t(a.vertexes)||t(a.insidePlotArea))){this.enabled=n(a.enabled,this.enabled);this.vertexes=n(a.vertexes,this.vertexes);this.insidePlotArea=n(a.insidePlotArea,this.insidePlotArea);delete a.enabled;delete a.vertexes;delete a.insidePlotArea;var f=q(this.vertexes,
h[e.chartIndex],this.insidePlotArea),p=e.toLinePath(f,!0);f=b.shapeArea(f);f=this.enabled&&0<f?"visible":"hidden";a.d=p;a.visibility=f}return c.prototype.attr.apply(this,arguments)};f.animate=function(a){if("object"===typeof a&&(t(a.enabled)||t(a.vertexes)||t(a.insidePlotArea))){this.enabled=n(a.enabled,this.enabled);this.vertexes=n(a.vertexes,this.vertexes);this.insidePlotArea=n(a.insidePlotArea,this.insidePlotArea);delete a.enabled;delete a.vertexes;delete a.insidePlotArea;var f=q(this.vertexes,
h[e.chartIndex],this.insidePlotArea),p=e.toLinePath(f,!0);f=b.shapeArea(f);f=this.enabled&&0<f?"visible":"hidden";a.d=p;this.attr("visibility",f)}return c.prototype.animate.apply(this,arguments)};return f.attr(a)};m.prototype.polyhedron=function(a){var e=this,f=this.g(),p=f.destroy;this.styledMode||f.attr({"stroke-linejoin":"round"});f.faces=[];f.destroy=function(){for(var a=0;a<f.faces.length;a++)f.faces[a].destroy();return p.call(this)};f.attr=function(a,p,d,b){if("object"===typeof a&&t(a.faces)){for(;f.faces.length>
a.faces.length;)f.faces.pop().destroy();for(;f.faces.length<a.faces.length;)f.faces.push(e.face3d().add(f));for(var l=0;l<a.faces.length;l++)e.styledMode&&delete a.faces[l].fill,f.faces[l].attr(a.faces[l],null,d,b);delete a.faces}return c.prototype.attr.apply(this,arguments)};f.animate=function(a,p,d){if(a&&a.faces){for(;f.faces.length>a.faces.length;)f.faces.pop().destroy();for(;f.faces.length<a.faces.length;)f.faces.push(e.face3d().add(f));for(var b=0;b<a.faces.length;b++)f.faces[b].animate(a.faces[b],
p,d);delete a.faces}return c.prototype.animate.apply(this,arguments)};return f.attr(a)};var l={initArgs:function(a){var c=this,f=c.renderer,e=f[c.pathType+"Path"](a),p=e.zIndexes;c.parts.forEach(function(a){c[a]=f.path(e[a]).attr({"class":"highcharts-3d-"+a,zIndex:p[a]||0}).add(c)});c.attr({"stroke-linejoin":"round",zIndex:p.group});c.originalDestroy=c.destroy;c.destroy=c.destroyParts},singleSetterForParts:function(a,c,f,e,d,b){var p={};e=[null,null,e||"attr",d,b];var l=f&&f.zIndexes;f?(g(f,function(c,
e){p[e]={};p[e][a]=c;l&&(p[e].zIndex=f.zIndexes[e]||0)}),e[1]=p):(p[a]=c,e[0]=p);return this.processParts.apply(this,e)},processParts:function(a,c,f,e,d){var p=this;p.parts.forEach(function(b){c&&(a=n(c[b],!1));if(!1!==a)p[b][f](a,e,d)});return p},destroyParts:function(){this.processParts(null,null,"destroy");return this.originalDestroy()}};var v=b.merge(l,{parts:["front","top","side"],pathType:"cuboid",attr:function(a,e,f,d){if("string"===typeof a&&"undefined"!==typeof e){var p=a;a={};a[p]=e}return a.shapeArgs||
t(a.x)?this.singleSetterForParts("d",null,this.renderer[this.pathType+"Path"](a.shapeArgs||a)):c.prototype.attr.call(this,a,void 0,f,d)},animate:function(a,e,f){t(a.x)&&t(a.y)?(a=this.renderer[this.pathType+"Path"](a),this.singleSetterForParts("d",null,a,"animate",e,f),this.attr({zIndex:a.zIndexes.group})):c.prototype.animate.call(this,a,e,f);return this},fillSetter:function(c){this.singleSetterForParts("fill",null,{front:c,top:a(c).brighten(.1).get(),side:a(c).brighten(-.1).get()});this.color=this.fill=
c;return this}});m.prototype.elements3d={base:l,cuboid:v};m.prototype.element3d=function(a,c){var f=this.g();x(f,this.elements3d[a]);f.initArgs(c);return f};m.prototype.cuboid=function(a){return this.element3d("cuboid",a)};b.SVGRenderer.prototype.cuboidPath=function(a){function c(a){return n[a]}var f=a.x,e=a.y,d=a.z,l=a.height,p=a.width,k=a.depth,v=h[this.chartIndex],w=v.options.chart.options3d.alpha,g=0,n=[{x:f,y:e,z:d},{x:f+p,y:e,z:d},{x:f+p,y:e+l,z:d},{x:f,y:e+l,z:d},{x:f,y:e+l,z:d+k},{x:f+p,y:e+
l,z:d+k},{x:f+p,y:e,z:d+k},{x:f,y:e,z:d+k}];n=q(n,v,a.insidePlotArea);var u=function(a,f){var e=[[],-1];a=a.map(c);f=f.map(c);0>b.shapeArea(a)?e=[a,0]:0>b.shapeArea(f)&&(e=[f,1]);return e};var m=u([3,2,1,0],[7,6,5,4]);a=m[0];p=m[1];m=u([1,6,7,0],[4,5,2,3]);l=m[0];k=m[1];m=u([1,2,5,6],[0,7,4,3]);u=m[0];m=m[1];1===m?g+=1E4*(1E3-f):m||(g+=1E4*f);g+=10*(!k||0<=w&&180>=w||360>w&&357.5<w?v.plotHeight-e:10+e);1===p?g+=100*d:p||(g+=100*(1E3-d));return{front:this.toLinePath(a,!0),top:this.toLinePath(l,!0),
side:this.toLinePath(u,!0),zIndexes:{group:Math.round(g)},isFront:p,isTop:k}};b.SVGRenderer.prototype.arc3d=function(e){function l(a){var f=!1,e={},c;a=k(a);for(c in a)-1!==v.indexOf(c)&&(e[c]=a[c],delete a[c],f=!0);return f?e:!1}var f=this.g(),p=f.renderer,v="x y r innerR start end".split(" ");e=k(e);e.alpha=(e.alpha||0)*d;e.beta=(e.beta||0)*d;f.top=p.path();f.side1=p.path();f.side2=p.path();f.inn=p.path();f.out=p.path();f.onAdd=function(){var a=f.parentGroup,e=f.attr("class");f.top.add(f);["out",
"inn","side1","side2"].forEach(function(c){f[c].attr({"class":e+" highcharts-3d-side"}).add(a)})};["addClass","removeClass"].forEach(function(a){f[a]=function(){var e=arguments;["top","out","inn","side1","side2"].forEach(function(c){f[c][a].apply(f[c],e)})}});f.setPaths=function(a){var e=f.renderer.arc3dPath(a),c=100*e.zTop;f.attribs=a;f.top.attr({d:e.top,zIndex:e.zTop});f.inn.attr({d:e.inn,zIndex:e.zInn});f.out.attr({d:e.out,zIndex:e.zOut});f.side1.attr({d:e.side1,zIndex:e.zSide1});f.side2.attr({d:e.side2,
zIndex:e.zSide2});f.zIndex=c;f.attr({zIndex:c});a.center&&(f.top.setRadialReference(a.center),delete a.center)};f.setPaths(e);f.fillSetter=function(e){var c=a(e).brighten(-.1).get();this.fill=e;this.side1.attr({fill:c});this.side2.attr({fill:c});this.inn.attr({fill:c});this.out.attr({fill:c});this.top.attr({fill:e});return this};["opacity","translateX","translateY","visibility"].forEach(function(a){f[a+"Setter"]=function(a,e){f[e]=a;["out","inn","side1","side2","top"].forEach(function(c){f[c].attr(e,
a)})}});f.attr=function(a){var e;"object"===typeof a&&(e=l(a))&&(x(f.attribs,e),f.setPaths(f.attribs));return c.prototype.attr.apply(f,arguments)};f.animate=function(a,e,d){var p=this.attribs,v="data-"+Math.random().toString(26).substring(2,9);delete a.center;delete a.z;delete a.depth;delete a.alpha;delete a.beta;var h=w(n(e,this.renderer.globalAnimation));if(h.duration){var q=l(a);f[v]=0;a[v]=1;f[v+"Setter"]=b.noop;q&&(h.step=function(a,e){function c(a){return p[a]+(n(q[a],p[a])-p[a])*e.pos}e.prop===
v&&e.elem.setPaths(k(p,{x:c("x"),y:c("y"),r:c("r"),innerR:c("innerR"),start:c("start"),end:c("end")}))});e=h}return c.prototype.animate.call(this,a,e,d)};f.destroy=function(){this.top.destroy();this.out.destroy();this.inn.destroy();this.side1.destroy();this.side2.destroy();return c.prototype.destroy.call(this)};f.hide=function(){this.top.hide();this.out.hide();this.inn.hide();this.side1.hide();this.side2.hide()};f.show=function(a){this.top.show(a);this.out.show(a);this.inn.show(a);this.side1.show(a);
this.side2.show(a)};return f};m.prototype.arc3dPath=function(a){function e(a){a%=2*Math.PI;a>Math.PI&&(a=2*Math.PI-a);return a}var c=a.x,d=a.y,b=a.start,l=a.end-.00001,k=a.r,p=a.innerR||0,v=a.depth||0,h=a.alpha,q=a.beta,w=Math.cos(b),g=Math.sin(b);a=Math.cos(l);var m=Math.sin(l),n=k*Math.cos(q);k*=Math.cos(h);var x=p*Math.cos(q),t=p*Math.cos(h);p=v*Math.sin(q);var A=v*Math.sin(h);v=["M",c+n*w,d+k*g];v=v.concat(z(c,d,n,k,b,l,0,0));v=v.concat(["L",c+x*a,d+t*m]);v=v.concat(z(c,d,x,t,l,b,0,0));v=v.concat(["Z"]);
var B=0<q?Math.PI/2:0;q=0<h?0:Math.PI/2;B=b>-B?b:l>-B?-B:b;var D=l<u-q?l:b<u-q?u-q:l,E=2*u-q;h=["M",c+n*y(B),d+k*r(B)];h=h.concat(z(c,d,n,k,B,D,0,0));l>E&&b<E?(h=h.concat(["L",c+n*y(D)+p,d+k*r(D)+A]),h=h.concat(z(c,d,n,k,D,E,p,A)),h=h.concat(["L",c+n*y(E),d+k*r(E)]),h=h.concat(z(c,d,n,k,E,l,0,0)),h=h.concat(["L",c+n*y(l)+p,d+k*r(l)+A]),h=h.concat(z(c,d,n,k,l,E,p,A)),h=h.concat(["L",c+n*y(E),d+k*r(E)]),h=h.concat(z(c,d,n,k,E,D,0,0))):l>u-q&&b<u-q&&(h=h.concat(["L",c+n*Math.cos(D)+p,d+k*Math.sin(D)+
A]),h=h.concat(z(c,d,n,k,D,l,p,A)),h=h.concat(["L",c+n*Math.cos(l),d+k*Math.sin(l)]),h=h.concat(z(c,d,n,k,l,D,0,0)));h=h.concat(["L",c+n*Math.cos(D)+p,d+k*Math.sin(D)+A]);h=h.concat(z(c,d,n,k,D,B,p,A));h=h.concat(["Z"]);q=["M",c+x*w,d+t*g];q=q.concat(z(c,d,x,t,b,l,0,0));q=q.concat(["L",c+x*Math.cos(l)+p,d+t*Math.sin(l)+A]);q=q.concat(z(c,d,x,t,l,b,p,A));q=q.concat(["Z"]);w=["M",c+n*w,d+k*g,"L",c+n*w+p,d+k*g+A,"L",c+x*w+p,d+t*g+A,"L",c+x*w,d+t*g,"Z"];c=["M",c+n*a,d+k*m,"L",c+n*a+p,d+k*m+A,"L",c+x*
a+p,d+t*m+A,"L",c+x*a,d+t*m,"Z"];m=Math.atan2(A,-p);d=Math.abs(l+m);a=Math.abs(b+m);b=Math.abs((b+l)/2+m);d=e(d);a=e(a);b=e(b);b*=1E5;l=1E5*a;d*=1E5;return{top:v,zTop:1E5*Math.PI+1,out:h,zOut:Math.max(b,l,d),inn:q,zInn:Math.max(b,l,d),side1:w,zSide1:.99*d,side2:c,zSide2:.99*l}}});B(r,"parts-3d/Chart.js",[r["parts/Globals.js"],r["parts/Utilities.js"]],function(b,m){function r(b,h){var a=b.plotLeft,d=b.plotWidth+a,k=b.plotTop,q=b.plotHeight+k,c=a+b.plotWidth/2,e=k+b.plotHeight/2,l=Number.MAX_VALUE,
v=-Number.MAX_VALUE,p=Number.MAX_VALUE,n=-Number.MAX_VALUE,f=1;var w=[{x:a,y:k,z:0},{x:a,y:k,z:h}];[0,1].forEach(function(a){w.push({x:d,y:w[a].y,z:w[a].z})});[0,1,2,3].forEach(function(a){w.push({x:w[a].x,y:q,z:w[a].z})});w=y(w,b,!1);w.forEach(function(a){l=Math.min(l,a.x);v=Math.max(v,a.x);p=Math.min(p,a.y);n=Math.max(n,a.y)});a>l&&(f=Math.min(f,1-Math.abs((a+c)/(l+c))%1));d<v&&(f=Math.min(f,(d-c)/(v-c)));k>p&&(f=0>p?Math.min(f,(k+e)/(-p+k+e)):Math.min(f,1-(k+e)/(p+e)%1));q<n&&(f=Math.min(f,Math.abs((q-
e)/(n-e))));return f}var t=m.isArray,x=m.pick;m=b.addEvent;var g=b.Chart,n=b.merge,y=b.perspective,u=b.wrap;g.prototype.is3d=function(){return this.options.chart.options3d&&this.options.chart.options3d.enabled};g.prototype.propsRequireDirtyBox.push("chart.options3d");g.prototype.propsRequireUpdateSeries.push("chart.options3d");m(g,"afterInit",function(){var b=this.options;this.is3d()&&(b.series||[]).forEach(function(h){"scatter"===(h.type||b.chart.type||b.chart.defaultSeriesType)&&(h.type="scatter3d")})});
m(g,"addSeries",function(b){this.is3d()&&"scatter"===b.options.type&&(b.options.type="scatter3d")});b.wrap(b.Chart.prototype,"isInsidePlot",function(b){return this.is3d()||b.apply(this,[].slice.call(arguments,1))});var A=b.getOptions();n(!0,A,{chart:{options3d:{enabled:!1,alpha:0,beta:0,depth:100,fitToPlot:!0,viewDistance:25,axisLabelPosition:null,frame:{visible:"default",size:1,bottom:{},top:{},left:{},right:{},back:{},front:{}}}}});m(g,"afterGetContainer",function(){this.styledMode&&(this.renderer.definition({tagName:"style",
textContent:".highcharts-3d-top{filter: url(#highcharts-brighter)}\n.highcharts-3d-side{filter: url(#highcharts-darker)}\n"}),[{name:"darker",slope:.6},{name:"brighter",slope:1.4}].forEach(function(b){this.renderer.definition({tagName:"filter",id:"highcharts-"+b.name,children:[{tagName:"feComponentTransfer",children:[{tagName:"feFuncR",type:"linear",slope:b.slope},{tagName:"feFuncG",type:"linear",slope:b.slope},{tagName:"feFuncB",type:"linear",slope:b.slope}]}]})},this))});u(g.prototype,"setClassName",
function(b){b.apply(this,[].slice.call(arguments,1));this.is3d()&&(this.container.className+=" highcharts-3d-chart")});m(b.Chart,"afterSetChartSize",function(){var b=this.options.chart.options3d;if(this.is3d()){var h=this.inverted,a=this.clipBox,d=this.margin;a[h?"y":"x"]=-(d[3]||0);a[h?"x":"y"]=-(d[0]||0);a[h?"height":"width"]=this.chartWidth+(d[3]||0)+(d[1]||0);a[h?"width":"height"]=this.chartHeight+(d[0]||0)+(d[2]||0);this.scale3d=1;!0===b.fitToPlot&&(this.scale3d=r(this,b.depth));this.frame3d=
this.get3dFrame()}});m(g,"beforeRedraw",function(){this.is3d()&&(this.isDirtyBox=!0)});m(g,"beforeRender",function(){this.is3d()&&(this.frame3d=this.get3dFrame())});u(g.prototype,"renderSeries",function(b){var h=this.series.length;if(this.is3d())for(;h--;)b=this.series[h],b.translate(),b.render();else b.call(this)});m(g,"afterDrawChartBox",function(){if(this.is3d()){var n=this.renderer,h=this.options.chart.options3d,a=this.get3dFrame(),d=this.plotLeft,k=this.plotLeft+this.plotWidth,q=this.plotTop,
c=this.plotTop+this.plotHeight;h=h.depth;var e=d-(a.left.visible?a.left.size:0),l=k+(a.right.visible?a.right.size:0),v=q-(a.top.visible?a.top.size:0),p=c+(a.bottom.visible?a.bottom.size:0),g=0-(a.front.visible?a.front.size:0),f=h+(a.back.visible?a.back.size:0),m=this.hasRendered?"animate":"attr";this.frame3d=a;this.frameShapes||(this.frameShapes={bottom:n.polyhedron().add(),top:n.polyhedron().add(),left:n.polyhedron().add(),right:n.polyhedron().add(),back:n.polyhedron().add(),front:n.polyhedron().add()});
this.frameShapes.bottom[m]({"class":"highcharts-3d-frame highcharts-3d-frame-bottom",zIndex:a.bottom.frontFacing?-1E3:1E3,faces:[{fill:b.color(a.bottom.color).brighten(.1).get(),vertexes:[{x:e,y:p,z:g},{x:l,y:p,z:g},{x:l,y:p,z:f},{x:e,y:p,z:f}],enabled:a.bottom.visible},{fill:b.color(a.bottom.color).brighten(.1).get(),vertexes:[{x:d,y:c,z:h},{x:k,y:c,z:h},{x:k,y:c,z:0},{x:d,y:c,z:0}],enabled:a.bottom.visible},{fill:b.color(a.bottom.color).brighten(-.1).get(),vertexes:[{x:e,y:p,z:g},{x:e,y:p,z:f},
{x:d,y:c,z:h},{x:d,y:c,z:0}],enabled:a.bottom.visible&&!a.left.visible},{fill:b.color(a.bottom.color).brighten(-.1).get(),vertexes:[{x:l,y:p,z:f},{x:l,y:p,z:g},{x:k,y:c,z:0},{x:k,y:c,z:h}],enabled:a.bottom.visible&&!a.right.visible},{fill:b.color(a.bottom.color).get(),vertexes:[{x:l,y:p,z:g},{x:e,y:p,z:g},{x:d,y:c,z:0},{x:k,y:c,z:0}],enabled:a.bottom.visible&&!a.front.visible},{fill:b.color(a.bottom.color).get(),vertexes:[{x:e,y:p,z:f},{x:l,y:p,z:f},{x:k,y:c,z:h},{x:d,y:c,z:h}],enabled:a.bottom.visible&&
!a.back.visible}]});this.frameShapes.top[m]({"class":"highcharts-3d-frame highcharts-3d-frame-top",zIndex:a.top.frontFacing?-1E3:1E3,faces:[{fill:b.color(a.top.color).brighten(.1).get(),vertexes:[{x:e,y:v,z:f},{x:l,y:v,z:f},{x:l,y:v,z:g},{x:e,y:v,z:g}],enabled:a.top.visible},{fill:b.color(a.top.color).brighten(.1).get(),vertexes:[{x:d,y:q,z:0},{x:k,y:q,z:0},{x:k,y:q,z:h},{x:d,y:q,z:h}],enabled:a.top.visible},{fill:b.color(a.top.color).brighten(-.1).get(),vertexes:[{x:e,y:v,z:f},{x:e,y:v,z:g},{x:d,
y:q,z:0},{x:d,y:q,z:h}],enabled:a.top.visible&&!a.left.visible},{fill:b.color(a.top.color).brighten(-.1).get(),vertexes:[{x:l,y:v,z:g},{x:l,y:v,z:f},{x:k,y:q,z:h},{x:k,y:q,z:0}],enabled:a.top.visible&&!a.right.visible},{fill:b.color(a.top.color).get(),vertexes:[{x:e,y:v,z:g},{x:l,y:v,z:g},{x:k,y:q,z:0},{x:d,y:q,z:0}],enabled:a.top.visible&&!a.front.visible},{fill:b.color(a.top.color).get(),vertexes:[{x:l,y:v,z:f},{x:e,y:v,z:f},{x:d,y:q,z:h},{x:k,y:q,z:h}],enabled:a.top.visible&&!a.back.visible}]});
this.frameShapes.left[m]({"class":"highcharts-3d-frame highcharts-3d-frame-left",zIndex:a.left.frontFacing?-1E3:1E3,faces:[{fill:b.color(a.left.color).brighten(.1).get(),vertexes:[{x:e,y:p,z:g},{x:d,y:c,z:0},{x:d,y:c,z:h},{x:e,y:p,z:f}],enabled:a.left.visible&&!a.bottom.visible},{fill:b.color(a.left.color).brighten(.1).get(),vertexes:[{x:e,y:v,z:f},{x:d,y:q,z:h},{x:d,y:q,z:0},{x:e,y:v,z:g}],enabled:a.left.visible&&!a.top.visible},{fill:b.color(a.left.color).brighten(-.1).get(),vertexes:[{x:e,y:p,
z:f},{x:e,y:v,z:f},{x:e,y:v,z:g},{x:e,y:p,z:g}],enabled:a.left.visible},{fill:b.color(a.left.color).brighten(-.1).get(),vertexes:[{x:d,y:q,z:h},{x:d,y:c,z:h},{x:d,y:c,z:0},{x:d,y:q,z:0}],enabled:a.left.visible},{fill:b.color(a.left.color).get(),vertexes:[{x:e,y:p,z:g},{x:e,y:v,z:g},{x:d,y:q,z:0},{x:d,y:c,z:0}],enabled:a.left.visible&&!a.front.visible},{fill:b.color(a.left.color).get(),vertexes:[{x:e,y:v,z:f},{x:e,y:p,z:f},{x:d,y:c,z:h},{x:d,y:q,z:h}],enabled:a.left.visible&&!a.back.visible}]});this.frameShapes.right[m]({"class":"highcharts-3d-frame highcharts-3d-frame-right",
zIndex:a.right.frontFacing?-1E3:1E3,faces:[{fill:b.color(a.right.color).brighten(.1).get(),vertexes:[{x:l,y:p,z:f},{x:k,y:c,z:h},{x:k,y:c,z:0},{x:l,y:p,z:g}],enabled:a.right.visible&&!a.bottom.visible},{fill:b.color(a.right.color).brighten(.1).get(),vertexes:[{x:l,y:v,z:g},{x:k,y:q,z:0},{x:k,y:q,z:h},{x:l,y:v,z:f}],enabled:a.right.visible&&!a.top.visible},{fill:b.color(a.right.color).brighten(-.1).get(),vertexes:[{x:k,y:q,z:0},{x:k,y:c,z:0},{x:k,y:c,z:h},{x:k,y:q,z:h}],enabled:a.right.visible},{fill:b.color(a.right.color).brighten(-.1).get(),
vertexes:[{x:l,y:p,z:g},{x:l,y:v,z:g},{x:l,y:v,z:f},{x:l,y:p,z:f}],enabled:a.right.visible},{fill:b.color(a.right.color).get(),vertexes:[{x:l,y:v,z:g},{x:l,y:p,z:g},{x:k,y:c,z:0},{x:k,y:q,z:0}],enabled:a.right.visible&&!a.front.visible},{fill:b.color(a.right.color).get(),vertexes:[{x:l,y:p,z:f},{x:l,y:v,z:f},{x:k,y:q,z:h},{x:k,y:c,z:h}],enabled:a.right.visible&&!a.back.visible}]});this.frameShapes.back[m]({"class":"highcharts-3d-frame highcharts-3d-frame-back",zIndex:a.back.frontFacing?-1E3:1E3,faces:[{fill:b.color(a.back.color).brighten(.1).get(),
vertexes:[{x:l,y:p,z:f},{x:e,y:p,z:f},{x:d,y:c,z:h},{x:k,y:c,z:h}],enabled:a.back.visible&&!a.bottom.visible},{fill:b.color(a.back.color).brighten(.1).get(),vertexes:[{x:e,y:v,z:f},{x:l,y:v,z:f},{x:k,y:q,z:h},{x:d,y:q,z:h}],enabled:a.back.visible&&!a.top.visible},{fill:b.color(a.back.color).brighten(-.1).get(),vertexes:[{x:e,y:p,z:f},{x:e,y:v,z:f},{x:d,y:q,z:h},{x:d,y:c,z:h}],enabled:a.back.visible&&!a.left.visible},{fill:b.color(a.back.color).brighten(-.1).get(),vertexes:[{x:l,y:v,z:f},{x:l,y:p,
z:f},{x:k,y:c,z:h},{x:k,y:q,z:h}],enabled:a.back.visible&&!a.right.visible},{fill:b.color(a.back.color).get(),vertexes:[{x:d,y:q,z:h},{x:k,y:q,z:h},{x:k,y:c,z:h},{x:d,y:c,z:h}],enabled:a.back.visible},{fill:b.color(a.back.color).get(),vertexes:[{x:e,y:p,z:f},{x:l,y:p,z:f},{x:l,y:v,z:f},{x:e,y:v,z:f}],enabled:a.back.visible}]});this.frameShapes.front[m]({"class":"highcharts-3d-frame highcharts-3d-frame-front",zIndex:a.front.frontFacing?-1E3:1E3,faces:[{fill:b.color(a.front.color).brighten(.1).get(),
vertexes:[{x:e,y:p,z:g},{x:l,y:p,z:g},{x:k,y:c,z:0},{x:d,y:c,z:0}],enabled:a.front.visible&&!a.bottom.visible},{fill:b.color(a.front.color).brighten(.1).get(),vertexes:[{x:l,y:v,z:g},{x:e,y:v,z:g},{x:d,y:q,z:0},{x:k,y:q,z:0}],enabled:a.front.visible&&!a.top.visible},{fill:b.color(a.front.color).brighten(-.1).get(),vertexes:[{x:e,y:v,z:g},{x:e,y:p,z:g},{x:d,y:c,z:0},{x:d,y:q,z:0}],enabled:a.front.visible&&!a.left.visible},{fill:b.color(a.front.color).brighten(-.1).get(),vertexes:[{x:l,y:p,z:g},{x:l,
y:v,z:g},{x:k,y:q,z:0},{x:k,y:c,z:0}],enabled:a.front.visible&&!a.right.visible},{fill:b.color(a.front.color).get(),vertexes:[{x:k,y:q,z:0},{x:d,y:q,z:0},{x:d,y:c,z:0},{x:k,y:c,z:0}],enabled:a.front.visible},{fill:b.color(a.front.color).get(),vertexes:[{x:l,y:p,z:g},{x:e,y:p,z:g},{x:e,y:v,z:g},{x:l,y:v,z:g}],enabled:a.front.visible}]})}});g.prototype.retrieveStacks=function(b){var h=this.series,a={},d,k=1;this.series.forEach(function(g){d=x(g.options.stack,b?0:h.length-1-g.index);a[d]?a[d].series.push(g):
(a[d]={series:[g],position:k},k++)});a.totalStacks=k+1;return a};g.prototype.get3dFrame=function(){var g=this,h=g.options.chart.options3d,a=h.frame,d=g.plotLeft,k=g.plotLeft+g.plotWidth,q=g.plotTop,c=g.plotTop+g.plotHeight,e=h.depth,l=function(a){a=b.shapeArea3d(a,g);return.5<a?1:-.5>a?-1:0},v=l([{x:d,y:c,z:e},{x:k,y:c,z:e},{x:k,y:c,z:0},{x:d,y:c,z:0}]),p=l([{x:d,y:q,z:0},{x:k,y:q,z:0},{x:k,y:q,z:e},{x:d,y:q,z:e}]),n=l([{x:d,y:q,z:0},{x:d,y:q,z:e},{x:d,y:c,z:e},{x:d,y:c,z:0}]),f=l([{x:k,y:q,z:e},
{x:k,y:q,z:0},{x:k,y:c,z:0},{x:k,y:c,z:e}]),m=l([{x:d,y:c,z:0},{x:k,y:c,z:0},{x:k,y:q,z:0},{x:d,y:q,z:0}]);l=l([{x:d,y:q,z:e},{x:k,y:q,z:e},{x:k,y:c,z:e},{x:d,y:c,z:e}]);var u=!1,t=!1,r=!1,A=!1;[].concat(g.xAxis,g.yAxis,g.zAxis).forEach(function(a){a&&(a.horiz?a.opposite?t=!0:u=!0:a.opposite?A=!0:r=!0)});var z=function(a,c,e){for(var d=["size","color","visible"],f={},b=0;b<d.length;b++)for(var l=d[b],k=0;k<a.length;k++)if("object"===typeof a[k]){var h=a[k][l];if(void 0!==h&&null!==h){f[l]=h;break}}a=
e;!0===f.visible||!1===f.visible?a=f.visible:"auto"===f.visible&&(a=0<c);return{size:x(f.size,1),color:x(f.color,"none"),frontFacing:0<c,visible:a}};a={axes:{},bottom:z([a.bottom,a.top,a],v,u),top:z([a.top,a.bottom,a],p,t),left:z([a.left,a.right,a.side,a],n,r),right:z([a.right,a.left,a.side,a],f,A),back:z([a.back,a.front,a],l,!0),front:z([a.front,a.back,a],m,!1)};"auto"===h.axisLabelPosition?(f=function(a,c){return a.visible!==c.visible||a.visible&&c.visible&&a.frontFacing!==c.frontFacing},h=[],f(a.left,
a.front)&&h.push({y:(q+c)/2,x:d,z:0,xDir:{x:1,y:0,z:0}}),f(a.left,a.back)&&h.push({y:(q+c)/2,x:d,z:e,xDir:{x:0,y:0,z:-1}}),f(a.right,a.front)&&h.push({y:(q+c)/2,x:k,z:0,xDir:{x:0,y:0,z:1}}),f(a.right,a.back)&&h.push({y:(q+c)/2,x:k,z:e,xDir:{x:-1,y:0,z:0}}),v=[],f(a.bottom,a.front)&&v.push({x:(d+k)/2,y:c,z:0,xDir:{x:1,y:0,z:0}}),f(a.bottom,a.back)&&v.push({x:(d+k)/2,y:c,z:e,xDir:{x:-1,y:0,z:0}}),p=[],f(a.top,a.front)&&p.push({x:(d+k)/2,y:q,z:0,xDir:{x:1,y:0,z:0}}),f(a.top,a.back)&&p.push({x:(d+k)/
2,y:q,z:e,xDir:{x:-1,y:0,z:0}}),n=[],f(a.bottom,a.left)&&n.push({z:(0+e)/2,y:c,x:d,xDir:{x:0,y:0,z:-1}}),f(a.bottom,a.right)&&n.push({z:(0+e)/2,y:c,x:k,xDir:{x:0,y:0,z:1}}),c=[],f(a.top,a.left)&&c.push({z:(0+e)/2,y:q,x:d,xDir:{x:0,y:0,z:-1}}),f(a.top,a.right)&&c.push({z:(0+e)/2,y:q,x:k,xDir:{x:0,y:0,z:1}}),d=function(a,c,e){if(0===a.length)return null;if(1===a.length)return a[0];for(var f=0,d=y(a,g,!1),b=1;b<d.length;b++)e*d[b][c]>e*d[f][c]?f=b:e*d[b][c]===e*d[f][c]&&d[b].z<d[f].z&&(f=b);return a[f]},
a.axes={y:{left:d(h,"x",-1),right:d(h,"x",1)},x:{top:d(p,"y",-1),bottom:d(v,"y",1)},z:{top:d(c,"y",-1),bottom:d(n,"y",1)}}):a.axes={y:{left:{x:d,z:0,xDir:{x:1,y:0,z:0}},right:{x:k,z:0,xDir:{x:0,y:0,z:1}}},x:{top:{y:q,z:0,xDir:{x:1,y:0,z:0}},bottom:{y:c,z:0,xDir:{x:1,y:0,z:0}}},z:{top:{x:r?k:d,y:q,xDir:r?{x:0,y:0,z:1}:{x:0,y:0,z:-1}},bottom:{x:r?k:d,y:c,xDir:r?{x:0,y:0,z:1}:{x:0,y:0,z:-1}}}};return a};b.Fx.prototype.matrixSetter=function(){if(1>this.pos&&(t(this.start)||t(this.end))){var b=this.start||
[1,0,0,1,0,0],h=this.end||[1,0,0,1,0,0];var a=[];for(var d=0;6>d;d++)a.push(this.pos*h[d]+(1-this.pos)*b[d])}else a=this.end;this.elem.attr(this.prop,a,null,!0)};""});B(r,"parts-3d/Axis.js",[r["parts/Globals.js"],r["parts/Utilities.js"]],function(b,m){function r(c,e,d){if(!c.chart.is3d()||"colorAxis"===c.coll)return e;var b=c.chart,l=u*b.options.chart.options3d.alpha,k=u*b.options.chart.options3d.beta,f=x(d&&c.options.title.position3d,c.options.labels.position3d);d=x(d&&c.options.title.skew3d,c.options.labels.skew3d);
var h=b.frame3d,g=b.plotLeft,q=b.plotWidth+g,n=b.plotTop,m=b.plotHeight+n;b=!1;var t=0,r=0,y={x:0,y:1,z:0};e=c.swapZ({x:e.x,y:e.y,z:0});if(c.isZAxis)if(c.opposite){if(null===h.axes.z.top)return{};r=e.y-n;e.x=h.axes.z.top.x;e.y=h.axes.z.top.y;g=h.axes.z.top.xDir;b=!h.top.frontFacing}else{if(null===h.axes.z.bottom)return{};r=e.y-m;e.x=h.axes.z.bottom.x;e.y=h.axes.z.bottom.y;g=h.axes.z.bottom.xDir;b=!h.bottom.frontFacing}else if(c.horiz)if(c.opposite){if(null===h.axes.x.top)return{};r=e.y-n;e.y=h.axes.x.top.y;
e.z=h.axes.x.top.z;g=h.axes.x.top.xDir;b=!h.top.frontFacing}else{if(null===h.axes.x.bottom)return{};r=e.y-m;e.y=h.axes.x.bottom.y;e.z=h.axes.x.bottom.z;g=h.axes.x.bottom.xDir;b=!h.bottom.frontFacing}else if(c.opposite){if(null===h.axes.y.right)return{};t=e.x-q;e.x=h.axes.y.right.x;e.z=h.axes.y.right.z;g=h.axes.y.right.xDir;g={x:g.z,y:g.y,z:-g.x}}else{if(null===h.axes.y.left)return{};t=e.x-g;e.x=h.axes.y.left.x;e.z=h.axes.y.left.z;g=h.axes.y.left.xDir}"chart"!==f&&("flap"===f?c.horiz?(k=Math.sin(l),
l=Math.cos(l),c.opposite&&(k=-k),b&&(k=-k),y={x:g.z*k,y:l,z:-g.x*k}):g={x:Math.cos(k),y:0,z:Math.sin(k)}:"ortho"===f?c.horiz?(y=Math.cos(l),f=Math.sin(k)*y,l=-Math.sin(l),k=-y*Math.cos(k),y={x:g.y*k-g.z*l,y:g.z*f-g.x*k,z:g.x*l-g.y*f},l=1/Math.sqrt(y.x*y.x+y.y*y.y+y.z*y.z),b&&(l=-l),y={x:l*y.x,y:l*y.y,z:l*y.z}):g={x:Math.cos(k),y:0,z:Math.sin(k)}:c.horiz?y={x:Math.sin(k)*Math.sin(l),y:Math.cos(l),z:-Math.cos(k)*Math.sin(l)}:g={x:Math.cos(k),y:0,z:Math.sin(k)});e.x+=t*g.x+r*y.x;e.y+=t*g.y+r*y.y;e.z+=
t*g.z+r*y.z;b=w([e],c.chart)[0];d&&(0>a(w([e,{x:e.x+g.x,y:e.y+g.y,z:e.z+g.z},{x:e.x+y.x,y:e.y+y.y,z:e.z+y.z}],c.chart))&&(g={x:-g.x,y:-g.y,z:-g.z}),c=w([{x:e.x,y:e.y,z:e.z},{x:e.x+g.x,y:e.y+g.y,z:e.z+g.z},{x:e.x+y.x,y:e.y+y.y,z:e.z+y.z}],c.chart),b.matrix=[c[1].x-c[0].x,c[1].y-c[0].y,c[2].x-c[0].x,c[2].y-c[0].y,b.x,b.y],b.matrix[4]-=b.x*b.matrix[0]+b.y*b.matrix[2],b.matrix[5]-=b.x*b.matrix[1]+b.y*b.matrix[3]);return b}var t=m.extend,x=m.pick,g=m.splat;m=b.addEvent;var n=b.Axis,y=b.Chart,u=b.deg2rad,
A=b.merge,w=b.perspective,h=b.perspective3D,a=b.shapeArea,d=b.Tick,k=b.wrap;A(!0,n.prototype.defaultOptions,{labels:{position3d:"offset",skew3d:!1},title:{position3d:null,skew3d:null}});m(n,"afterSetOptions",function(){if(this.chart.is3d&&this.chart.is3d()&&"colorAxis"!==this.coll){var a=this.options;a.tickWidth=x(a.tickWidth,0);a.gridLineWidth=x(a.gridLineWidth,1)}});k(n.prototype,"getPlotLinePath",function(a){var c=a.apply(this,[].slice.call(arguments,1));if(!this.chart.is3d()||"colorAxis"===this.coll||
null===c)return c;var b=this.chart,d=b.options.chart.options3d;d=this.isZAxis?b.plotWidth:d.depth;b=b.frame3d;c=[this.swapZ({x:c[1],y:c[2],z:0}),this.swapZ({x:c[1],y:c[2],z:d}),this.swapZ({x:c[4],y:c[5],z:0}),this.swapZ({x:c[4],y:c[5],z:d})];d=[];this.horiz?(this.isZAxis?(b.left.visible&&d.push(c[0],c[2]),b.right.visible&&d.push(c[1],c[3])):(b.front.visible&&d.push(c[0],c[2]),b.back.visible&&d.push(c[1],c[3])),b.top.visible&&d.push(c[0],c[1]),b.bottom.visible&&d.push(c[2],c[3])):(b.front.visible&&
d.push(c[0],c[2]),b.back.visible&&d.push(c[1],c[3]),b.left.visible&&d.push(c[0],c[1]),b.right.visible&&d.push(c[2],c[3]));d=w(d,this.chart,!1);return this.chart.renderer.toLineSegments(d)});k(n.prototype,"getLinePath",function(a){return this.chart.is3d()&&"colorAxis"!==this.coll?[]:a.apply(this,[].slice.call(arguments,1))});k(n.prototype,"getPlotBandPath",function(a){if(!this.chart.is3d()||"colorAxis"===this.coll)return a.apply(this,[].slice.call(arguments,1));var c=arguments,b=c[2],d=[];c=this.getPlotLinePath({value:c[1]});
b=this.getPlotLinePath({value:b});if(c&&b)for(var h=0;h<c.length;h+=6)d.push("M",c[h+1],c[h+2],"L",c[h+4],c[h+5],"L",b[h+4],b[h+5],"L",b[h+1],b[h+2],"Z");return d});k(d.prototype,"getMarkPath",function(a){var c=a.apply(this,[].slice.call(arguments,1));c=[r(this.axis,{x:c[1],y:c[2],z:0}),r(this.axis,{x:c[4],y:c[5],z:0})];return this.axis.chart.renderer.toLineSegments(c)});m(d,"afterGetLabelPosition",function(a){t(a.pos,r(this.axis,a.pos))});k(n.prototype,"getTitlePosition",function(a){var c=a.apply(this,
[].slice.call(arguments,1));return r(this,c,!0)});m(n,"drawCrosshair",function(a){this.chart.is3d()&&"colorAxis"!==this.coll&&a.point&&(a.point.crosshairPos=this.isXAxis?a.point.axisXpos:this.len-a.point.axisYpos)});m(n,"destroy",function(){["backFrame","bottomFrame","sideFrame"].forEach(function(a){this[a]&&(this[a]=this[a].destroy())},this)});n.prototype.swapZ=function(a,b){return this.isZAxis?(b=b?0:this.chart.plotLeft,{x:b+a.z,y:a.y,z:a.x-b}):a};var q=b.ZAxis=function(){this.init.apply(this,arguments)};
t(q.prototype,n.prototype);t(q.prototype,{isZAxis:!0,setOptions:function(a){a=A({offset:0,lineWidth:0},a);n.prototype.setOptions.call(this,a);this.coll="zAxis"},setAxisSize:function(){n.prototype.setAxisSize.call(this);this.width=this.len=this.chart.options.chart.options3d.depth;this.right=this.chart.chartWidth-this.width-this.left},getSeriesExtremes:function(){var a=this,b=a.chart;a.hasVisibleSeries=!1;a.dataMin=a.dataMax=a.ignoreMinPadding=a.ignoreMaxPadding=null;a.buildStacks&&a.buildStacks();
a.series.forEach(function(c){if(c.visible||!b.options.chart.ignoreHiddenSeries)a.hasVisibleSeries=!0,c=c.zData,c.length&&(a.dataMin=Math.min(x(a.dataMin,c[0]),Math.min.apply(null,c)),a.dataMax=Math.max(x(a.dataMax,c[0]),Math.max.apply(null,c)))})}});m(y,"afterGetAxes",function(){var a=this,b=this.options;b=b.zAxis=g(b.zAxis||{});a.is3d()&&(this.zAxis=[],b.forEach(function(c,b){c.index=b;c.isX=!0;(new q(a,c)).setScale()}))});k(n.prototype,"getSlotWidth",function(a,b){if(this.chart.is3d()&&b&&b.label&&
this.categories&&this.chart.frameShapes){var c=this.chart,d=this.ticks,e=this.gridGroup.element.childNodes[0].getBBox(),k=c.frameShapes.left.getBBox(),f=c.options.chart.options3d;c={x:c.plotWidth/2,y:c.plotHeight/2,z:f.depth/2,vd:x(f.depth,1)*x(f.viewDistance,0)};var g,q;f=b.pos;var n=d[f-1];d=d[f+1];0!==f&&n&&n.label.xy&&(g=h({x:n.label.xy.x,y:n.label.xy.y,z:null},c,c.vd));d&&d.label.xy&&(q=h({x:d.label.xy.x,y:d.label.xy.y,z:null},c,c.vd));d={x:b.label.xy.x,y:b.label.xy.y,z:null};d=h(d,c,c.vd);return Math.abs(g?
d.x-g.x:q?q.x-d.x:e.x-k.x)}return a.apply(this,[].slice.call(arguments,1))})});B(r,"parts-3d/Series.js",[r["parts/Globals.js"],r["parts/Utilities.js"]],function(b,m){var r=m.pick;m=b.addEvent;var t=b.perspective;m(b.Series,"afterTranslate",function(){this.chart.is3d()&&this.translate3dPoints()});b.Series.prototype.translate3dPoints=function(){var b=this.chart,g=r(this.zAxis,b.options.zAxis[0]),n=[],m;for(m=0;m<this.data.length;m++){var u=this.data[m];if(g&&g.translate){var A=g.isLog&&g.val2lin?g.val2lin(u.z):
u.z;u.plotZ=g.translate(A);u.isInside=u.isInside?A>=g.min&&A<=g.max:!1}else u.plotZ=0;u.axisXpos=u.plotX;u.axisYpos=u.plotY;u.axisZpos=u.plotZ;n.push({x:u.plotX,y:u.plotY,z:u.plotZ})}b=t(n,b,!0);for(m=0;m<this.data.length;m++)u=this.data[m],g=b[m],u.plotX=g.x,u.plotY=g.y,u.plotZ=g.z}});B(r,"parts-3d/Column.js",[r["parts/Globals.js"],r["parts/Utilities.js"]],function(b,m){function r(b){var a=b.apply(this,[].slice.call(arguments,1));this.chart.is3d&&this.chart.is3d()&&(a.stroke=this.options.edgeColor||
a.fill,a["stroke-width"]=g(this.options.edgeWidth,1));return a}function t(b,a,d){var h=this.chart.is3d&&this.chart.is3d();h&&(this.options.inactiveOtherPoints=!0);b.call(this,a,d);h&&(this.options.inactiveOtherPoints=!1)}function x(b){for(var a=[],d=1;d<arguments.length;d++)a[d-1]=arguments[d];return this.series.chart.is3d()?this.graphic&&"g"!==this.graphic.element.nodeName:b.apply(this,a)}var g=m.pick;m=b.addEvent;var n=b.perspective,y=b.Series,u=b.seriesTypes,A=b.svg,w=b.wrap;w(u.column.prototype,
"translate",function(b){b.apply(this,[].slice.call(arguments,1));this.chart.is3d()&&this.translate3dShapes()});w(b.Series.prototype,"alignDataLabel",function(b){arguments[3].outside3dPlot=arguments[1].outside3dPlot;b.apply(this,[].slice.call(arguments,1))});w(b.Series.prototype,"justifyDataLabel",function(b){return arguments[2].outside3dPlot?!1:b.apply(this,[].slice.call(arguments,1))});u.column.prototype.translate3dPoints=function(){};u.column.prototype.translate3dShapes=function(){var b=this,a=
b.chart,d=b.options,g=d.depth||25,q=(d.stacking?d.stack||0:b.index)*(g+(d.groupZPadding||1)),c=b.borderWidth%2?.5:0;a.inverted&&!b.yAxis.reversed&&(c*=-1);!1!==d.grouping&&(q=0);q+=d.groupZPadding||1;b.data.forEach(function(d){d.outside3dPlot=null;if(null!==d.y){var e=d.shapeArgs,h=d.tooltipPos,k;[["x","width"],["y","height"]].forEach(function(a){k=e[a[0]]-c;0>k&&(e[a[1]]+=e[a[0]]+c,e[a[0]]=-c,k=0);k+e[a[1]]>b[a[0]+"Axis"].len&&0!==e[a[1]]&&(e[a[1]]=b[a[0]+"Axis"].len-e[a[0]]);if(0!==e[a[1]]&&(e[a[0]]>=
b[a[0]+"Axis"].len||e[a[0]]+e[a[1]]<=c)){for(var f in e)e[f]=0;d.outside3dPlot=!0}});"rect"===d.shapeType&&(d.shapeType="cuboid");e.z=q;e.depth=g;e.insidePlotArea=!0;h=n([{x:h[0],y:h[1],z:q}],a,!0)[0];d.tooltipPos=[h.x,h.y]}});b.z=q};w(u.column.prototype,"animate",function(b){if(this.chart.is3d()){var a=arguments[1],d=this.yAxis,g=this,h=this.yAxis.reversed;A&&(a?g.data.forEach(function(a){null!==a.y&&(a.height=a.shapeArgs.height,a.shapey=a.shapeArgs.y,a.shapeArgs.height=1,h||(a.shapeArgs.y=a.stackY?
a.plotY+d.translate(a.stackY):a.plotY+(a.negative?-a.height:a.height)))}):(g.data.forEach(function(a){null!==a.y&&(a.shapeArgs.height=a.height,a.shapeArgs.y=a.shapey,a.graphic&&a.graphic.animate(a.shapeArgs,g.options.animation))}),this.drawDataLabels(),g.animate=null))}else b.apply(this,[].slice.call(arguments,1))});w(u.column.prototype,"plotGroup",function(b,a,d,g,q,c){"dataLabelsGroup"!==a&&this.chart.is3d()&&(this[a]&&delete this[a],c&&(this.chart.columnGroup||(this.chart.columnGroup=this.chart.renderer.g("columnGroup").add(c)),
this[a]=this.chart.columnGroup,this.chart.columnGroup.attr(this.getPlotBox()),this[a].survive=!0,"group"===a||"markerGroup"===a))&&(arguments[3]="visible");return b.apply(this,Array.prototype.slice.call(arguments,1))});w(u.column.prototype,"setVisible",function(b,a){var d=this,h;d.chart.is3d()&&d.data.forEach(function(b){h=(b.visible=b.options.visible=a=void 0===a?!g(d.visible,b.visible):a)?"visible":"hidden";d.options.data[d.data.indexOf(b)]=b.options;b.graphic&&b.graphic.attr({visibility:h})});
b.apply(this,Array.prototype.slice.call(arguments,1))});u.column.prototype.handle3dGrouping=!0;m(y,"afterInit",function(){if(this.chart.is3d()&&this.handle3dGrouping){var b=this.options,a=b.grouping,d=b.stacking,k=g(this.yAxis.options.reversedStacks,!0),n=0;if(void 0===a||a){a=this.chart.retrieveStacks(d);n=b.stack||0;for(d=0;d<a[n].series.length&&a[n].series[d]!==this;d++);n=10*(a.totalStacks-a[n].position)+(k?d:-d);this.xAxis.reversed||(n=10*a.totalStacks-n)}b.zIndex=n}});w(u.column.prototype,"pointAttribs",
r);w(u.column.prototype,"setState",t);w(u.column.prototype.pointClass.prototype,"hasNewShapeType",x);u.columnrange&&(w(u.columnrange.prototype,"pointAttribs",r),w(u.columnrange.prototype,"setState",t),w(u.columnrange.prototype.pointClass.prototype,"hasNewShapeType",x),u.columnrange.prototype.plotGroup=u.column.prototype.plotGroup,u.columnrange.prototype.setVisible=u.column.prototype.setVisible);w(y.prototype,"alignDataLabel",function(b){if(this.chart.is3d()&&this instanceof u.column){var a=arguments,
d=a[4];a=a[1];var g={x:d.x,y:d.y,z:this.z};g=n([g],this.chart,!0)[0];d.x=g.x;d.y=a.outside3dPlot?-9E9:g.y}b.apply(this,[].slice.call(arguments,1))});w(b.StackItem.prototype,"getStackBox",function(g,a){var d=g.apply(this,[].slice.call(arguments,1));if(a.is3d()){var h={x:d.x,y:d.y,z:0};h=b.perspective([h],a,!0)[0];d.x=h.x;d.y=h.y}return d})});B(r,"parts-3d/Pie.js",[r["parts/Globals.js"],r["parts/Utilities.js"]],function(b,m){var r=m.pick,t=b.deg2rad;m=b.seriesTypes;var x=b.svg;b=b.wrap;b(m.pie.prototype,
"translate",function(b){b.apply(this,[].slice.call(arguments,1));if(this.chart.is3d()){var g=this,m=g.options,u=m.depth||0,r=g.chart.options.chart.options3d,w=r.alpha,h=r.beta,a=m.stacking?(m.stack||0)*u:g._i*u;a+=u/2;!1!==m.grouping&&(a=0);g.data.forEach(function(b){var d=b.shapeArgs;b.shapeType="arc3d";d.z=a;d.depth=.75*u;d.alpha=w;d.beta=h;d.center=g.center;d=(d.end+d.start)/2;b.slicedTranslation={translateX:Math.round(Math.cos(d)*m.slicedOffset*Math.cos(w*t)),translateY:Math.round(Math.sin(d)*
m.slicedOffset*Math.cos(w*t))}})}});b(m.pie.prototype.pointClass.prototype,"haloPath",function(b){var g=arguments;return this.series.chart.is3d()?[]:b.call(this,g[1])});b(m.pie.prototype,"pointAttribs",function(b,n,m){b=b.call(this,n,m);m=this.options;this.chart.is3d()&&!this.chart.styledMode&&(b.stroke=m.edgeColor||n.color||this.color,b["stroke-width"]=r(m.edgeWidth,1));return b});b(m.pie.prototype,"drawDataLabels",function(b){if(this.chart.is3d()){var g=this.chart.options.chart.options3d;this.data.forEach(function(b){var n=
b.shapeArgs,m=n.r,r=(n.start+n.end)/2;b=b.labelPosition;var h=b.connectorPosition,a=-m*(1-Math.cos((n.alpha||g.alpha)*t))*Math.sin(r),d=m*(Math.cos((n.beta||g.beta)*t)-1)*Math.cos(r);[b.natural,h.breakAt,h.touchingSliceAt].forEach(function(b){b.x+=d;b.y+=a})})}b.apply(this,[].slice.call(arguments,1))});b(m.pie.prototype,"addPoint",function(b){b.apply(this,[].slice.call(arguments,1));this.chart.is3d()&&this.update(this.userOptions,!0)});b(m.pie.prototype,"animate",function(b){if(this.chart.is3d()){var g=
arguments[1],m=this.options.animation,r=this.center,t=this.group,w=this.markerGroup;x&&(!0===m&&(m={}),g?(t.oldtranslateX=t.translateX,t.oldtranslateY=t.translateY,g={translateX:r[0],translateY:r[1],scaleX:.001,scaleY:.001},t.attr(g),w&&(w.attrSetters=t.attrSetters,w.attr(g))):(g={translateX:t.oldtranslateX,translateY:t.oldtranslateY,scaleX:1,scaleY:1},t.animate(g,m),w&&w.animate(g,m),this.animate=null))}else b.apply(this,[].slice.call(arguments,1))})});B(r,"parts-3d/Scatter.js",[r["parts/Globals.js"]],
function(b){var m=b.Point,r=b.seriesType,t=b.seriesTypes;r("scatter3d","scatter",{tooltip:{pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>"}},{pointAttribs:function(m){var g=t.scatter.prototype.pointAttribs.apply(this,arguments);this.chart.is3d()&&m&&(g.zIndex=b.pointCameraDistance(m,this.chart));return g},axisTypes:["xAxis","yAxis","zAxis"],pointArrayMap:["x","y","z"],parallelArrays:["x","y","z"],directTouch:!0},{applyOptions:function(){m.prototype.applyOptions.apply(this,
arguments);void 0===this.z&&(this.z=0);return this}});""});B(r,"parts-3d/VMLRenderer.js",[r["parts/Globals.js"]],function(b){var m=b.addEvent,r=b.Axis,t=b.SVGRenderer,x=b.VMLRenderer;x&&(b.setOptions({animate:!1}),x.prototype.face3d=t.prototype.face3d,x.prototype.polyhedron=t.prototype.polyhedron,x.prototype.elements3d=t.prototype.elements3d,x.prototype.element3d=t.prototype.element3d,x.prototype.cuboid=t.prototype.cuboid,x.prototype.cuboidPath=t.prototype.cuboidPath,x.prototype.toLinePath=t.prototype.toLinePath,
x.prototype.toLineSegments=t.prototype.toLineSegments,x.prototype.arc3d=function(b){b=t.prototype.arc3d.call(this,b);b.css({zIndex:b.zIndex});return b},b.VMLRenderer.prototype.arc3dPath=b.SVGRenderer.prototype.arc3dPath,m(r,"render",function(){this.sideFrame&&(this.sideFrame.css({zIndex:0}),this.sideFrame.front.attr({fill:this.sideFrame.color}));this.bottomFrame&&(this.bottomFrame.css({zIndex:1}),this.bottomFrame.front.attr({fill:this.bottomFrame.color}));this.backFrame&&(this.backFrame.css({zIndex:0}),
this.backFrame.front.attr({fill:this.backFrame.color}))}))});B(r,"masters/highcharts-3d.src.js",[],function(){})});
//# sourceMappingURL=highcharts-3d.js.map