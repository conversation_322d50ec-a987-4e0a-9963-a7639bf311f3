<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Users";
	$pagename='codeManageAdminUser.php';
    include("inc/clsObj.php");	
	extract($_POST);	
	$obj_admin->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$obj_admin->adminName=$txtAdminName;
	$obj_admin->adminUsername=$txtUserName;
    $obj_admin->adminPassword=encryptPassword($txtPassword);
	$obj_admin->adminEmail=$txtEmail;
	$obj_admin->adminContact=$txtContact;
	$obj_admin->adminRole=$adminRole;
	$obj_admin->company="All";
	$obj_admin->adminRights=implode(",",$chkMain);
	// echo $obj_admin->adminRights; //die;
	$obj_admin->status=isset($_GET['status']) ? $_GET['status'] : 1;	
	if(isset($_POST['btnAdd']))
		{
			$obj_admin->insert();	
			redirect("codeManageAdminUser.php?msg=add");			
		}
	if(isset($_POST['btnUpdate']))
		{	
			 $obj_admin->update();
			 redirect("codeManageAdminUser.php?msg=edit");
		}
	if(isset($_POST['btnAction']))
		{
		 extract($_POST);
		 switch($optAction)
		  {
		  	case 0:
					$obj_admin->deleteSelect($chkAction);
					break;
			case 1:
			 		$obj_admin->statusUpdatePublish($chkAction);
			        break;
			case 2:
			        $obj_admin->statusUpdateUnPublish($chkAction);
		  } 		  
		 redirect("codeManageAdminUser.php?msg=edit");
		}	
	if(isset($_GET['delete']))
		{			 
			  $obj_admin->delete();
			  redirect("codeManageAdminUser.php?msg=del");
		}	
	if(isset($_GET['status']))
		{			 
			  $obj_admin->status();
			  redirect("codeManageAdminUser.php?msg=status");		
		}
	if(isset($_GET['id']))
		{			
		  $listEdit=$obj_admin->selectRecById();		
		}	
	$listRec=$obj_admin->paging();
   
	include("html/frmManageAdminUser.php");
 } 
?>