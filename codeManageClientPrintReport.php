<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Envelope Print ";
	$pageName="codeManageClientPrintReport.php";
    include("inc/clsObj.php");	
	extract($_POST);	

	// echo $Contact_Number1; die;
	$objParty->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objParty->party_name=$Company_Name;
	$objParty->party_address=$Party_Address;	
	$objParty->city=$City;
	$objParty->state=$State;
	$objParty->zipcode=$Zipcode;
	$objParty->country=$Country;
	$objParty->office_no1=$Office_Number1;
	$objParty->office_no2=$Office_Number2;
	$objParty->fax_no=$Fax_Number;
	$objParty->email=$Email_Address;
	$objParty->domain_name=$Website;
	$objParty->contact_name1=$Contact_Name1;
	$objParty->contact_no1=$Contact_Number1;
	$objParty->email1=$Email_Address1;
	$objParty->contact_name2=$Contact_Name2;
	$objParty->contact_no2=$Contact_Number2;
	$objParty->email2=$Email_Address2;
	$objParty->reference_name=$Reference_Name;
	$objParty->reference_no=$Reference_Number;
	$objParty->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objParty->insert();	
		redirect("codeManageParty.php?msg=add");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		$objParty->update();
		redirect("codeManageParty.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objParty->deleteSelect($chkAction);
					break;
			case 1:
					$objParty->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objParty->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageParty.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objParty->delete();
		redirect("codeManageParty.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objParty->status();
		redirect("codeManageParty.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objParty->selectRecById();						
	}	
	
	if($_GET['Client_Name']!="" || $_GET['Area_Name'])
	{
		if($_GET['Client_Name']!="")
			$query.=" and id='".$_GET['Client_Name']."'";
		/* if($_GET['Area_Name']!="")
			$query.=" and area='".$_GET['Area_Name']."'"; */
				
		$listRec=$objParty->selectRecBySearch($query);
	}
	else
		$listRec=$objParty->select();
    
	include("html/frmManageClientPrintReport.php");
 } 
?>