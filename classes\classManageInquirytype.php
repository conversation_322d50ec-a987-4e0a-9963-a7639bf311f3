<?php
global $db;
class inquirytypeMaster
{
   /* Variable Declaration*/		
	var $tablename='inquirytype_master';
	var $id;
	var $year_name;
	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function inquirytypeMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
					'$this->inquiry_type',
					'$this->status')";
		// echo $sql;die();							
		$this->db->insert($sql);
		$id=mysql_insert_id();
		return($id);
	} 			


	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/* Fetch all the records */	
	function selectStatus()
	{
		$sql ="select * from `$this->tablename` where status=1 order by inquiry_type";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function getNameById($id)
	{
		$this->id = $id;
		$sql ="select inquiry_type from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['inquiry_type']);
	}
	
	/*Fectch record by id from Database*/		
	function getCodeById($id)
	{
		$this->id = $id;
		$sql ="select inquirytype_code from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['inquirytype_code']);
	}
	
	/*Fectch record by id from Database*/		
	function getAreaName($id)
	{
		$this->id = $id;
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['inquiry_type']);
	}
	
	/*Fectch record by id from Database*/		
	function chkAreaName($inquirytypename)
	{
		$this->id = $id;
		$sql ="select * from `$this->tablename` 
			   where inquiry_type=$inquirytypename";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/
	function selectRecByArea($inquirytypename)
	{
		$sql ="select * from `$this->tablename` 
			   where inquiry_type='$inquirytypename'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/* update the record in database*/		
	function update()
	{
		$sql = "update `$this->tablename` set
						`inquiry_type`='$this->inquiry_type'
						 where `id`=$this->id";
		// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";//echo $sql;die();
		mysql_query($sql);	  
	}		

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
		{
			for($i=0;$i<count($chk);$i++)
					{
						$id = $chk[$i];
						$sql="delete from `$this->tablename` where `id` = '$id'";
						$res = mysql_query($sql);
					}
			return true;
		}	
					
	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` order by inquiry_type";
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 100;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   
}		
?>   