<?php
global $db;
class expenseMaster
{
   /* Variable Declaration*/		
	var $tablename='expense_master';
	var $id;
	var $party_name;
	var $party_address;
	var $city;
	var $state;
	var $zipcode;
	var $country;
	var $office_no1;
	var $office_no2;
	var $fax_no;
	var $email;
	var $domain_name;
	var $contact_name1;
	var $contact_no1;
	var $email1;
	var $contact_name2;
	var $contact_no2;
	var $email2;
	var $reference_name;
	var $reference_no;

	var $status;	 
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function expenseMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
					'$this->party_name',
					'$this->party_address',
					'$this->city',
					'$this->state',
					'$this->zipcode',
					'$this->country',
					'$this->email',
					'$this->contact_no',
					'$this->status')";
					// echo $sql;die();							
		$this->db->insert($sql);
		$id=mysql_insert_id();
		return($id);
	} 			

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename` order by party_name";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	
	
	/* Fetch all the records */	
	function selectStatus()
	{
		$sql ="select * from `$this->tablename` where status = 1 order by party_name";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}		

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function getPartyName($id)
	{
		$this->id = $id;
		$sql ="select * from `$this->tablename` 
			   where id='$this->id'";
			   // echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['party_name']);
	}
	/* update the record in database*/		
	function update()
	{
		$sql = "update `$this->tablename` set
					`party_name`='$this->party_name',
					`party_address`='$this->party_address',	
					`city`='$this->city',
					`state`='$this->state',
					`zipcode`='$this->zipcode',
					`country`='$this->country',
					`email`='$this->email',
					`contact_no`='$this->contact_no'
				 where `id`=$this->id";
						 // echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";//echo $sql;die();
		mysql_query($sql);	  
	}		

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
		{
			for($i=0;$i<count($chk);$i++)
					{
						$id = $chk[$i];
						$sql="delete from `$this->tablename` where `id` = '$id'";
						$res = mysql_query($sql);
					}
			return true;
		}	

	function selectRecBySearch($query)
	{
		$sql ="select * from `$this->tablename` where id!=''".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

					
	/*...paging...*/
	function paging()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` order by party_name";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	
		/*...paging...*/
	function paging_pending()
	{			
		$pages = new Paging();
		$pages->sql ="select * from `$this->tablename` order by party_name";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 500;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}   
}		
?>   