<?	session_start(); 
	error_reporting(0);
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>ERP Demo</title>
</head>
<?
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	include("inc/constant.php");
	checkLogin();	

	$objQuote->id=$_GET['id'];
	$listEdit=$objQuote->selectRecById();
	$objQuote->qid = $listEdit[0]['id'];
	$listItems=$objQuote->selectItemsByBillNo();

	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
p{
	margin:0px!important;
	padding:0px!important;
}
#print_header{
	font-size:12px;
	font-family:Verdana, Geneva, sans-serif;
}
.tbl_invoice td{
	font-size:13px!important;
	padding:5px;
	vertical-align:top!important;
}

.tbl_invoice td ul, .tbl_invoice td ol{
	padding:0px 20px!important;
	margin-top:0px!important;
}

.tbl_invoice td ul li, .tbl_invoice td ol li{
	font-size:12px!important;
}


.tbl_invoice td strong{
	font-size:13px!important;
}

table {
    border:solid #999 !important;
    border-width:1px !important;
}
th, td {
    border:solid #999 !important;
    border-width:1px !important;
}

@media print 
{
	@page  
	{ 
		/* size: auto; */   /* auto is the initial value */ 
		/* this affects the margin in the printer settings 
		margin-top: 40mm!important;
		margin-bottom: 30mm!important;
		margin-left:25px!important;*/ 
		margin:4cm 0 2cm 0;
	} 
} 

</style>
<body>
<?php /*?><div style="width:100%;" id="print_header"><?php */?>
<div style="width:700px; margin:0 auto;">
<table width="100%" border="1" cellpadding="0" cellspacing="0">
<tr>
  <td>
<table width="100%" border="1" cellspacing="0" cellpadding="5" class="tbl_invoice">
  <tr class="toprow">
    <td width="55%" align="left" valign="top" style="padding:5px!important;" rowspan="2">
    	<strong>M/s :</strong><br/>
		<?=$listEdit[0]['party']."<br/>".$listEdit[0]['party_address']."<br/>".$listEdit[0]['party_city']; ?></td>
    <td width="45%" align="right" valign="top" style="padding:5px!important;" >
      <strong>Quotation No. : </strong>
      <?=$listEdit[0]['quote_no'];?>      </td>
  </tr>
  <tr class="toprow">
    <td align="right" valign="bottom" style="padding:5px!important;" ><strong>Date : </strong><?=$listEdit[0]['bdt'];?></td>
  </tr>
  
  <tr class="toprow"><td align="center" valign="top" colspan="2" style="padding:10px!important; padding:10px!important;"><strong style="font-size:15px!important;">Quotation : <?=$listEdit[0]['quote_for'];?></strong></td></tr>
  </table>
</td></tr>
<tr>
<td>
<table width="100%" border="1" cellspacing="0" cellpadding="5" class="tbl_invoice">
    <tr>
      <td width="5%" style="text-align:center;"><strong>No.</strong></td>
      <td width="50%" style="text-align:center;"><strong>Particulars</strong></td>
      <td width="17%" style="text-align:center;"><strong>Rate</strong></td>
      <td width="10%" style="text-align:center;"><strong>Quantity</strong></td>
      <td width="18%" style="text-align:center;"><strong>Amount</strong></td>
    </tr>
  <? for($i=0;$i<count($listItems);$i++) { ?>
  <tr>
  	<td valign="top" style="text-align:center;"><?=$i+1;?></td>
   <td valign="top">
   	<? 	$objItem->id=$listItems[$i]['item'];
		$itemdet=$objItem->selectRecById();
		// echo "<strong>".$itemdet[0]['item_name']."</strong><br/>";
		echo $listItems[$i]['description'];
	?>
	</td>
    <td style="text-align:center;"  valign="top"><img src="images/inr_img.png" width="8" height="10"> <?=$listItems[$i]['rate_per_qty'];?></td>
    <td style="text-align:center;"  valign="top"><?=$listItems[$i]['no_of_items'];?></td>
    <td style="text-align:right;"  valign="top"><img src="images/inr_img.png" width="8" height="10"> <?=$listItems[$i]['amount'];?></td>
  </tr>
<? } ?>  
  <tr class="toprow">
    <td colspan="4" style="text-align:right;">Net Amount :</td>
	<td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['net_amount'];?></td></tr>
    <? if($listEdit[0]['quote_type']=="GST"){ ?>
    	<tr class="toprow">
        <td colspan="4" style="text-align:right;">CGST@9% :</td>
        <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['cgst_amt'];?></td></tr>
        <tr class="toprow">
        <td colspan="4" style="text-align:right;">SGST@9% :</td>
        <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['sgst_amt'];?></td></tr>
    <? }
	if($listEdit[0]['quote_type']=="IGST"){ ?>
		<tr class="toprow">
        <td colspan="4" style="text-align:right;">IGST@18% :</td>
        <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['igst_amt'];?></td></tr>
	<? } ?>
    <tr>
    <td colspan="4" style="text-align:right;">Total Amount :</td>
    <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['amount'];?></td>
  </tr>  
  <tr><td colspan="5">
<?
function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }

echo "<strong>Amount Chargeable (in words) :</strong> Rs. ".ucwords(no_to_words($listEdit[0]['amount']))." Only";
?>
</td></tr>
</table>
</td>
</tr>

<tr>
<td>
<table border="1" cellspacing="0" cellpadding="5" width="100%" class="tbl_invoice">
  <tr>
    <td width="348" valign="top">
<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
<?=$listEdit[0]['quote_terms'];?>
</td>
    <td width="226" valign="top" ><br/>
    <div style="padding-left:20px!important;">
    <?=$listEdit[0]['quote_signature'];?>
    </div>
  <br/><br/>
  <hr/>
  <br/>
      <p align="center">For, <?=$_SESSION['company'];?></p>
      <br/><p align="center"><br/><br/><br/><br/><br/><br/><br/></p><br/>
      <p align="center">AUTHORISED    SIGNATORY</p></td>
  </tr>
</table>
</td>
</tr>
</table>
</div>
<?php /*?></div><?php */?>
<script language="javascript">
	window.print();
</script>
</body>
</html>