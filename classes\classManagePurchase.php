<?php
global $db;
class purchaseMaster
{
   /* Variable Declaration */		
	var $tablename='purchase';
	var $id;
	var $party;
	var $amount;
	var $cheque_cash;
	var $cheque_detail;
	var $transaction_detail;
	var $date;
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function purchaseMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
				'$this->company',
				'$this->financial_year',
				'$this->party',
				'$this->invoice_no',
				'$this->amount',
				'$this->cheque_cash',
				'$this->cheque_detail',
				'$this->transaction_detail',
				'$this->transaction_date')";
		// echo $sql; die();
		$this->db->insert($sql);
		$id=mysql_insert_id();
		return($id);
	} 			

	/* update the record in database */		
	function update()
	{
		$sql = "update `$this->tablename` set
						`party`='$this->party',
						`invoice_no`='$this->invoice_no',
						`amount`='$this->amount',
						`cheque_cash`='$this->cheque_cash',
						`cheque_detail`='$this->cheque_detail',
						`transaction_detail`='$this->transaction_detail',
						`date`='$this->transaction_date' 
						where `id`=$this->id";
		// echo $sql;die();
		$this->db->edit($sql);		
		return true;
	}	

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select *, date_format(`date`, '%d-%m-%Y') as trn_dt from `$this->tablename` where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectDateByParty()
	{
		$sql ="select date_format(`date`, '%d-%m-%Y') as tdate from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and financial_year = '".$_SESSION['fyear']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectSearchDateByParty($query)
	{
		$sql ="select date_format(`date`, '%d-%m-%Y') as tdate from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' ".$query;
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectSumByParty()
	{
		$sql ="select SUM(amount) as totamt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql; // die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectSumByPartyAll()
	{
		$sql ="select SUM(amount) as totamt from `$this->tablename` where party='$this->party'";
		// echo $sql; // die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
		/*Fectch record by id from Database*/		
	function selectSumByPartyBeforeDate()
	{
		$sql ="select SUM(amount) as totamt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and date < '$this->bdate'";
		// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	
	
	/*Fectch Details by party from Database*/		
	function selectDetByParty()
	{
		$sql ="select chb.id as chid, chb.party as party, cd.id as cdid, cd.cid as cid, (cd.sale_qty * cd.weight) * cd.amount as total_amount, cd.amount as amount from chitthibook_details cd  left join  chitthibook chb on chb.id = cd.cid where chb.party='$this->party'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	


	/*Fectch record by id from Database*/		
	function selectRecByPartyDate()
	{
		$sql ="select *, date_format(`date`, '%d-%m-%Y') as bdt from `$this->tablename` where party = '$this->party' and `date` like '%$this->tdt%'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";
		//echo $sql;die();
		mysql_query($sql);
	}	

	/*delete a record from database*/	
	function deleteByParty()
	{
		$sql="delete from `$this->tablename` 
			  where `party`='".$this->party."'";
		// echo $sql;die();
		mysql_query($sql);
	}			

	/*delete the selected record*/	
	function deleteSelect($chk) 
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql="delete from `$this->tablename` where `id` = '$id'";
			$res = mysql_query($sql);
		}
		return true;
	}	
					
	/*...paging...*/
	function paging($query)
	{			
		$pages = new Paging();
		// $pages->sql ="select *, date_format(`date`,'%d-%m-%Y') as tdt from `$this->tablename`";
			$pages->sql = "select prm.id as prid, date_format(prm.date,'%d-%m-%Y') as prdt, prm.invoice_no as prmin, prm.party as prmp, prm.amount as prmamt, pm.id as pid, pm.party_name as pmpn from `$this->tablename` prm left join party_master pm on prm.party=pm.id where prm.party!=''".$query." and company='".$_SESSION['company']."' order by pm.party_name";
			// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}

	/*...paging...*/
	function selectRecBySearch($query)
	{
		$sql ="select *, date_format(`date`,'%d-%m-%Y') as tdt from `$this->tablename` where party!=''".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*...paging...*/
	function pagingAccountStatus()
	{			
		$pages = new Paging();
		$pages->sql ="select party, Sum(amount) as amount from `cashbook` group by party";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}	
}		
?>   