<?php
global $db;
class cashMaster
{
	/* Variable Declaration */
	var $tablename='cashbook';
	var $id;
	var $party;
	var $credit_amount;
	var $debit_amount;
	var $transaction_type;
	var $cheque_detail;
	var $transaction_detail;
	var $transaction_date;
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function cashMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('0',
				'$this->company',
				'$this->financial_year',
				'$this->receipt_no',
				'$this->pay_type',
				'$this->party',
				'$this->currency',
				'$this->currency_inr',
				'$this->credit_amount',
				'$this->debit_amount',
				'$this->applicable_tax',
				'$this->tax_type',
				'$this->cgst_per',
				'$this->cgst_amt',
				'$this->sgst_per',
				'$this->sgst_amt',
				'$this->igst_per',
				'$this->igst_amt',
				'$this->total_amt',
				'$this->transaction_type',
				'$this->ref_no',
				'$this->cheque_no',
				'$this->cheque_date',
				'$this->cheque_detail',
				'$this->cheque_cleared',
				'$this->transaction_detail',
				'$this->transaction_date')";
		// echo $sql; die();
		$id=$this->db->insert($sql);
		return($id);
	} 			

	function insertInvDet() 
	{
		$sql = "insert into `bill_payment` values ('',
				'$this->party',
				'$this->bill_id',
				'$this->invoice_id',
				'$this->invoice_no',
				'$this->amount',
				now())";
		// echo $sql; die();
		$pid=$this->db->insert($sql);
		return($pid);
	}

	/* update the record in database */		
	function update()
	{
		$sql = "update `$this->tablename` set
						`pay_type`='$this->pay_type',
						`party`='$this->party',
						`currency`='$this->currency',
						`currency_inr`='$this->currency_inr',
						`credit_amount`='$this->credit_amount',
						`debit_amount`='$this->debit_amount',
						`applicable_tax`='$this->applicable_tax',
						`tax_type`='$this->tax_type',
						`cgst_per`='$this->cgst_per',
						`cgst_amt`='$this->cgst_amt',
						`sgst_per`='$this->sgst_per',
						`sgst_amt`='$this->sgst_amt',
						`igst_per`='$this->igst_per',
						`igst_amt`='$this->igst_amt',
						`total_amt`='$this->total_amt',
						`transaction_type`='$this->transaction_type',
						`ref_no`='$this->ref_no',
						`cheque_no`='$this->cheque_no',
						`cheque_date`='$this->cheque_date',
						`cheque_detail`='$this->cheque_detail',
						`cheque_cleared`='$this->cheque_cleared',
						`transaction_detail`='$this->transaction_detail',
						`transaction_date`='$this->transaction_date' 
						where `id`=$this->id";
		//echo "<br/><br/><br/><br/><br/>".$sql;die();
		$this->db->edit($sql);		
		return true;
	}	

	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		//echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select *, date_format(`transaction_date`, '%d-%m-%Y') as trn_dt, date_format(`cheque_date`, '%d-%m-%Y') as chq_dt from `$this->tablename` where id='$this->id'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*Fectch record by id from Database*/		
	function selectDateByParty()
	{
		$sql ="select date_format(`transaction_date`, '%d-%m-%Y') as tdate from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and financial_year = '".$_SESSION['fyear']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectSearchDateByParty($query)
	{
		$sql ="select date_format(`transaction_date`, '%d-%m-%Y') as tdate from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' ".$query;
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}


	/*Fectch record by id from Database*/		
	function selectRecByPartyDate()
	{
		$sql ="select *, date_format(`transaction_date`, '%d-%m-%Y') as bdt from `$this->tablename` where party = '$this->party' and `transaction_date` like '%$this->tdt%' and company = '".$_SESSION['company']."'";
		// echo $sql."<br/>"; //die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	
	
	/*Fectch Details by party from Database*/		
	function selectDetByParty()
	{
		$sql ="select chb.id as chid, chb.party as party, cd.id as cdid, cd.cid as cid, (cd.sale_qty * cd.weight) * cd.amount as total_amount, cd.amount as amount from chitthibook_details cd  left join  chitthibook chb on chb.id = cd.cid where chb.party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch Details by party from Database*/		
	function selectRecByParty()
	{
		$sql ="select * from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch Details by party from Database*/		
	function selectRecByPartyAll()
	{
		$sql ="select * from `$this->tablename` where party='$this->party'";
		//echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*Fectch Details by party from Database*/		
	function selectSumByParty()
	{
		$sql ="select *, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount, SUM(credit_amount * currency_inr) as tot_credit, SUM(debit_amount * currency_inr) as tot_debit from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	
	
		/*Fectch Details by party from Database*/		
	function selectSumByPartyBeforeDate()
	{
		$sql ="select *, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount  from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and transaction_date < '$this->bdate'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}		

	function selectExp()
	{	
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where pay_type = 'Exp' and company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."' order by transaction_date";
		$result=$this->db->select($sql);
		return($result);
	}


	function getInvoicePaidAmt($inv_id)
	{
		$sql ="select *, Sum(amount) as paidamt from `bill_payment` where invoice_id='$inv_id'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result[0]['paidamt']);
	}

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";
		$this->db->sql_query($sql);
	}	

	/*delete a record from database*/	
	function deleteByParty()
	{
		$sql="delete from `$this->tablename` 
			  where `party`='".$this->party."'";
		$this->db->sql_query($sql);
	}			


	/*delete a record from database*/	
	function deleteById()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";
	  	$this->db->sql_query($sql);
	}
	
	/* Fetch all the records */	
	function selectCreditAmount()
	{
		$sql = "SELECT SUM(credit_amount) FROM `$this->tablename` where financial_year = '".$_SESSION['fyear']."' and company = '".$_SESSION['company']."'";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	
	
	/*...paging...*/
	function pagingCollection()
	{	
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where credit_amount > 0 and company = '".$_SESSION['company']."' and MONTH(transaction_date)='$this->month' and financial_year='".$_SESSION['fyear']."' order by transaction_date";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 100;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	
	/*...paging...*/
	function pagingCollectionByQuery($query)
	{	
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where credit_amount > 0 and company = '".$_SESSION['company']."' and MONTH(transaction_date)='$this->month' and financial_year='".$_SESSION['fyear']."' $query order by transaction_date";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 1000;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	
	/*...paging...*/
	function pagingCollectionExp()
	{	
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where credit_amount > 0 and company = '".$_SESSION['company']."' and MONTH(transaction_date)='$this->month' and financial_year='".$_SESSION['fyear']."' and pay_type = 'Exp' order by transaction_date";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 100;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}		
	
	function printCollection($query)
	{	
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where credit_amount > 0 and company = '".$_SESSION['company']."' and MONTH(transaction_date)='$this->month' and financial_year='".$_SESSION['fyear']."' $query order by transaction_date";
		$result=$this->db->select($sql);
		return($result);
	}					
			
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";
						//echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*delete the selected record*/	
	function deleteSelect($chk) 
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql="delete from `$this->tablename` where `id` = '$id'";
			$this->db->sql_query($sql);
		}
		return true;
	}

	/*...paging...*/
	function paging()
	{	
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where pay_type != 'Exp' and company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."' order by transaction_date";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10000;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	
	/*...paging...*/
	function pagingExp()
	{	
		$pages = new Paging();
		$pages->sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where pay_type = 'Exp' and company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."' order by transaction_date";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 100;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}
	
	/*...paging...*/
	function selectReport()
	{	
		// $pages = new Paging();
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt from `$this->tablename` where credit_amount > 0 and company = '".$_SESSION['company']."' and financial_year='".$_SESSION['fyear']."' order by transaction_date";
		// echo $pages->sql; die;
		// $pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		// $pages->limit = 100;
		// $pages->GeneratePaging();
		// $this->pagination=$pages->pagination; 
		$result=$this->db->select($sql);
		return($result);
	}

	/*...paging...*/
	function selectRecBySearch($query)
	{
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt, date_format(`transaction_date`,'%m') as tm from `$this->tablename` where credit_amount > 0 and company = '".$_SESSION['company']."'".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*...paging...*/
	function selectRecBySearchExp($query)
	{
		$sql ="select *, date_format(`transaction_date`,'%d-%m-%Y') as tdt, date_format(`transaction_date`,'%m') as tm from `$this->tablename` where pay_type = 'Exp' and credit_amount > 0 and company = '".$_SESSION['company']."'".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*...paging...*/
	function pagingAccountStatus()
	{
		$pages = new Paging();
		$pages->sql ="select party, Sum(credit_amount) as credit_amount, Sum(debit_amount) as debit_amount from `cashbook` where company = '".$_SESSION['company']."' group by party";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 10;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}	
}		
?>   