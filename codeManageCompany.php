<?php
include("template.php");
function main()
{
	include("inc/clsObj.php");	
	
	$heading="Manage Company ";
	$pageName="codeManageCompany.php";
	
	extract($_POST);	
	$objCompany->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objCompany->company_name=$company_name;	
	$objCompany->invoice_header=$invoice_header;
	$objCompany->quotation_header=$quotation_header;
	$objCompany->quotation_signature=$quotation_signature;
	$objCompany->gstin=$gstin;
	$objCompany->pan_no=$pan_no;
	$objCompany->bank_details=$bank_details;
	$objCompany->terms_conditions_invoice=$terms_conditions_invoice;
	$objCompany->terms_conditions_quotation=$terms_conditions_quotation;
		
	$objCompany->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
	
				$objCompany->insert();	
				redirect("codeManageCompany.php");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objCompany->update();
		 redirect("codeManageCompany.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objCompany->deleteSelect($chkAction);
					break;
			case 1:
					$objCompany->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objCompany->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageCompany.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objCompany->delete();
		redirect("codeManageCompany.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objCompany->status();
		redirect("codeManageCompany.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objCompany->selectRecById();						
	}	
	$listRec=$objCompany->select();
    include("html/frmManageCompany.php");
 } 
?>