<?php
//Common Function
include("class.upload.php");
//function to encrypt the password
function encryptPassword($str)
{
  for($i=0; $i<5;$i++)
  {
    $str=strrev(base64_encode($str)); 
  }
  return $str;
}
//function to decrypt the password
function decryptPassword($str)
{
  for($i=0; $i<5;$i++)
  {
    $str=base64_decode(strrev($str));
  }
  return $str;
}  
//Function to uploadImage
function uploadImage($filename,$newFileName,$Path,$width="",$height="")
{	
	/*echo $filename."<br>";
	echo $newFileName."<br>";
	echo $Path."<br>";
	echo $width."<br>";
	echo $height."<br>";
	print_r($_FILES);
	echo "<hr>";
	die;*/
	
	 if($_FILES[$filename]['name']!="")
		{
		    $foo = new Upload($_FILES[$filename]); 
			if ($foo->uploaded) 
			{ 	
				$foo->file_new_name_body = $newFileName;
				if($width!="" or $height!="")
					{
						$foo->image_resize = true;
						if($width!="" and $height=="")
						 {
							$foo->image_x = $width;  
							$foo->image_ratio_y=true;
						  }
					    if($width=="" and $height!="")   
						   {
						  		$foo->image_y = $height; 
								$foo->image_ratio_x=true;								
						   }
						if($width!="" and $height!="")   
						   {
						   		$foo->image_y = $height;  
								$foo->image_x = $width;
						   }   	
					}
				$foo->Process($Path); 
				$newFileName=$foo->file_dst_name;
				//$foo->Clean(); 
			 }
		}	
	return $newFileName;  
}


//==========content images upload====================//
function uploadImages($cid,$file,$Path,$width="",$height="")
{	
		if($file['name']!="")
		{			
			$newFileName=createRandomCode();

			$foo = new Upload($file);
			if ($foo->uploaded) 
			{ 

				$foo->file_new_name_body=$newFileName;
				if($width!="" or $height!="")
				{
					$foo->image_resize = true;
					if($width!="" and $height=="")
					{
						$foo->image_x = $width;  
						$foo->image_ratio_y=true;
					}
					if($width=="" and $height!="")   
					{
						$foo->image_y = $height; 
						$foo->image_ratio_x=true;								
					}
					if($width!="" and $height!="")   
					{
						$foo->image_y = $height;  
						$foo->image_x = $width;
					}   	
				}
				$foo->Process($Path);  
				$newFileName=$foo->file_dst_name;
				$foo->Clean();
			}							
			return $newFileName;
		}
}
//==========end of upload content images===============

//Fuction to Upload file
function uploadFile($fileName,$newFileName,$path)
{
	if($_FILES[$fileName]['name']!="")
	{
		$foo = new Upload($_FILES[$fileName]); 
		if ($foo->uploaded) 
		{ 
			$foo->file_new_name_body =$newFileName;
			$foo->Process($path);  
			$newFileName=$foo->file_dst_name;
			$foo->Clean();
		}							
	}
	return  $newFileName;
}

//Function to Generate Random Code
function createRandomCode() {

    $chars = "abcdefghijkmnopqrstuvwxyz023456789";
    srand((double)microtime()*1000000);
    $i = 0;
    $pass = '' ;

    while ($i <= 7) {
        $num = rand() % 33;
        $tmp = substr($chars, $num, 1);
        $pass = $pass . $tmp;
        $i++;
    }

    return $pass;

}


//Function to get Delete Verification 
 function JscriptDeleteVerification($pageName){ ?>
<script language="javascript">
function verifyDelete(id){
if(confirm("Do you want to delete ?\n\n>> Click OK to delete this.[Enter Key]\n>> Click Cancel to Abort the deletion.[Esc Key]")){
document.location.href="<?=$pageName?>?delete="+id;
}					
}
</script>
<? } 

//Funcition to Redirect the page of particular url
function redirect($url)
{
  ?><script>document.location="<?=$url;?>"</script><? 
}
// FUNCTION FOR CHECKING THE VARAIBLE IS SET OR NOT (REQUEST  OR SESSION )
function issetvar($val="",$fixkept="")
{
	if(isset($_REQUEST[$val]))	{
		return $_REQUEST[$val];
	}else{
		if(isset($_SESSION[$val])){
			return $_SESSION[$val];
		}else{	
			if(trim($fixkept)!=""){
				return $fixkept;
			}else{
				return false;
			}
		}
	}
}
function get_date($date)
{
if($date=="0000-00-00")
return "";
if ($dateSeparator == "") { $dateSeparator = '[:/.\ \-]';}
$ddmmyyyy[] = explode($dateSeparator,$date);
$dd=$ddmmyyyy[0][1];
$mm=$ddmmyyyy[0][0];
$yyyy=$ddmmyyyy[0][2];
return ($yyyy."-".$mm."-".$dd);
}


// FUNCTION FOR CHECKING  THAT ANY MAGIC QUOTES ARE REPLACED IN STRING
function mymagictxt($theValue)
{
	$theValue = (!get_magic_quotes_gpc()) ? addslashes($theValue) : $theValue;	
	$theValue= str_replace("%<%","&lt;",$theValue);
	$theValue= str_replace("%>%","&gt;",$theValue);
	return $theValue;
}

// FUNCTION FOR CHECKING ACTIONS OF FORM
function actionfrmcheck($var="",$val="")
{
	//echo $_REQUEST[$var]."$var-->$val";
	if( isset($_REQUEST[$var])  && ($_REQUEST[$var]!="") &&  ($_REQUEST[$var]==$val) )
	{
		return true;
	}
	else
	{
		return false;
	}
}

// FUNCTION FOR CHECKING ACTIONS OF FORM
function actionfrmcheckrm($var="")
{
	//echo $_REQUEST[$var]."$var-->$val";
	if( isset($_REQUEST[$var])  && ($_REQUEST[$var]!=""))
	{
		return true;
	}
	else
	{
		return false;
	}
}

//FUNCTION FOR THE CRYPT THE GIVEN PASSWORD INTO CRYPT PASSWORD WITH SERVER NAME
function cyptmypass($pass)
{
	$servername=$_SERVER['HTTP_HOST'];
	$pass=crypt($pass,$servername);
	return $pass;
}

function createRandomPassword() {

    $chars = "abcdefghijkmnopqrstuvwxyz023456789";
    srand((double)microtime()*1000000);
    $i = 0;
    $pass = '' ;

    while ($i <= 7) {
        $num = rand() % 33;
        $tmp = substr($chars, $num, 1);
        $pass = $pass . $tmp;
        $i++;
    }

    return $pass;

}

function createActivationKey() {

    $chars = "abcdefghijkmnopqrstuvwxyz023456789";
    srand((double)microtime()*1000000);
    $i = 0;
    $pass = '' ;

    while ($i <= 10) {
        $num = rand() % 33;
        $tmp = substr($chars, $num, 1);
        $pass = $pass . $tmp;
        $i++;
    }
	return $pass;

}

//////////////Add slash to array()/////////////////////

function addslashes_array($a){
        if(is_array($a)){
            foreach($a as $n=>$v){
                $b[$n]=addslashes_array($v);
            }
            return $b;
        }else{
            return addslashes($a);
        }
    }


////function for checking user is logged in or not ////
function checkLogin(){
	if(!isset($_SESSION['uid'])){?>
		<script type="text/javascript">
			alert("Please Login by entering 'Login Name' & 'Password'.");
			document.location="index.php";
		</script>
	<? } 
}

//======convert string into proper case==========
function ProperCase($words){
	$words = explode(" ", $words);
	for ($i=0; $i<count($words); $i++) {
		$s = strtolower($words[$i]);
		$s = substr_replace($s, strtoupper(substr($s, 0, 1)), 0, 1);
		$result .=$s." " ;
	}
	$string = trim($result);
	return $string;
}

function getPageName(){
	$page_name = explode("/",$_SERVER['PHP_SELF']);
	$pn = count($page_name)-1;
	$page = $page_name[$pn];
	return $page;
}

?>