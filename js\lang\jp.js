Calendar.LANG("jp", "Japanese", {

        fdow: 1,                // 地元の週の初めの日; 0 = 日曜日, 1 = 月曜日, 等.

        goToday: "本日へ",

        today: "本日",         // ボットンバーに表示

        wk: "週",

        weekend: "0,6",         // 0 = 日曜日, 1 = 月曜日, 等.

        AM: "am",

        PM: "pm",

        mn : [ "1月",
               "2月",
               "3月",
               "4月",
               "5月",
               "6月",
               "7月",
               "8月",
               "9月",
               "10月",
               "11月",
               "12月" ],

        smn : [ "1月",
               	"2月",
               	"3月",
               	"4月",
               	"5月",
               	"6月",
               	"7月",
               	"8月",
               	"9月",
               	"10月",
               	"11月",
               	"12月" ],

        dn : [ "日曜日",
               "月曜日",
               "火曜日",
               "水曜日",
               "木曜日",
               "金曜日",
               "土曜日",
               "日曜日" ],

        sdn : [ "日",
                "月",
                "火",
                "水",
                "木",
                "金",
                "土",
                "日" ]

});
