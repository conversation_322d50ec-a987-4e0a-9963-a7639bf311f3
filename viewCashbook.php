<? include("template.php");
function main() { 
include("inc/clsObj.php");
$heading="View Payment";		
$pageName="viewCashbook.php";	

$objCash->id=$_GET['id'];
$listEdit=$objCash->selectRecById();

$objCash->cid = $listEdit[0]['id'];
$listItems=$objCash->selectRecById();
//echo count($listItems);

function no_to_words($no){
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
}
?>
<style>
	.terms_conditions ul li {font-size:12px!important;}
	.text-left {text-align:left !important;}
	.text-right {text-align:right !important;}
	.width4 {width:4% !important;}
	.width5 {width:5% !important;}
	.width8 {width:8% !important;}
	.width25 {width:25% !important;}
	.width27 {width:27% !important;}
	.width34 {width:34% !important;}
	.width100 {width:100% !important;}
	.bdr1 {border:#999 1px solid;}
	.bdr1right {border-right:#999 1px solid;}
	.bdrcoll {border-collapse:collapse;}
	.bdrclr999999 {border-color:#999999 !important;}
</style>
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-12">
								<h5>Manage Invoice</h5>
                                <a href="codeManageCashbook.php" class="btn btn-success btn-sm btn-round has-ripple f-right">
											<i class="fas fa-angle-left"></i> Back
										</a>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<!--========== Payment Details ==========-->
						<div class="width100">
							<div class="width100" style="margin:0 auto;">
								<table class="width100 mb-20 table-responsive" border="0" cellpadding="0" cellspacing="0">
									<tbody>
										<tr>
											<td>
												<table class="width100 bdrcoll bdr1" border="1" cellspacing="0" cellpadding="5">
													<tbody>
														<tr class="toprow">
															<td align="center" valign="top"> 
																<strong class="f-20"><?=strtoupper($_SESSION['company']);?></strong>
																<br>
																<p class="mb-5">
																	<?=$objCompany->getInvoiceHeaderByCompany($listEdit[0]['company']);?>
																</p>
																<strong>PAN No. : </strong><?=$objCompany->getPannoByCompany($listEdit[0]['company']);?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
																<strong>GSTIN : </strong><?=$objCompany->getGstinByCompany($listEdit[0]['company']);?>
															</td>
														</tr>
														<tr>
															<td class="text-center f-15 f-bold" valign="middle">
																Payment Receipt
															</td>
														</tr>
														<tr>
															<td>
																<table class="width100">
																	<tbody>
																		<tr>
																			<td align="left">
																				<strong>Receipt No. <?=$listEdit[0]['receipt_no'];?></strong>
																			</td>
																			<td align="right">
																				<strong>Date: - <?=$listEdit[0]['trn_dt'];?></strong>
																			</td>
																		</tr>
																		<tr>
																			<td colspan="2" style="line-height:22px;">
                                                                            <? $objParty->id=$listEdit[0]['party'];
       $partydet=$objParty->selectRecById(); 
	
	if($listEdit[0]['credit_amount'] > 0){
		$amount = $listEdit[0]['credit_amount'] + $listEdit[0]['cgst_amt'] + $listEdit[0]['sgst_amt'] + $listEdit[0]['igst_amt'];
		$credit_debit = 0;
	}
	elseif($listEdit[0]['debit_amount'] > 0){
		$amount = $listEdit[0]['debit_amount'] + $listEdit[0]['cgst_amt'] + $listEdit[0]['sgst_amt'] + $listEdit[0]['igst_amt'];
		$credit_debit = 1;
	}
	
	if($listEdit[0]['transaction_type']=="Cash")
		$payment_msg = "Cash dated : ".$listEdit[0]['trn_dt'];
	elseif($listEdit[0]['transaction_type']=="Cheque")
		$payment_msg = "Cheque No. : ".$listEdit[0]['cheque_no']." dated ".$listEdit[0]['chq_dt']; 
	else
		$payment_msg = "Payment with ".$listEdit[0]['transaction_type']." dated : ".$listEdit[0]['trn_dt'];
?>	
                                                                            
																				<p class="f-13 mt-10">
																					Received with thanks from <strong><u><?=$partydet[0]['party_name'];?></u></strong> as sum of Rs. <?=$amount;?> (Rupees <?=ucwords(no_to_words($amount));?> Only) as an Payment for "<?=$listEdit[0]['transaction_detail'];?>" <?=($listEdit[0]['transaction_type']=="Cheque") ? "through  ".$listEdit[0]['cheque_detail'] : "";?> vide <?=$payment_msg;?> favoring M/s. <?=strtoupper($_SESSION['company']);?>
																				</p>										
																				<p>
																					<strong>Rs. <?=$amount;?><br>
																					Amount in words : Rupees <?=ucwords(no_to_words($amount));?> Only</strong>
																				</p>
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
										<tr>
											<td>
												<table class="bdr1 width100 bdrcoll" cellspacing="0" cellpadding="5">
													<tbody>
														<tr>
															<td width="226" valign="top" align="right" ;="">
																<p align="right">For, ERP Demo</p>
																<br>
																<br>
																<br>
																<p align="right">AUTHORISED SIGNATORY</p>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="text-center">
								<a href="codeManageCashbook.php?id=<?=$_GET['id'];?>" target="_blank" class="btn btn-info btn-sm has-ripple">Edit Invoice</a>
								<a href="print_receipt.php?id=<?=$_GET['id'];?>" target="_blank" class="btn btn-info btn-sm has-ripple">Print Receipt</a>
								<a href="print_receipt_letterhead.php?id=<?=$_GET['id'];?>" target="_blank" class="btn btn-info btn-sm has-ripple">Print Receipt Letterhead</a>
							</div>
						</div>
						<!--========== Payment Details ==========-->
						
                    </div>
                </div>
            </div>
        </div>
		
		
		
	</div>
</div>


<? } ?>