<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Admin</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5><?=$heading;?></h5>
							</div>
							<div class="col-md-6">
								<? if(isset($_REQUEST['btnAddUser'])){ ?>
									<form action="<?=$pageName;?>" method="post">
										<button type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack">
											<i class="fas fa-angle-left"></i> Back
										</button>
									</form>
								<? }
								else{?>
									<form action="<?=$pageName;?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add <?=$heading;?>
										</button>
									</form>									
								<? } ?>
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						<? if(isset($_REQUEST['btnAddUser'])){ ?>
						<!--========== Add Admin Menu ==========-->
						<form>
                            <div class="form-row">
								<div class="form-group col-md-4">
                                    <label>Menu Position</label>
                                    <select id="" class="form-control">
                                        <option selected>-- Main Position --</option>
										<option value="Dashboard">Dashboard</option>
										<option value="Admin">Admin</option>
										<option value="Master">Master</option>
										<option value="Invoice">Invoice</option>
										<option value="Export">Export</option>
										<option value="Payment">Payment</option>
										<option value="TDS Entry">TDS Entry</option>
										<option value="Followups">Followups</option>
										<option value="Reports">Reports</option>
										<option value="Database Backup">Database Backup</option>
                                    </select>
                                </div>
                                <div class="form-group col-md-4">
                                    <label>Menu Name</label>
                                    <input type="text" id="" name="" class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                    <label>Sequence No.</label>
                                    <input type="text" id="" name="" class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label>Link</label>
                                    <input type="text" id="" name="" class="form-control">
                                </div>
								<div class="form-group col-md-12">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" id="">
										<label class="form-check-label">Display only Admin</label>
									</div>
								</div>
                            </div>
                            <button type="submit" class="btn btn-success">Add Menu</button>
                        </form>
						<!--========== Add Admin Menu ==========-->
						
						
						<? }
						else {
						?>
						
						<!--========== List View ==========-->
						<div class="dt-responsive table-responsive">
							<table id="simpletable" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th colspan="6" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													<option>-- Select Action --</option>
													<option value="Delete">Delete</option>
													<option value="Archive">Archive</option>
													<option value="Inactive">Inactive</option>
												</select>
											</label>
											<button class="btn btn-success btn-sm btn-round has-ripple f-right">
												Submit
											</button>
										</th>											
									</tr>
									<tr>
										<th>Id.</th>
										<th>Seq. No.</th>
										<th>Menu</th>
										<th>
											<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"> Action
										</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>1</td>
										<td>
											<input type="text" size="5" name="" id="" value="1">
										</td>
										<td><a href="#">Dashboard</a></td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">

											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
										</td>
									</tr>
									<tr>
										<td>2</td>
										<td>
											<input type="text" size="5" name="" id="" value="2">
										</td>
										<td><a href="#">Admin</a></a></td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">

											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
										</td>
									</tr>
									<tr>
										<td>3</td>
										<td>
											<input type="text" size="5" name="" id="" value="3">
										</td>
										<td><a href="#" class="pl-30">Manage user</a></td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">

											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
										</td>
									</tr>
									<tr>
										<td>4</td>
										<td>
											<input type="text" size="5" name="" id="" value="4">
										</td>
										<td><a href="#" class="pl-30">Change Password</a></td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">

											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
										</td>
									</tr>
									<tr>
										<td>5</td>
										<td>
											<input type="text" size="5" name="" id="" value="5">
										</td>
										<td><a href="#" class="pl-30">Admin Menu</a></td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">

											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
										</td>
									</tr>
									<tr>
										<td>6</td>
										<td>
											<input type="text" size="5" name="" id="" value="6">
										</td>
										<td><a href="#">Master</a></td>
										<td class="table-action">
											<input type="checkbox" class="v-align-middle mr-10">

											<a href="#!">
												<i class="fas fa-check mr-10 clr-green"></i>
												<!--<i class="fas fa-minus mr-10 clr-red"></i>-->
											</a>
											<a href="#!">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
											<a href="#!">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<!--========== List View ==========-->
						
						<? } ?>
						
                    </div>
                </div>
            </div>
        </div>

		

	</div>
</div>