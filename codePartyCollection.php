<?php 
include("template.php");
function main()
{
	$heading="<span>Manage</span> Collection ";
    include("inc/clsObj.php");	
	
	extract($_POST);	
	$cdate = explode("-",$Transaction_Date);
	$transaction_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];

	$objCash->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objCash->company=$_SESSION['company'];
	$objCash->financial_year=$_SESSION['fyear'];
	$objCash->party=$Party_Name;
	if($Credit_Debit==0)
	{
		$objCash->credit_amount=$Amount;
		$objCash->debit_amount=0;
	}
	elseif($Credit_Debit==1)
	{
		$objCash->credit_amount=0;
		$objCash->debit_amount=$Amount;
	}
	$objCash->cheque_cash=$Cash_Cheque;
	$objCash->cheque_no=$Cheque_No;
	$objCash->cheque_detail=$Cheque_Detail;
	$objCash->transaction_detail=$Transaction_Detail;
	$objCash->transaction_date=$transaction_date;
	
	if(isset($_POST['btnAdd']))
	{
		$objCash->insert();
		redirect("codeManageCashbook.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objCash->update();
		redirect("codeManageCashbook.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objCash->deleteSelect($chkAction);
				break;
			case 1:
				$objCash->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objCash->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
					
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManageCashbook.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objCash->deleteById();
		$objCash->deleteItemsById();
		redirect("codeManageCashbook.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objCash->status();
		redirect("codeManageCashbook.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objCash->selectRecById();

		$objCash->cid = $listEdit[0]['id'];
		$listItems=$objCash->selectRecById();	
	}
	elseif($_GET['Party_Name']!="" || ($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="__-__-____" && $_GET['To_Date']!="__-__-____"))
	{
		$query = "";
		if($_GET['Party_Name']!="")
			$query.=" and party='".$_GET['Party_Name']."'";
		if($_GET['From_Date']!="" && $_GET['To_Date']!="" && $_GET['From_Date']!="__-__-____" && $_GET['To_Date']!="__-__-____")
		{
			$fdate = explode("-",$_GET['From_Date']);
			$fdate=$fdate[2]."-".$fdate[1]."-".$fdate[0];
			

			$tdate = explode("-",$_GET['To_Date']);
			$tdate=$tdate[2]."-".$tdate[1]."-".$tdate[0];
			$query.=" and `transaction_date` between '".$fdate."' and '".$tdate."'";
		}
		else
			$query.=" and financial_year='".$_SESSION['fyear']."'";
		
		// $objCash->party = $_GET['party'];
		$listRec=$objCash->selectRecBySearch($query);
    }
	//else
		//$listRec=$objCash->pagingCollection();
	include("html/frmPartyCollection.php");
} 
?>
