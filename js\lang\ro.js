Calendar.LANG("ro", "<PERSON><PERSON><PERSON><PERSON>", {

        fdow: 1,                // first day of week for this locale; 0 = Sunday, 1 = Monday, etc.

        goToday: "<PERSON><PERSON><PERSON><PERSON>",

        today: "<PERSON><PERSON><PERSON><PERSON>",         // appears in bottom bar

        wk: "săp.",

        weekend: "0,6",         // 0 = Sunday, 1 = Monday, etc.

        AM: "am",

        PM: "pm",

        mn : [ "<PERSON><PERSON><PERSON>",
               "<PERSON><PERSON><PERSON><PERSON>",
               "<PERSON><PERSON>",
               "<PERSON><PERSON>",
               "<PERSON>",
               "<PERSON><PERSON><PERSON>",
               "<PERSON><PERSON><PERSON>",
               "August",
               "Septembrie",
               "<PERSON><PERSON><PERSON>",
               "<PERSON>ie<PERSON><PERSON>",
               "Decembrie" ],

        smn : [ "<PERSON>",
                "Feb",
                "<PERSON>",
                "Apr",
                "<PERSON>",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "Aug",
                "Sep",
                "Oct",
                "Noi",
                "Dec" ],

        dn : [ "<PERSON><PERSON><PERSON><PERSON>",
               "<PERSON><PERSON>",
               "<PERSON><PERSON><PERSON>",
               "<PERSON><PERSON><PERSON><PERSON>",
               "<PERSON><PERSON>",
               "<PERSON><PERSON>",
               "S<PERSON>mb<PERSON>t<PERSON>",
               "<PERSON><PERSON><PERSON><PERSON>" ],

        sdn : [ "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "<PERSON>" ]

});
