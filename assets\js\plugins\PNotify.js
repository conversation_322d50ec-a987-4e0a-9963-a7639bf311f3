var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };

var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; };

/* src/PNotify.html generated by Svelte v2.16.1 */
(function (global, factory) {
  (typeof exports === "undefined" ? "undefined" : _typeof(exports)) === "object" && typeof module !== "undefined" ? module.exports = factory() : typeof define === "function" && define.amd ? define('PNotify', factory) : global.PNotify = factory();
})(this, function () {
  "use strict";

  var PNotify = void 0;

  var posTimer = void 0; // Position all timer.

  // These actions need to be done once the DOM is ready.
  var onDocumentLoaded = function onDocumentLoaded() {
    PNotify.defaultStack.context = document.body;
    // Reposition the notices when the window resizes.
    window.addEventListener('resize', function () {
      if (posTimer) {
        clearTimeout(posTimer);
      }
      posTimer = setTimeout(function () {
        PNotify.positionAll();
      }, 10);
    });
  };

  // Creates the background overlay for modal stacks.
  var createStackOverlay = function createStackOverlay(stack) {
    var overlay = document.createElement('div');
    overlay.classList.add('ui-pnotify-modal-overlay');
    if (stack.context !== document.body) {
      overlay.style.height = stack.context.scrollHeight + 'px';
      overlay.style.width = stack.context.scrollWidth + 'px';
    }
    // Close the notices on overlay click.
    overlay.addEventListener('click', function () {
      if (stack.overlayClose) {
        PNotify.closeStack(stack);
      }
    });
    stack.overlay = overlay;
  };

  var insertStackOverlay = function insertStackOverlay(stack) {
    if (stack.overlay.parentNode !== stack.context) {
      stack.overlay = stack.context.insertBefore(stack.overlay, stack.context.firstChild);
    }
  };

  var removeStackOverlay = function removeStackOverlay(stack) {
    if (stack.overlay.parentNode) {
      stack.overlay.parentNode.removeChild(stack.overlay);
    }
  };

  // Default arguments for the new notice helper functions.
  var getDefaultArgs = function getDefaultArgs(options, type) {
    if ((typeof options === "undefined" ? "undefined" : _typeof(options)) !== 'object') {
      options = { 'text': options };
    }

    // Only assign the type if it was requested, so we don't overwrite
    // options.type if it has something assigned.
    if (type) {
      options.type = type;
    }

    return { target: document.body, data: options };
  };

  function _styles(_ref) {
    var styling = _ref.styling;

    return (typeof styling === "undefined" ? "undefined" : _typeof(styling)) === 'object' ? styling : PNotify.styling[styling];
  }

  function _icons(_ref2) {
    var icons = _ref2.icons;

    return (typeof icons === "undefined" ? "undefined" : _typeof(icons)) === 'object' ? icons : PNotify.icons[icons];
  }

  function _widthStyle(_ref3) {
    var width = _ref3.width;

    return typeof width === 'string' ? 'width: ' + width + ';' : '';
  }

  function _minHeightStyle(_ref4) {
    var minHeight = _ref4.minHeight;

    return typeof minHeight === 'string' ? 'min-height: ' + minHeight + ';' : '';
  }

  function data() {
    var data = _extends({
      '_state': 'initializing', // The state can be 'initializing', 'opening', 'open', 'closing', and 'closed'.
      '_timer': null, // Auto close timer.
      '_animTimer': null, // Animation timer.
      '_animating': false, // Stores what is currently being animated (in or out).
      '_animatingClass': '', // Stores the class that adds entry/exit animation effects.
      '_moveClass': '', // Stores the class that adds movement animation effects.
      '_timerHide': false, // Stores whether the notice was hidden by a timer.
      '_moduleClasses': [], // Modules can add classes here to be added to the notice element. (They should play nice and not remove classes that aren't theirs.)
      '_moduleIsNoticeOpen': false, // Modules that change how the notice displays (causing the notice element to not appear) can set this to true to make PNotify assume the notice has opened.
      '_modules': {}, // Stores the instances of the modules.
      '_modulesPrependContainer': PNotify.modulesPrependContainer,
      '_modulesAppendContainer': PNotify.modulesAppendContainer
    }, PNotify.defaults);
    data.modules = _extends({}, PNotify.defaults.modules);
    return data;
  };

  var methods = {
    // This runs an event on all the modules.
    runModules: function runModules(event) {
      if (event === 'init') {
        // Initializing a module should only be done if it has an init
        // function, which means it's not rendered in the template.
        for (var key in PNotify.modules) {
          if (!PNotify.modules.hasOwnProperty(key)) {
            continue;
          }
          if (typeof PNotify.modules[key].init === 'function') {
            var _module = PNotify.modules[key].init(this);
            this.initModule(_module);
          }
        }
      } else {
        var _get = this.get(),
            _modules = _get._modules;

        for (var _module2 in _modules) {
          if (!_modules.hasOwnProperty(_module2)) {
            continue;
          }
          var moduleOptions = _extends({
            '_notice': this,
            '_options': this.get()
          }, this.get().modules[_module2]);
          _modules[_module2].set(moduleOptions);
          if (typeof _modules[_module2][event] === 'function') {
            _modules[_module2][event]();
          }
        }
      }
    },


    // This passes module options to a module.
    initModule: function initModule(module) {
      var _get2 = this.get(),
          modules = _get2.modules;

      if (!modules.hasOwnProperty(module.constructor.key)) {
        modules[module.constructor.key] = {};
      }
      var moduleOptions = _extends({
        '_notice': this,
        '_options': this.get()
      }, modules[module.constructor.key]);
      module.initModule(moduleOptions);

      // Now save the module instance.

      var _get3 = this.get(),
          _modules = _get3._modules;

      _modules[module.constructor.key] = module;
    },
    update: function update(options) {
      // Save old options.
      var oldHide = this.get().hide;
      var oldIcon = this.get().icon;

      this.set(options);

      // Run the modules.
      this.runModules('update');

      // Update the timed hiding.
      if (!this.get().hide) {
        this.cancelClose();
      } else if (!oldHide) {
        this.queueClose();
      }
      this.queuePosition();

      // Font Awesome 5 replaces our lovely element with a gross SVG. In order
      // to make it play nice with Svelte, we have to clear the element and
      // make it again.

      var _get4 = this.get(),
          icon = _get4.icon;

      if (icon !== oldIcon && (icon === true && this.get().icons === 'fontawesome5' || typeof icon === 'string' && icon.match(/(^| )fa[srlb]($| )/))) {
        this.set({ 'icon': false });
        this.set({ 'icon': icon });
      }

      return this;
    },


    // Display the notice.
    open: function open() {
      var _this = this;

      var _get5 = this.get(),
          _state = _get5._state,
          hide = _get5.hide;

      if (_state === 'opening') {
        return;
      }
      if (_state === 'open') {
        if (hide) {
          this.queueClose();
        }
        return;
      }
      this.set({
        '_state': 'opening',
        // This makes the notice visibity: hidden; so its dimensions can be
        // determined.
        '_animatingClass': 'ui-pnotify-initial-hidden'
      });
      // Run the modules.
      this.runModules('beforeOpen');

      var _get6 = this.get(),
          stack = _get6.stack;
      // If the notice is not in the DOM, or in the wrong context, append it.


      if (!this.refs.elem.parentNode || stack && stack.context && stack.context !== this.refs.elem.parentNode) {
        if (stack && stack.context) {
          stack.context.appendChild(this.refs.elem);
        } else if (document.body) {
          document.body.appendChild(this.refs.elem);
        } else {
          throw new Error('No context to open this notice in.');
        }
      }

      // Wait until the DOM is updated.
      setTimeout(function () {
        if (stack) {
          // Mark the stack so it won't animate the new notice.
          stack.animation = false;
          // Now position all the notices.
          PNotify.positionAll();
          // Reset animation.
          stack.animation = true;
        }

        _this.animateIn(function () {
          // Now set it to hide.
          if (_this.get().hide) {
            _this.queueClose();
          }

          _this.set({ '_state': 'open' });

          // Run the modules.
          _this.runModules('afterOpen');
        });
      }, 0);

      return this;
    },
    remove: function remove(timerHide) {
      return this.close(timerHide);
    },


    // Remove the notice.
    close: function close(timerHide) {
      var _this2 = this;

      var _get7 = this.get(),
          _state = _get7._state;

      if (_state === 'closing' || _state === 'closed') {
        return;
      }
      this.set({ '_state': 'closing', '_timerHide': !!timerHide }); // Make sure it's a boolean.
      // Run the modules.
      this.runModules('beforeClose');

      var _get8 = this.get(),
          _timer = _get8._timer;

      if (_timer && clearTimeout) {
        clearTimeout(_timer);
        this.set({ '_timer': null });
      }
      this.animateOut(function () {
        _this2.set({ '_state': 'closed' });
        // Run the modules.
        _this2.runModules('afterClose');
        _this2.queuePosition();
        // If we're supposed to remove the notice from the DOM, do it.
        if (_this2.get().remove) {
          _this2.refs.elem.parentNode.removeChild(_this2.refs.elem);
        }
        // Run the modules.
        _this2.runModules('beforeDestroy');
        // Remove object from PNotify.notices to prevent memory leak (issue #49)
        // unless destroy is off
        if (_this2.get().destroy) {
          if (PNotify.notices !== null) {
            var idx = PNotify.notices.indexOf(_this2);
            if (idx !== -1) {
              PNotify.notices.splice(idx, 1);
            }
          }
        }
        // Run the modules.
        _this2.runModules('afterDestroy');
      });

      return this;
    },


    // Animate the notice in.
    animateIn: function animateIn(callback) {
      var _this3 = this;

      // Declare that the notice is animating in.
      this.set({ '_animating': 'in' });
      var finished = function finished() {
        _this3.refs.elem.removeEventListener('transitionend', finished);

        var _get9 = _this3.get(),
            _animTimer = _get9._animTimer,
            _animating = _get9._animating,
            _moduleIsNoticeOpen = _get9._moduleIsNoticeOpen;

        if (_animTimer) {
          clearTimeout(_animTimer);
        }
        if (_animating !== 'in') {
          return;
        }
        var visible = _moduleIsNoticeOpen;
        if (!visible) {
          var domRect = _this3.refs.elem.getBoundingClientRect();
          for (var prop in domRect) {
            if (domRect[prop] > 0) {
              visible = true;
              break;
            }
          }
        }
        if (visible) {
          if (callback) {
            callback.call();
          }
          // Declare that the notice has completed animating.
          _this3.set({ '_animating': false });
        } else {
          _this3.set({ '_animTimer': setTimeout(finished, 40) });
        }
      };

      if (this.get().animation === 'fade') {
        this.refs.elem.addEventListener('transitionend', finished);
        this.set({ '_animatingClass': 'ui-pnotify-in' });
        // eslint-disable-next-line no-unused-expressions
        this.refs.elem.style.opacity; // This line is necessary for some reason. Some notices don't fade without it.
        this.set({ '_animatingClass': 'ui-pnotify-in ui-pnotify-fade-in' });
        // Just in case the event doesn't fire, call it after 650 ms.
        this.set({ '_animTimer': setTimeout(finished, 650) });
      } else {
        this.set({ '_animatingClass': 'ui-pnotify-in' });
        finished();
      }
    },


    // Animate the notice out.
    animateOut: function animateOut(callback) {
      var _this4 = this;

      // Declare that the notice is animating out.
      this.set({ '_animating': 'out' });
      var finished = function finished() {
        _this4.refs.elem.removeEventListener('transitionend', finished);

        var _get10 = _this4.get(),
            _animTimer = _get10._animTimer,
            _animating = _get10._animating,
            _moduleIsNoticeOpen = _get10._moduleIsNoticeOpen;

        if (_animTimer) {
          clearTimeout(_animTimer);
        }
        if (_animating !== 'out') {
          return;
        }
        var visible = _moduleIsNoticeOpen;
        if (!visible) {
          var domRect = _this4.refs.elem.getBoundingClientRect();
          for (var prop in domRect) {
            if (domRect[prop] > 0) {
              visible = true;
              break;
            }
          }
        }
        if (!_this4.refs.elem.style.opacity || _this4.refs.elem.style.opacity === '0' || !visible) {
          _this4.set({ '_animatingClass': '' });

          var _get11 = _this4.get(),
              stack = _get11.stack;

          if (stack && stack.overlay) {
            // Go through the modal stack to see if any are left open.
            // TODO: Rewrite this cause it sucks.
            var stillOpen = false;
            for (var i = 0; i < PNotify.notices.length; i++) {
              var notice = PNotify.notices[i];
              if (notice !== _this4 && notice.get().stack === stack && notice.get()._state !== 'closed') {
                stillOpen = true;
                break;
              }
            }
            if (!stillOpen) {
              removeStackOverlay(stack);
            }
          }
          if (callback) {
            callback.call();
          }
          // Declare that the notice has completed animating.
          _this4.set({ '_animating': false });
        } else {
          // In case this was called before the notice finished animating.
          _this4.set({ '_animTimer': setTimeout(finished, 40) });
        }
      };

      if (this.get().animation === 'fade') {
        this.refs.elem.addEventListener('transitionend', finished);
        this.set({ '_animatingClass': 'ui-pnotify-in' });
        // Just in case the event doesn't fire, call it after 650 ms.
        this.set({ '_animTimer': setTimeout(finished, 650) });
      } else {
        this.set({ '_animatingClass': '' });
        finished();
      }
    },


    // Position the notice.
    position: function position() {
      // Get the notice's stack.
      var _get12 = this.get(),
          stack = _get12.stack;

      var elem = this.refs.elem;
      if (!stack) {
        return;
      }
      if (!stack.context) {
        stack.context = document.body;
      }
      if (typeof stack.nextpos1 !== 'number') {
        stack.nextpos1 = stack.firstpos1;
      }
      if (typeof stack.nextpos2 !== 'number') {
        stack.nextpos2 = stack.firstpos2;
      }
      if (typeof stack.addpos2 !== 'number') {
        stack.addpos2 = 0;
      }

      // Skip this notice if it's not shown.
      if (!elem.classList.contains('ui-pnotify-in') && !elem.classList.contains('ui-pnotify-initial-hidden')) {
        return this;
      }

      if (stack.modal) {
        if (!stack.overlay) {
          createStackOverlay(stack);
        }
        insertStackOverlay(stack);
      }

      // Read from the DOM to cause refresh.
      elem.getBoundingClientRect();

      if (stack.animation) {
        // Add animate class.
        this.set({ '_moveClass': 'ui-pnotify-move' });
      }

      var spaceY = stack.context === document.body ? window.innerHeight : stack.context.scrollHeight;
      var spaceX = stack.context === document.body ? window.innerWidth : stack.context.scrollWidth;

      var csspos1 = void 0;

      if (stack.dir1) {
        csspos1 = {
          'down': 'top',
          'up': 'bottom',
          'left': 'right',
          'right': 'left'
        }[stack.dir1];

        // Calculate the current pos1 value.
        var curpos1 = void 0;
        switch (stack.dir1) {
          case 'down':
            curpos1 = elem.offsetTop;
            break;
          case 'up':
            curpos1 = spaceY - elem.scrollHeight - elem.offsetTop;
            break;
          case 'left':
            curpos1 = spaceX - elem.scrollWidth - elem.offsetLeft;
            break;
          case 'right':
            curpos1 = elem.offsetLeft;
            break;
        }
        // Remember the first pos1, so the first notice goes there.
        if (typeof stack.firstpos1 === 'undefined') {
          stack.firstpos1 = curpos1;
          stack.nextpos1 = stack.firstpos1;
        }
      }

      if (stack.dir1 && stack.dir2) {
        var csspos2 = {
          'down': 'top',
          'up': 'bottom',
          'left': 'right',
          'right': 'left'
        }[stack.dir2];

        // Calculate the current pos2 value.
        var curpos2 = void 0;
        switch (stack.dir2) {
          case 'down':
            curpos2 = elem.offsetTop;
            break;
          case 'up':
            curpos2 = spaceY - elem.scrollHeight - elem.offsetTop;
            break;
          case 'left':
            curpos2 = spaceX - elem.scrollWidth - elem.offsetLeft;
            break;
          case 'right':
            curpos2 = elem.offsetLeft;
            break;
        }
        // Remember the first pos2, so the first notice goes there.
        if (typeof stack.firstpos2 === 'undefined') {
          stack.firstpos2 = curpos2;
          stack.nextpos2 = stack.firstpos2;
        }

        // Check that it's not beyond the viewport edge.
        var endY = stack.nextpos1 + elem.offsetHeight + (typeof stack.spacing1 === 'undefined' ? 25 : stack.spacing1);
        var endX = stack.nextpos1 + elem.offsetWidth + (typeof stack.spacing1 === 'undefined' ? 25 : stack.spacing1);
        if ((stack.dir1 === 'down' || stack.dir1 === 'up') && endY > spaceY || (stack.dir1 === 'left' || stack.dir1 === 'right') && endX > spaceX) {
          // If it is, it needs to go back to the first pos1, and over on pos2.
          stack.nextpos1 = stack.firstpos1;
          stack.nextpos2 += stack.addpos2 + (typeof stack.spacing2 === 'undefined' ? 25 : stack.spacing2);
          stack.addpos2 = 0;
        }

        // Move the notice on dir2.
        if (typeof stack.nextpos2 === 'number') {
          elem.style[csspos2] = stack.nextpos2 + 'px';
          if (!stack.animation) {
            // eslint-disable-next-line no-unused-expressions
            elem.style[csspos2]; // Read from the DOM for update.
          }
        }

        // Keep track of the widest/tallest notice in the column/row, so we can push the next column/row.
        switch (stack.dir2) {
          case 'down':
          case 'up':
            if (elem.offsetHeight + (parseFloat(elem.style.marginTop, 10) || 0) + (parseFloat(elem.style.marginBottom, 10) || 0) > stack.addpos2) {
              stack.addpos2 = elem.offsetHeight;
            }
            break;
          case 'left':
          case 'right':
            if (elem.offsetWidth + (parseFloat(elem.style.marginLeft, 10) || 0) + (parseFloat(elem.style.marginRight, 10) || 0) > stack.addpos2) {
              stack.addpos2 = elem.offsetWidth;
            }
            break;
        }
      } else if (stack.dir1) {
        // Center the notice along dir1 axis, because the stack has no dir2.
        var cssMiddle = void 0,
            cssposCross = void 0;
        switch (stack.dir1) {
          case 'down':
          case 'up':
            cssposCross = ['left', 'right'];
            cssMiddle = stack.context.scrollWidth / 2 - elem.offsetWidth / 2;
            break;
          case 'left':
          case 'right':
            cssposCross = ['top', 'bottom'];
            cssMiddle = spaceY / 2 - elem.offsetHeight / 2;
            break;
        }
        elem.style[cssposCross[0]] = cssMiddle + 'px';
        elem.style[cssposCross[1]] = 'auto';
        if (!stack.animation) {
          // eslint-disable-next-line no-unused-expressions
          elem.style[cssposCross[0]]; // Read from the DOM for update.
        }
      }

      if (stack.dir1) {
        // Move the notice on dir1.
        if (typeof stack.nextpos1 === 'number') {
          elem.style[csspos1] = stack.nextpos1 + 'px';
          if (!stack.animation) {
            // eslint-disable-next-line no-unused-expressions
            elem.style[csspos1]; // Read from the DOM for update.
          }
        }

        // Calculate the next dir1 position.
        switch (stack.dir1) {
          case 'down':
          case 'up':
            stack.nextpos1 += elem.offsetHeight + (typeof stack.spacing1 === 'undefined' ? 25 : stack.spacing1);
            break;
          case 'left':
          case 'right':
            stack.nextpos1 += elem.offsetWidth + (typeof stack.spacing1 === 'undefined' ? 25 : stack.spacing1);
            break;
        }
      } else {
        // Center the notice on the screen, because the stack has no dir1.
        var cssMiddleLeft = spaceX / 2 - elem.offsetWidth / 2;
        var cssMiddleTop = spaceY / 2 - elem.offsetHeight / 2;
        elem.style.left = cssMiddleLeft + 'px';
        elem.style.top = cssMiddleTop + 'px';
        if (!stack.animation) {
          // eslint-disable-next-line no-unused-expressions
          elem.style.left; // Read from the DOM for update.
        }
      }

      return this;
    },


    // Queue the position all function so it doesn't run repeatedly and
    // use up resources.
    queuePosition: function queuePosition(milliseconds) {
      if (posTimer) {
        clearTimeout(posTimer);
      }
      if (!milliseconds) {
        milliseconds = 10;
      }
      posTimer = setTimeout(function () {
        PNotify.positionAll();
      }, milliseconds);
      return this;
    },
    cancelRemove: function cancelRemove() {
      return this.cancelClose();
    },


    // Cancel any pending removal timer.
    cancelClose: function cancelClose() {
      var _get13 = this.get(),
          _timer = _get13._timer,
          _animTimer = _get13._animTimer,
          _state = _get13._state,
          animation = _get13.animation;

      if (_timer) {
        clearTimeout(_timer);
      }
      if (_animTimer) {
        clearTimeout(_animTimer);
      }
      if (_state === 'closing') {
        // If it's animating out, stop it.
        this.set({
          '_state': 'open',
          '_animating': false,
          '_animatingClass': animation === 'fade' ? 'ui-pnotify-in ui-pnotify-fade-in' : 'ui-pnotify-in'
        });
      }
      return this;
    },
    queueRemove: function queueRemove() {
      return this.queueClose();
    },


    // Queue a close timer.
    queueClose: function queueClose() {
      var _this5 = this;

      // Cancel any current close timer.
      this.cancelClose();
      this.set({
        '_timer': setTimeout(function () {
          return _this5.close(true);
        }, isNaN(this.get().delay) ? 0 : this.get().delay)
      });
      return this;
    },
    addModuleClass: function addModuleClass() {
      var _get14 = this.get(),
          _moduleClasses = _get14._moduleClasses;

      for (var _len = arguments.length, classNames = Array(_len), _key = 0; _key < _len; _key++) {
        classNames[_key] = arguments[_key];
      }

      for (var i = 0; i < classNames.length; i++) {
        var className = classNames[i];
        if (_moduleClasses.indexOf(className) === -1) {
          _moduleClasses.push(className);
        }
      }
      this.set({ _moduleClasses: _moduleClasses });
    },
    removeModuleClass: function removeModuleClass() {
      var _get15 = this.get(),
          _moduleClasses = _get15._moduleClasses;

      for (var _len2 = arguments.length, classNames = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        classNames[_key2] = arguments[_key2];
      }

      for (var i = 0; i < classNames.length; i++) {
        var className = classNames[i];
        var idx = _moduleClasses.indexOf(className);
        if (idx !== -1) {
          _moduleClasses.splice(idx, 1);
        }
      }
      this.set({ _moduleClasses: _moduleClasses });
    },
    hasModuleClass: function hasModuleClass() {
      var _get16 = this.get(),
          _moduleClasses = _get16._moduleClasses;

      for (var _len3 = arguments.length, classNames = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        classNames[_key3] = arguments[_key3];
      }

      for (var i = 0; i < classNames.length; i++) {
        var className = classNames[i];
        if (_moduleClasses.indexOf(className) === -1) {
          return false;
        }
      }
      return true;
    }
  };

  function oncreate() {
    var _this6 = this;

    this.on('mouseenter', function (e) {
      // Stop animation, reset the removal timer when the user mouses over.
      if (_this6.get().mouseReset && _this6.get()._animating === 'out') {
        if (!_this6.get()._timerHide) {
          return;
        }
        _this6.cancelClose();
      }
      // Stop the close timer.
      if (_this6.get().hide && _this6.get().mouseReset) {
        _this6.cancelClose();
      }
    });

    this.on('mouseleave', function (e) {
      // Start the close timer.
      if (_this6.get().hide && _this6.get().mouseReset && _this6.get()._animating !== 'out') {
        _this6.queueClose();
      }
      PNotify.positionAll();
    });

    var _get17 = this.get(),
        stack = _get17.stack;

    // Add the notice to the notice array.


    if (stack && stack.push === 'top') {
      PNotify.notices.splice(0, 0, this);
    } else {
      PNotify.notices.push(this);
    }

    // Run the modules.
    this.runModules('init');

    // We're now initialized, but haven't been opened yet.
    this.set({ '_state': 'closed' });

    // Display the notice.
    if (this.get().autoDisplay) {
      this.open();
    }
  };

  function setup(Component) {
    // Add static properties to the PNotify object.
    PNotify = Component;

    PNotify.VERSION = '4.0.0';

    PNotify.defaultStack = {
      dir1: 'down',
      dir2: 'left',
      firstpos1: 25,
      firstpos2: 25,
      spacing1: 36,
      spacing2: 36,
      push: 'bottom',
      context: window && document.body
    };

    PNotify.defaults = {
      // The notice's title.
      title: false,
      // Whether to trust the title or escape its contents. (Not allow HTML.)
      titleTrusted: false,
      // The notice's text.
      text: false,
      // Whether to trust the text or escape its contents. (Not allow HTML.)
      textTrusted: false,
      // What styling classes to use. (Can be 'brighttheme', 'bootstrap3', 'bootstrap4', or a styling object.)
      styling: 'brighttheme',
      // What icons to use (Can be 'brighttheme', 'bootstrap3', 'fontawesome4', 'fontawesome5', or an icon object.)
      icons: 'brighttheme',
      // Additional classes to be added to the notice. (For custom styling.)
      addClass: '',
      // Class to be added to the notice for corner styling.
      cornerClass: '',
      // Display the notice when it is created.
      autoDisplay: true,
      // Width of the notice.
      width: '360px',
      // Minimum height of the notice. It will expand to fit content.
      minHeight: '16px',
      // Type of the notice. 'notice', 'info', 'success', or 'error'.
      type: 'notice',
      // Set icon to true to use the default icon for the selected
      // style/type, false for no icon, or a string for your own icon class.
      icon: true,
      // The animation to use when displaying and hiding the notice. 'none'
      // and 'fade' are supported through CSS. Others are supported
      // through the Animate module and Animate.css.
      animation: 'fade',
      // Speed at which the notice animates in and out. 'slow', 'normal',
      // or 'fast'. Respectively, 400ms, 250ms, 100ms.
      animateSpeed: 'normal',
      // Display a drop shadow.
      shadow: true,
      // After a delay, remove the notice.
      hide: true,
      // Delay in milliseconds before the notice is removed.
      delay: 8000,
      // Reset the hide timer if the mouse moves over the notice.
      mouseReset: true,
      // Remove the notice's elements from the DOM after it is removed.
      remove: true,
      // Whether to remove the notice from the global array when it is closed.
      destroy: true,
      // The stack on which the notices will be placed. Also controls the
      // direction the notices stack.
      stack: PNotify.defaultStack,
      // This is where options for modules should be defined.
      modules: {}
    };

    // An array of all active notices.
    PNotify.notices = [];

    // This object holds all the PNotify modules. They are used to provide
    // additional functionality.
    PNotify.modules = {};

    // Modules can add themselves to these to be rendered in the template.
    PNotify.modulesPrependContainer = [];
    PNotify.modulesAppendContainer = [];

    // Helper function to create a new notice.
    PNotify.alert = function (options) {
      return new PNotify(getDefaultArgs(options));
    };
    // Helper function to create a new notice (notice type).
    PNotify.notice = function (options) {
      return new PNotify(getDefaultArgs(options, 'notice'));
    };
    // Helper function to create a new notice (info type).
    PNotify.info = function (options) {
      return new PNotify(getDefaultArgs(options, 'info'));
    };
    // Helper function to create a new notice (success type).
    PNotify.success = function (options) {
      return new PNotify(getDefaultArgs(options, 'success'));
    };
    // Helper function to create a new notice (error type).
    PNotify.error = function (options) {
      return new PNotify(getDefaultArgs(options, 'error'));
    };

    PNotify.removeAll = function () {
      PNotify.closeAll();
    };

    // Close all notices.
    PNotify.closeAll = function () {
      for (var i = 0; i < PNotify.notices.length; i++) {
        if (PNotify.notices[i].close) {
          PNotify.notices[i].close(false);
        }
      }
    };

    PNotify.removeStack = function (stack) {
      PNotify.closeStack(stack);
    };

    // Close all notices in a single stack.
    PNotify.closeStack = function (stack) {
      if (stack === false) {
        return;
      }
      for (var i = 0; i < PNotify.notices.length; i++) {
        if (PNotify.notices[i].close && PNotify.notices[i].get().stack === stack) {
          PNotify.notices[i].close(false);
        }
      }
    };

    // Position all notices.
    PNotify.positionAll = function () {
      // This timer is used for queueing this function so it doesn't run
      // repeatedly.
      if (posTimer) {
        clearTimeout(posTimer);
      }
      posTimer = null;
      // Reset the next position data.
      if (PNotify.notices.length > 0) {
        for (var i = 0; i < PNotify.notices.length; i++) {
          var notice = PNotify.notices[i];

          var _notice$get = notice.get(),
              stack = _notice$get.stack;

          if (!stack) {
            continue;
          }
          if (stack.overlay) {
            removeStackOverlay(stack);
          }
          stack.nextpos1 = stack.firstpos1;
          stack.nextpos2 = stack.firstpos2;
          stack.addpos2 = 0;
        }
        for (var _i = 0; _i < PNotify.notices.length; _i++) {
          PNotify.notices[_i].position();
        }
      } else {
        delete PNotify.defaultStack.nextpos1;
        delete PNotify.defaultStack.nextpos2;
      }
    };

    PNotify.styling = {
      brighttheme: {
        // Bright Theme doesn't require any UI libraries.
        container: 'brighttheme',
        notice: 'brighttheme-notice',
        info: 'brighttheme-info',
        success: 'brighttheme-success',
        error: 'brighttheme-error'
      },
      bootstrap3: {
        container: 'alert',
        notice: 'alert-warning',
        info: 'alert-info',
        success: 'alert-success',
        error: 'alert-danger',
        icon: 'ui-pnotify-icon-bs3'
      },
      bootstrap4: {
        container: 'alert',
        notice: 'alert-warning',
        info: 'alert-info',
        success: 'alert-success',
        error: 'alert-danger',
        icon: 'ui-pnotify-icon-bs4',
        title: 'ui-pnotify-title-bs4'
      }
    };

    // icons are separate from the style, since bs4 doesn't come with any
    PNotify.icons = {
      brighttheme: {
        notice: 'brighttheme-icon-notice',
        info: 'brighttheme-icon-info',
        success: 'brighttheme-icon-success',
        error: 'brighttheme-icon-error'
      },
      bootstrap3: {
        notice: 'glyphicon glyphicon-exclamation-sign',
        info: 'glyphicon glyphicon-info-sign',
        success: 'glyphicon glyphicon-ok-sign',
        error: 'glyphicon glyphicon-warning-sign'
      },
      // User must have Font Awesome v4.0+
      fontawesome4: {
        notice: 'fa fa-exclamation-circle',
        info: 'fa fa-info-circle',
        success: 'fa fa-check-circle',
        error: 'fa fa-exclamation-triangle'
      },
      // User must have Font Awesome v5.0+
      fontawesome5: {
        notice: 'fas fa-exclamation-circle',
        info: 'fas fa-info-circle',
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-triangle'
      }
    };

    // Run the deferred actions once the DOM is ready.
    if (window && document.body) {
      onDocumentLoaded();
    } else {
      document.addEventListener('DOMContentLoaded', onDocumentLoaded);
    }
  }

  function add_css() {
    var style = createElement("style");
    style.id = 'svelte-1eldsjg-style';
    style.textContent = "body > .ui-pnotify{position:fixed;z-index:100040}body > .ui-pnotify.ui-pnotify-modal{z-index:100042}.ui-pnotify{position:absolute;height:auto;z-index:1;display:none}.ui-pnotify.ui-pnotify-modal{z-index:3}.ui-pnotify.ui-pnotify-in{display:block}.ui-pnotify.ui-pnotify-initial-hidden{display:block;visibility:hidden}.ui-pnotify.ui-pnotify-move{transition:left .5s ease, top .5s ease, right .5s ease, bottom .5s ease}.ui-pnotify.ui-pnotify-fade-slow{transition:opacity .4s linear;opacity:0}.ui-pnotify.ui-pnotify-fade-slow.ui-pnotify.ui-pnotify-move{transition:opacity .4s linear, left .5s ease, top .5s ease, right .5s ease, bottom .5s ease}.ui-pnotify.ui-pnotify-fade-normal{transition:opacity .25s linear;opacity:0}.ui-pnotify.ui-pnotify-fade-normal.ui-pnotify.ui-pnotify-move{transition:opacity .25s linear, left .5s ease, top .5s ease, right .5s ease, bottom .5s ease}.ui-pnotify.ui-pnotify-fade-fast{transition:opacity .1s linear;opacity:0}.ui-pnotify.ui-pnotify-fade-fast.ui-pnotify.ui-pnotify-move{transition:opacity .1s linear, left .5s ease, top .5s ease, right .5s ease, bottom .5s ease}.ui-pnotify.ui-pnotify-fade-in{opacity:1}.ui-pnotify .ui-pnotify-shadow{-webkit-box-shadow:0px 6px 28px 0px rgba(0,0,0,0.1);-moz-box-shadow:0px 6px 28px 0px rgba(0,0,0,0.1);box-shadow:0px 6px 28px 0px rgba(0,0,0,0.1)}.ui-pnotify-container{background-position:0 0;padding:.8em;height:100%;margin:0}.ui-pnotify-container:after{content:\" \";visibility:hidden;display:block;height:0;clear:both}.ui-pnotify-container.ui-pnotify-sharp{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.ui-pnotify-title{display:block;white-space:pre-line;margin-bottom:.4em;margin-top:0}.ui-pnotify.ui-pnotify-with-icon .ui-pnotify-title,.ui-pnotify.ui-pnotify-with-icon .ui-pnotify-text{margin-left:24px}[dir=rtl] .ui-pnotify.ui-pnotify-with-icon .ui-pnotify-title,[dir=rtl] .ui-pnotify.ui-pnotify-with-icon .ui-pnotify-text{margin-right:24px;margin-left:0}.ui-pnotify-title-bs4{font-size:1.2rem}.ui-pnotify-text{display:block;white-space:pre-line}.ui-pnotify-icon,.ui-pnotify-icon span{display:block;float:left}[dir=rtl] .ui-pnotify-icon,[dir=rtl] .ui-pnotify-icon span{float:right}.ui-pnotify-icon-bs3 > span{position:relative;top:2px}.ui-pnotify-icon-bs4 > span{position:relative;top:4px}.ui-pnotify-modal-overlay{background-color:rgba(0, 0, 0, .4);top:0;left:0;position:absolute;height:100%;width:100%;z-index:2}body > .ui-pnotify-modal-overlay{position:fixed;z-index:100041}";
    append(document.head, style);
  }

  function get_each1_context(ctx, list, i) {
    var child_ctx = Object.create(ctx);
    child_ctx.module = list[i];
    return child_ctx;
  }

  function get_each0_context(ctx, list, i) {
    var child_ctx = Object.create(ctx);
    child_ctx.module = list[i];
    return child_ctx;
  }

  function create_main_fragment(component, ctx) {
    var div1,
        div0,
        each0_blocks_1 = [],
        each0_lookup = blankObject(),
        text0,
        text1,
        text2,
        text3,
        each1_blocks_1 = [],
        each1_lookup = blankObject(),
        div0_class_value,
        div0_style_value,
        div1_class_value;

    var each0_value = ctx._modulesPrependContainer;

    var get_key = function get_key(ctx) {
      return ctx.module.key;
    };

    for (var i = 0; i < each0_value.length; i += 1) {
      var child_ctx = get_each0_context(ctx, each0_value, i);
      var key = get_key(child_ctx);
      each0_blocks_1[i] = each0_lookup[key] = create_each_block_1(component, key, child_ctx);
    }

    var if_block0 = ctx.icon !== false && create_if_block_4(component, ctx);

    var if_block1 = ctx.title !== false && create_if_block_2(component, ctx);

    var if_block2 = ctx.text !== false && create_if_block(component, ctx);

    var each1_value = ctx._modulesAppendContainer;

    var get_key_1 = function get_key_1(ctx) {
      return ctx.module.key;
    };

    for (var i = 0; i < each1_value.length; i += 1) {
      var _child_ctx = get_each1_context(ctx, each1_value, i);
      var _key4 = get_key_1(_child_ctx);
      each1_blocks_1[i] = each1_lookup[_key4] = create_each_block(component, _key4, _child_ctx);
    }

    function mouseover_handler(event) {
      component.fire("mouseover", event);
    }

    function mouseout_handler(event) {
      component.fire("mouseout", event);
    }

    function mouseenter_handler(event) {
      component.fire("mouseenter", event);
    }

    function mouseleave_handler(event) {
      component.fire("mouseleave", event);
    }

    function mousemove_handler(event) {
      component.fire("mousemove", event);
    }

    function mousedown_handler(event) {
      component.fire("mousedown", event);
    }

    function mouseup_handler(event) {
      component.fire("mouseup", event);
    }

    function click_handler(event) {
      component.fire("click", event);
    }

    function dblclick_handler(event) {
      component.fire("dblclick", event);
    }

    function focus_handler(event) {
      component.fire("focus", event);
    }

    function blur_handler(event) {
      component.fire("blur", event);
    }

    function touchstart_handler(event) {
      component.fire("touchstart", event);
    }

    function touchmove_handler(event) {
      component.fire("touchmove", event);
    }

    function touchend_handler(event) {
      component.fire("touchend", event);
    }

    function touchcancel_handler(event) {
      component.fire("touchcancel", event);
    }

    return {
      c: function c() {
        div1 = createElement("div");
        div0 = createElement("div");

        for (i = 0; i < each0_blocks_1.length; i += 1) {
          each0_blocks_1[i].c();
        }text0 = createText("\n    ");
        if (if_block0) if_block0.c();
        text1 = createText("\n    ");
        if (if_block1) if_block1.c();
        text2 = createText("\n    ");
        if (if_block2) if_block2.c();
        text3 = createText("\n    ");

        for (i = 0; i < each1_blocks_1.length; i += 1) {
          each1_blocks_1[i].c();
        }div0.className = div0_class_value = "\n        ui-pnotify-container\n        " + (ctx._styles.container ? ctx._styles.container : '') + "\n        " + (ctx._styles[ctx.type] ? ctx._styles[ctx.type] : '') + "\n        " + ctx.cornerClass + "\n        " + (ctx.shadow ? 'ui-pnotify-shadow' : '') + "\n      ";
        div0.style.cssText = div0_style_value = "" + ctx._widthStyle + " " + ctx._minHeightStyle;
        setAttribute(div0, "role", "alert");
        addListener(div1, "mouseover", mouseover_handler);
        addListener(div1, "mouseout", mouseout_handler);
        addListener(div1, "mouseenter", mouseenter_handler);
        addListener(div1, "mouseleave", mouseleave_handler);
        addListener(div1, "mousemove", mousemove_handler);
        addListener(div1, "mousedown", mousedown_handler);
        addListener(div1, "mouseup", mouseup_handler);
        addListener(div1, "click", click_handler);
        addListener(div1, "dblclick", dblclick_handler);
        addListener(div1, "focus", focus_handler);
        addListener(div1, "blur", blur_handler);
        addListener(div1, "touchstart", touchstart_handler);
        addListener(div1, "touchmove", touchmove_handler);
        addListener(div1, "touchend", touchend_handler);
        addListener(div1, "touchcancel", touchcancel_handler);
        div1.className = div1_class_value = "\n      ui-pnotify\n      " + (ctx.icon !== false ? 'ui-pnotify-with-icon' : '') + "\n      " + (ctx._styles.element ? ctx._styles.element : '') + "\n      " + ctx.addClass + "\n      " + ctx._animatingClass + "\n      " + ctx._moveClass + "\n      " + (ctx.animation === 'fade' ? 'ui-pnotify-fade-' + ctx.animateSpeed : '') + "\n      " + (ctx.stack && ctx.stack.modal ? 'ui-pnotify-modal' : '') + "\n      " + ctx._moduleClasses.join(' ') + "\n    ";
        setAttribute(div1, "aria-live", "assertive");
        setAttribute(div1, "role", "alertdialog");
        setAttribute(div1, "ui-pnotify", true);
      },
      m: function m(target, anchor) {
        insert(target, div1, anchor);
        append(div1, div0);

        for (i = 0; i < each0_blocks_1.length; i += 1) {
          each0_blocks_1[i].m(div0, null);
        }append(div0, text0);
        if (if_block0) if_block0.m(div0, null);
        append(div0, text1);
        if (if_block1) if_block1.m(div0, null);
        append(div0, text2);
        if (if_block2) if_block2.m(div0, null);
        append(div0, text3);

        for (i = 0; i < each1_blocks_1.length; i += 1) {
          each1_blocks_1[i].m(div0, null);
        }component.refs.container = div0;
        component.refs.elem = div1;
      },
      p: function p(changed, ctx) {
        var each0_value = ctx._modulesPrependContainer;
        each0_blocks_1 = updateKeyedEach(each0_blocks_1, component, changed, get_key, 1, ctx, each0_value, each0_lookup, div0, destroyBlock, create_each_block_1, "m", text0, get_each0_context);

        if (ctx.icon !== false) {
          if (if_block0) {
            if_block0.p(changed, ctx);
          } else {
            if_block0 = create_if_block_4(component, ctx);
            if_block0.c();
            if_block0.m(div0, text1);
          }
        } else if (if_block0) {
          if_block0.d(1);
          if_block0 = null;
        }

        if (ctx.title !== false) {
          if (if_block1) {
            if_block1.p(changed, ctx);
          } else {
            if_block1 = create_if_block_2(component, ctx);
            if_block1.c();
            if_block1.m(div0, text2);
          }
        } else if (if_block1) {
          if_block1.d(1);
          if_block1 = null;
        }

        if (ctx.text !== false) {
          if (if_block2) {
            if_block2.p(changed, ctx);
          } else {
            if_block2 = create_if_block(component, ctx);
            if_block2.c();
            if_block2.m(div0, text3);
          }
        } else if (if_block2) {
          if_block2.d(1);
          if_block2 = null;
        }

        var each1_value = ctx._modulesAppendContainer;
        each1_blocks_1 = updateKeyedEach(each1_blocks_1, component, changed, get_key_1, 1, ctx, each1_value, each1_lookup, div0, destroyBlock, create_each_block, "m", null, get_each1_context);

        if ((changed._styles || changed.type || changed.cornerClass || changed.shadow) && div0_class_value !== (div0_class_value = "\n        ui-pnotify-container\n        " + (ctx._styles.container ? ctx._styles.container : '') + "\n        " + (ctx._styles[ctx.type] ? ctx._styles[ctx.type] : '') + "\n        " + ctx.cornerClass + "\n        " + (ctx.shadow ? 'ui-pnotify-shadow' : '') + "\n      ")) {
          div0.className = div0_class_value;
        }

        if ((changed._widthStyle || changed._minHeightStyle) && div0_style_value !== (div0_style_value = "" + ctx._widthStyle + " " + ctx._minHeightStyle)) {
          div0.style.cssText = div0_style_value;
        }

        if ((changed.icon || changed._styles || changed.addClass || changed._animatingClass || changed._moveClass || changed.animation || changed.animateSpeed || changed.stack || changed._moduleClasses) && div1_class_value !== (div1_class_value = "\n      ui-pnotify\n      " + (ctx.icon !== false ? 'ui-pnotify-with-icon' : '') + "\n      " + (ctx._styles.element ? ctx._styles.element : '') + "\n      " + ctx.addClass + "\n      " + ctx._animatingClass + "\n      " + ctx._moveClass + "\n      " + (ctx.animation === 'fade' ? 'ui-pnotify-fade-' + ctx.animateSpeed : '') + "\n      " + (ctx.stack && ctx.stack.modal ? 'ui-pnotify-modal' : '') + "\n      " + ctx._moduleClasses.join(' ') + "\n    ")) {
          div1.className = div1_class_value;
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(div1);
        }

        for (i = 0; i < each0_blocks_1.length; i += 1) {
          each0_blocks_1[i].d();
        }if (if_block0) if_block0.d();
        if (if_block1) if_block1.d();
        if (if_block2) if_block2.d();

        for (i = 0; i < each1_blocks_1.length; i += 1) {
          each1_blocks_1[i].d();
        }if (component.refs.container === div0) component.refs.container = null;
        removeListener(div1, "mouseover", mouseover_handler);
        removeListener(div1, "mouseout", mouseout_handler);
        removeListener(div1, "mouseenter", mouseenter_handler);
        removeListener(div1, "mouseleave", mouseleave_handler);
        removeListener(div1, "mousemove", mousemove_handler);
        removeListener(div1, "mousedown", mousedown_handler);
        removeListener(div1, "mouseup", mouseup_handler);
        removeListener(div1, "click", click_handler);
        removeListener(div1, "dblclick", dblclick_handler);
        removeListener(div1, "focus", focus_handler);
        removeListener(div1, "blur", blur_handler);
        removeListener(div1, "touchstart", touchstart_handler);
        removeListener(div1, "touchmove", touchmove_handler);
        removeListener(div1, "touchend", touchend_handler);
        removeListener(div1, "touchcancel", touchcancel_handler);
        if (component.refs.elem === div1) component.refs.elem = null;
      }
    };
  }

  // (53:4) {#each _modulesPrependContainer as module (module.key)}
  function create_each_block_1(component, key_1, ctx) {
    var first, switch_instance_anchor;

    var switch_value = ctx.module;

    function switch_props(ctx) {
      return {
        root: component.root,
        store: component.store
      };
    }

    if (switch_value) {
      var switch_instance = new switch_value(switch_props(ctx));
    }

    function switch_instance_init(event) {
      component.initModule(event.module);
    }

    if (switch_instance) switch_instance.on("init", switch_instance_init);

    return {
      key: key_1,

      first: null,

      c: function c() {
        first = createComment();
        if (switch_instance) switch_instance._fragment.c();
        switch_instance_anchor = createComment();
        this.first = first;
      },
      m: function m(target, anchor) {
        insert(target, first, anchor);

        if (switch_instance) {
          switch_instance._mount(target, anchor);
        }

        insert(target, switch_instance_anchor, anchor);
      },
      p: function p(changed, ctx) {
        if (switch_value !== (switch_value = ctx.module)) {
          if (switch_instance) {
            switch_instance.destroy();
          }

          if (switch_value) {
            switch_instance = new switch_value(switch_props(ctx));
            switch_instance._fragment.c();
            switch_instance._mount(switch_instance_anchor.parentNode, switch_instance_anchor);

            switch_instance.on("init", switch_instance_init);
          } else {
            switch_instance = null;
          }
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(first);
          detachNode(switch_instance_anchor);
        }

        if (switch_instance) switch_instance.destroy(detach);
      }
    };
  }

  // (56:4) {#if icon !== false}
  function create_if_block_4(component, ctx) {
    var div, span, span_class_value, div_class_value;

    return {
      c: function c() {
        div = createElement("div");
        span = createElement("span");
        span.className = span_class_value = ctx.icon === true ? ctx._icons[ctx.type] ? ctx._icons[ctx.type] : '' : ctx.icon;
        div.className = div_class_value = "ui-pnotify-icon " + (ctx._styles.icon ? ctx._styles.icon : '');
      },
      m: function m(target, anchor) {
        insert(target, div, anchor);
        append(div, span);
        component.refs.iconContainer = div;
      },
      p: function p(changed, ctx) {
        if ((changed.icon || changed._icons || changed.type) && span_class_value !== (span_class_value = ctx.icon === true ? ctx._icons[ctx.type] ? ctx._icons[ctx.type] : '' : ctx.icon)) {
          span.className = span_class_value;
        }

        if (changed._styles && div_class_value !== (div_class_value = "ui-pnotify-icon " + (ctx._styles.icon ? ctx._styles.icon : ''))) {
          div.className = div_class_value;
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(div);
        }

        if (component.refs.iconContainer === div) component.refs.iconContainer = null;
      }
    };
  }

  // (61:4) {#if title !== false}
  function create_if_block_2(component, ctx) {
    var h4, h4_class_value;

    function select_block_type(ctx) {
      if (ctx.titleTrusted) return create_if_block_3;
      return create_else_block_1;
    }

    var current_block_type = select_block_type(ctx);
    var if_block = current_block_type(component, ctx);

    return {
      c: function c() {
        h4 = createElement("h4");
        if_block.c();
        h4.className = h4_class_value = "ui-pnotify-title " + (ctx._styles.title ? ctx._styles.title : '');
      },
      m: function m(target, anchor) {
        insert(target, h4, anchor);
        if_block.m(h4, null);
        component.refs.titleContainer = h4;
      },
      p: function p(changed, ctx) {
        if (current_block_type === (current_block_type = select_block_type(ctx)) && if_block) {
          if_block.p(changed, ctx);
        } else {
          if_block.d(1);
          if_block = current_block_type(component, ctx);
          if_block.c();
          if_block.m(h4, null);
        }

        if (changed._styles && h4_class_value !== (h4_class_value = "ui-pnotify-title " + (ctx._styles.title ? ctx._styles.title : ''))) {
          h4.className = h4_class_value;
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(h4);
        }

        if_block.d();
        if (component.refs.titleContainer === h4) component.refs.titleContainer = null;
      }
    };
  }

  // (65:8) {:else}
  function create_else_block_1(component, ctx) {
    var text;

    return {
      c: function c() {
        text = createText(ctx.title);
      },
      m: function m(target, anchor) {
        insert(target, text, anchor);
      },
      p: function p(changed, ctx) {
        if (changed.title) {
          setData(text, ctx.title);
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(text);
        }
      }
    };
  }

  // (63:8) {#if titleTrusted}
  function create_if_block_3(component, ctx) {
    var raw_before, raw_after;

    return {
      c: function c() {
        raw_before = createElement('noscript');
        raw_after = createElement('noscript');
      },
      m: function m(target, anchor) {
        insert(target, raw_before, anchor);
        raw_before.insertAdjacentHTML("afterend", ctx.title);
        insert(target, raw_after, anchor);
      },
      p: function p(changed, ctx) {
        if (changed.title) {
          detachBetween(raw_before, raw_after);
          raw_before.insertAdjacentHTML("afterend", ctx.title);
        }
      },
      d: function d(detach) {
        if (detach) {
          detachBetween(raw_before, raw_after);
          detachNode(raw_before);
          detachNode(raw_after);
        }
      }
    };
  }

  // (70:4) {#if text !== false}
  function create_if_block(component, ctx) {
    var div, div_class_value;

    function select_block_type_1(ctx) {
      if (ctx.textTrusted) return create_if_block_1;
      return create_else_block;
    }

    var current_block_type = select_block_type_1(ctx);
    var if_block = current_block_type(component, ctx);

    return {
      c: function c() {
        div = createElement("div");
        if_block.c();
        div.className = div_class_value = "ui-pnotify-text " + (ctx._styles.text ? ctx._styles.text : '');
        setAttribute(div, "role", "alert");
      },
      m: function m(target, anchor) {
        insert(target, div, anchor);
        if_block.m(div, null);
        component.refs.textContainer = div;
      },
      p: function p(changed, ctx) {
        if (current_block_type === (current_block_type = select_block_type_1(ctx)) && if_block) {
          if_block.p(changed, ctx);
        } else {
          if_block.d(1);
          if_block = current_block_type(component, ctx);
          if_block.c();
          if_block.m(div, null);
        }

        if (changed._styles && div_class_value !== (div_class_value = "ui-pnotify-text " + (ctx._styles.text ? ctx._styles.text : ''))) {
          div.className = div_class_value;
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(div);
        }

        if_block.d();
        if (component.refs.textContainer === div) component.refs.textContainer = null;
      }
    };
  }

  // (74:8) {:else}
  function create_else_block(component, ctx) {
    var text;

    return {
      c: function c() {
        text = createText(ctx.text);
      },
      m: function m(target, anchor) {
        insert(target, text, anchor);
      },
      p: function p(changed, ctx) {
        if (changed.text) {
          setData(text, ctx.text);
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(text);
        }
      }
    };
  }

  // (72:8) {#if textTrusted}
  function create_if_block_1(component, ctx) {
    var raw_before, raw_after;

    return {
      c: function c() {
        raw_before = createElement('noscript');
        raw_after = createElement('noscript');
      },
      m: function m(target, anchor) {
        insert(target, raw_before, anchor);
        raw_before.insertAdjacentHTML("afterend", ctx.text);
        insert(target, raw_after, anchor);
      },
      p: function p(changed, ctx) {
        if (changed.text) {
          detachBetween(raw_before, raw_after);
          raw_before.insertAdjacentHTML("afterend", ctx.text);
        }
      },
      d: function d(detach) {
        if (detach) {
          detachBetween(raw_before, raw_after);
          detachNode(raw_before);
          detachNode(raw_after);
        }
      }
    };
  }

  // (79:4) {#each _modulesAppendContainer as module (module.key)}
  function create_each_block(component, key_1, ctx) {
    var first, switch_instance_anchor;

    var switch_value = ctx.module;

    function switch_props(ctx) {
      return {
        root: component.root,
        store: component.store
      };
    }

    if (switch_value) {
      var switch_instance = new switch_value(switch_props(ctx));
    }

    function switch_instance_init(event) {
      component.initModule(event.module);
    }

    if (switch_instance) switch_instance.on("init", switch_instance_init);

    return {
      key: key_1,

      first: null,

      c: function c() {
        first = createComment();
        if (switch_instance) switch_instance._fragment.c();
        switch_instance_anchor = createComment();
        this.first = first;
      },
      m: function m(target, anchor) {
        insert(target, first, anchor);

        if (switch_instance) {
          switch_instance._mount(target, anchor);
        }

        insert(target, switch_instance_anchor, anchor);
      },
      p: function p(changed, ctx) {
        if (switch_value !== (switch_value = ctx.module)) {
          if (switch_instance) {
            switch_instance.destroy();
          }

          if (switch_value) {
            switch_instance = new switch_value(switch_props(ctx));
            switch_instance._fragment.c();
            switch_instance._mount(switch_instance_anchor.parentNode, switch_instance_anchor);

            switch_instance.on("init", switch_instance_init);
          } else {
            switch_instance = null;
          }
        }
      },
      d: function d(detach) {
        if (detach) {
          detachNode(first);
          detachNode(switch_instance_anchor);
        }

        if (switch_instance) switch_instance.destroy(detach);
      }
    };
  }

  function PNotify_1(options) {
    var _this7 = this;

    init(this, options);
    this.refs = {};
    this._state = assign(data(), options.data);

    this._recompute({ styling: 1, icons: 1, width: 1, minHeight: 1 }, this._state);
    this._intro = true;

    if (!document.getElementById("svelte-1eldsjg-style")) add_css();

    this._fragment = create_main_fragment(this, this._state);

    this.root._oncreate.push(function () {
      oncreate.call(_this7);
      _this7.fire("update", { changed: assignTrue({}, _this7._state), current: _this7._state });
    });

    if (options.target) {
      this._fragment.c();
      this._mount(options.target, options.anchor);

      flush(this);
    }
  }

  assign(PNotify_1.prototype, {
    destroy: destroy,
    get: get,
    fire: fire,
    on: on,
    set: set,
    _set: _set,
    _stage: _stage,
    _mount: _mount,
    _differs: _differs
  });
  assign(PNotify_1.prototype, methods);

  PNotify_1.prototype._recompute = function _recompute(changed, state) {
    if (changed.styling) {
      if (this._differs(state._styles, state._styles = _styles(state))) changed._styles = true;
    }

    if (changed.icons) {
      if (this._differs(state._icons, state._icons = _icons(state))) changed._icons = true;
    }

    if (changed.width) {
      if (this._differs(state._widthStyle, state._widthStyle = _widthStyle(state))) changed._widthStyle = true;
    }

    if (changed.minHeight) {
      if (this._differs(state._minHeightStyle, state._minHeightStyle = _minHeightStyle(state))) changed._minHeightStyle = true;
    }
  };

  setup(PNotify_1);

  function createElement(name) {
    return document.createElement(name);
  }

  function append(target, node) {
    target.appendChild(node);
  }

  function blankObject() {
    return Object.create(null);
  }

  function createText(data) {
    return document.createTextNode(data);
  }

  function setAttribute(node, attribute, value) {
    if (value == null) node.removeAttribute(attribute);else node.setAttribute(attribute, value);
  }

  function addListener(node, event, handler, options) {
    node.addEventListener(event, handler, options);
  }

  function insert(target, node, anchor) {
    target.insertBefore(node, anchor);
  }

  function updateKeyedEach(old_blocks, component, changed, get_key, dynamic, ctx, list, lookup, node, destroy, create_each_block, intro_method, next, get_context) {
    var o = old_blocks.length;
    var n = list.length;

    var i = o;
    var old_indexes = {};
    while (i--) {
      old_indexes[old_blocks[i].key] = i;
    }var new_blocks = [];
    var new_lookup = {};
    var deltas = {};

    var i = n;
    while (i--) {
      var child_ctx = get_context(ctx, list, i);
      var key = get_key(child_ctx);
      var block = lookup[key];

      if (!block) {
        block = create_each_block(component, key, child_ctx);
        block.c();
      } else if (dynamic) {
        block.p(changed, child_ctx);
      }

      new_blocks[i] = new_lookup[key] = block;

      if (key in old_indexes) deltas[key] = Math.abs(i - old_indexes[key]);
    }

    var will_move = {};
    var did_move = {};

    function insert(block) {
      block[intro_method](node, next);
      lookup[block.key] = block;
      next = block.first;
      n--;
    }

    while (o && n) {
      var new_block = new_blocks[n - 1];
      var old_block = old_blocks[o - 1];
      var new_key = new_block.key;
      var old_key = old_block.key;

      if (new_block === old_block) {
        // do nothing
        next = new_block.first;
        o--;
        n--;
      } else if (!new_lookup[old_key]) {
        // remove old block
        destroy(old_block, lookup);
        o--;
      } else if (!lookup[new_key] || will_move[new_key]) {
        insert(new_block);
      } else if (did_move[old_key]) {
        o--;
      } else if (deltas[new_key] > deltas[old_key]) {
        did_move[new_key] = true;
        insert(new_block);
      } else {
        will_move[old_key] = true;
        o--;
      }
    }

    while (o--) {
      var old_block = old_blocks[o];
      if (!new_lookup[old_block.key]) destroy(old_block, lookup);
    }

    while (n) {
      insert(new_blocks[n - 1]);
    }return new_blocks;
  }

  function destroyBlock(block, lookup) {
    block.d(1);
    lookup[block.key] = null;
  }

  function detachNode(node) {
    node.parentNode.removeChild(node);
  }

  function removeListener(node, event, handler, options) {
    node.removeEventListener(event, handler, options);
  }

  function createComment() {
    return document.createComment('');
  }

  function setData(text, data) {
    text.data = '' + data;
  }

  function detachBetween(before, after) {
    while (before.nextSibling && before.nextSibling !== after) {
      before.parentNode.removeChild(before.nextSibling);
    }
  }

  function init(component, options) {
    component._handlers = blankObject();
    component._slots = blankObject();
    component._bind = options._bind;
    component._staged = {};

    component.options = options;
    component.root = options.root || component;
    component.store = options.store || component.root.store;

    if (!options.root) {
      component._beforecreate = [];
      component._oncreate = [];
      component._aftercreate = [];
    }
  }

  function assign(tar, src) {
    for (var k in src) {
      tar[k] = src[k];
    }return tar;
  }

  function assignTrue(tar, src) {
    for (var k in src) {
      tar[k] = 1;
    }return tar;
  }

  function flush(component) {
    component._lock = true;
    callAll(component._beforecreate);
    callAll(component._oncreate);
    callAll(component._aftercreate);
    component._lock = false;
  }

  function destroy(detach) {
    this.destroy = noop;
    this.fire('destroy');
    this.set = noop;

    this._fragment.d(detach !== false);
    this._fragment = null;
    this._state = {};
  }

  function get() {
    return this._state;
  }

  function fire(eventName, data) {
    var handlers = eventName in this._handlers && this._handlers[eventName].slice();
    if (!handlers) return;

    for (var i = 0; i < handlers.length; i += 1) {
      var handler = handlers[i];

      if (!handler.__calling) {
        try {
          handler.__calling = true;
          handler.call(this, data);
        } finally {
          handler.__calling = false;
        }
      }
    }
  }

  function on(eventName, handler) {
    var handlers = this._handlers[eventName] || (this._handlers[eventName] = []);
    handlers.push(handler);

    return {
      cancel: function cancel() {
        var index = handlers.indexOf(handler);
        if (~index) handlers.splice(index, 1);
      }
    };
  }

  function set(newState) {
    this._set(assign({}, newState));
    if (this.root._lock) return;
    flush(this.root);
  }

  function _set(newState) {
    var oldState = this._state,
        changed = {},
        dirty = false;

    newState = assign(this._staged, newState);
    this._staged = {};

    for (var key in newState) {
      if (this._differs(newState[key], oldState[key])) changed[key] = dirty = true;
    }
    if (!dirty) return;

    this._state = assign(assign({}, oldState), newState);
    this._recompute(changed, this._state);
    if (this._bind) this._bind(changed, this._state);

    if (this._fragment) {
      this.fire("state", { changed: changed, current: this._state, previous: oldState });
      this._fragment.p(changed, this._state);
      this.fire("update", { changed: changed, current: this._state, previous: oldState });
    }
  }

  function _stage(newState) {
    assign(this._staged, newState);
  }

  function _mount(target, anchor) {
    this._fragment[this._fragment.i ? 'i' : 'm'](target, anchor || null);
  }

  function _differs(a, b) {
    return a != a ? b == b : a !== b || a && (typeof a === "undefined" ? "undefined" : _typeof(a)) === 'object' || typeof a === 'function';
  }

  function callAll(fns) {
    while (fns && fns.length) {
      fns.shift()();
    }
  }

  function noop() {}

  return PNotify_1;
});
//# sourceMappingURL=PNotify.js.map