Calendar.LANG("de", "<PERSON><PERSON><PERSON>", {

        fdow: 1,                // first day of week for this locale; 0 = Sunday, 1 = Monday, etc.

        goToday : "Heute ausw\u00e4hlen",

        today: "Heute",         // appears in bottom bar

        wk: "wk",

        weekend: "0,6",         // 0 = Sunday, 1 = Monday, etc.

        AM: "am",

        PM: "pm",

        mn : [ "<PERSON><PERSON><PERSON>",
               "<PERSON>ruar",
               "M\u00e4rz",
               "April",
               "Mai",
               "Juni",
               "Juli",
               "August",
               "September",
               "Oktober",
               "November",
               "Dezember" ],

        smn : [ "Jan",
                "Feb",
                "M\u00e4r",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
                "Sep",
                "Okt",
                "Nov",
                "Dez" ],

        dn : [ "Sonntag",
               "Montag",
               "Dienstag",
               "Mittwoch",
               "Donnerstag",
               "Freitag",
               "Samstag",
               "Sonntag" ],

        sdn : [ "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>" ]

});
