<?
	session_start();
	include("inc/fileInclude.php");
	include("inc/clsObj.php");


	$objBill->party=$_GET['cid'];
	$partyBil=$objBill->selectSumByPartyAll();

	$objPurchase->party=$_GET['cid'];
	$partyPurchase=$objPurchase->selectSumByPartyAll();
	
	$objTds->party=$_GET['cid'];
	$partyTds=$objTds->selectSumByPartyAll();
	
	$partyAmount = $partyBil[0]['totamt']-($partyPurchase[0]['totamt']+$partyTds[0]['credit_amount']);
	
//===================================================================

	$objCash->party=$_GET['cid'];
	$partyBal=$objCash->selectRecByPartyAll();
	$total_debit = 0;
	$total_credit = 0;
	
	for($c=0;$c<count($partyBal);$c++)
	{
		if($partyBal[$c]['credit_amount']==0 && $partyBal[$c]['debit_amount']>0)
			$total_debit+=$partyBal[$c]['debit_amount'];
		elseif($partyBal[$c]['credit_amount']>0 && $partyBal[$c]['debit_amount']==0)
			$total_credit+=$partyBal[$c]['credit_amount'];
	}

	$balance_amount = $partyAmount-($total_credit-$total_debit);
	
	if($balance_amount < 0)
		echo "Credit Amount : ".-($balance_amount);
	elseif($balance_amount >= 0)
		echo "Debit Amount : ".$balance_amount;
?>