<?php
function add($company, $item_name, $lrno, $aqty, $sqty, $wpb, $yp)
{	
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	unset($cart);
	$cart = array( 
			  "company"=>array(),
			  "item_name"=>array(),
			  "lr_no"=>array(),
			  "aqty"=>array(),
			  "sqty"=>array(),
			  "wpb"=>array(),
			  "yp"=>array());	
	$cart=$_SESSION['cart'];
	$i=count($cart[0]);	

	$cart[0][$i]=$company;
	$cart[1][$i]=$item_name;
	$cart[2][$i]=$lrno;
	$cart[3][$i]=$aqty;
	$cart[4][$i]=$sqty;
	$cart[5][$i]=$wpb;
	$cart[6][$i]=$yp;

	$_SESSION['cart']=$cart;
	return $cart;
}

function remove($id)
{
	include("inc/fileInclude.php");
	include("inc/clsObj.php");
	
	$cart=$_SESSION['cart'];
	$tempcart =array( 
			  "company"=>array(),
			  "item_name"=>array(),
			  "lr_no"=>array(),
			  "aqty"=>array(),
			  "sqty"=>array(),
			  "wpb"=>array(),
			  "yp"=>array());

	for($col=0,$tempcol=0;$col<count($cart[1]);$col++)
	{
		if($col!=$id)	
		{
			$tempcart[0][$tempcol]=$cart[0][$col];
			$tempcart[1][$tempcol]=$cart[1][$col];
			$tempcart[2][$tempcol]=$cart[2][$col];
			$tempcart[3][$tempcol]=$cart[3][$col];
			$tempcart[4][$tempcol]=$cart[4][$col];
			$tempcart[5][$tempcol]=$cart[5][$col];
			$tempcart[6][$tempcol]=$cart[6][$col];
			$tempcol++;											
		}
	}

	$cart=$tempcart;
	$_SESSION['cart']=$cart;
	return $_SESSION['cart'];
}

?>