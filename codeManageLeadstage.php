<?php
include("template.php");
function main()
{
	$heading="<span>Manage</span> Lead Stage ";
    include("inc/clsObj.php");	
	extract($_POST);	
	$objLeadstage->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid  )) ;
	$objLeadstage->lead_stage=$Lead_Stage;
		
	$objLeadstage->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objLeadstage->insert();	
		redirect("codeManageLeadstage.php?msg=add");			
	}

	if(isset($_POST['btnUpdate']))
	{	
		 $objLeadstage->update();
		 redirect("codeManageLeadstage.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
					$objLeadstage->deleteSelect($chkAction);
					break;
			case 1:
					$objLeadstage->statusUpdatePublish($chkAction);
					break;
			case 2:
					$objLeadstage->statusUpdateUnPublish($chkAction);
		} 		  
		redirect("codeManageLeadstage.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{			 
		$objLeadstage->delete();
		redirect("codeManageLeadstage.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objLeadstage->status();
		redirect("codeManageLeadstage.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objLeadstage->selectRecById();						
	}	
	elseif($_GET['Lead_Stage']!="")
	{
		$objLeadstage->id = $_GET['Lead_Stage'];
		$listRec=$objLeadstage->selectRecById();
    }
	else
		$listRec=$objLeadstage->paging();
		
    include("html/frmManageLeadStage.php");
 } 
?>