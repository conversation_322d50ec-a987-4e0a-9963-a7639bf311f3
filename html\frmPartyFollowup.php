<script type="text/javascript">
	function changeStatus(obj){
		fstatus  = obj.value;
		document.location  = "codePartyFollowup.php?fstatus="+fstatus;
	}
</script>
<div class="pcoded-main-container">
    <div class="pcoded-content">
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>Manage Followup</h5>
							</div>
							<div class="col-md-6">
								
								<? if(isset($_REQUEST['btnAddUser']) || $_GET['id']!=''){ ?>
								
									<form action="<?=$pageName;?>?fstatus=<?=$_REQUEST['fstatus'];?>" method="post">
										<button type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack">
											<i class="fas fa-angle-left"></i> Back
										</button>
									</form>
								 <?php } elseif($_SESSION['act_add']==1) { ?>
									<form action="<?=$pageName;?>?fstatus=<?=$_REQUEST['fstatus'];?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add Followup
										</button>
									</form>									
								
								<? } ?>
								
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						<? if(isset($_REQUEST['btnAddUser']) || $_GET['id']!=''){ ?>
						<!--========== Add Followup ==========-->
						<form action="codePartyFollowup.php" method="post" name="frmcb" id="frmcb" onsubmit="MM_validateForm('Party_Name','','R','Next_Date','','R');return document.MM_returnValue">
                            <input name="hid" id="hid" type="hidden" value="<?=$_GET['id'];?>" />
                            <input type="hidden" name="action" id="action" value="" />
                            <input type="hidden" name="fstatus" id="fstatus" value="<?=$_GET['fstatus'];?>" />
                            <div class="form-row">
								<div class="form-group col-md-4">
                                    <label>Followup Date</label>
                                    <input type="text" name="Date" id="Date" class="form-control dd" value="<?=($listEdit[0]['trn_dt']!="") ? $listEdit[0]['trn_dt'] : date("d-m-Y");?>" required>
                                </div>	
                                <div class="form-group col-md-4">
                                    <label>Next Followup Date</label>
                                    <input type="text" name="Next_Date" id="Next_Date" value="<?=($listEdit[0]['ndt']!="") ? $listEdit[0]['ndt'] : date("d-m-Y");?>" class="form-control dd" >
                                </div>	
								<div class="form-group col-md-4">
                                    <label>Party Name</label>
                                    <? $listParty=$objParty->selectStatus(); ?>
									<select name="Party_Name" id="Party_Name" class="js-example-basic-single form-control col-sm-12" onchange="getpartybalance()" required>
										<option value="">--Select Client--</option>
										<? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
									</select>
                                	<b id="party_balance"></b>
                                </div>								
								<div class="form-group col-md-4">
                                    <label>Details</label>
                                    <textarea type="text" name="Remarks" id="Remarks" class="form-control" required><?=$listEdit[0]['remarks'];?></textarea>
                                </div>	
								<div class="form-group col-md-4">
                                    <label>Amount</label>
                                    <input type="text" name="Amount" id="Amount" value="<?=$listEdit[0]['amount'];?>" class="form-control">
                                </div>
								<div class="form-group col-md-4">
                                    <label>Due Date</label>
                                    <input type="text" name="Due_Date"  id="Due_Date" size="10" value="<?=($listEdit[0]['ddt']!="") ? $listEdit[0]['ddt'] : date("d-m-Y");?>" class="form-control dd">
                                </div>
                            </div>
							
                            <?php if(isset($_GET['id']) && $_GET['id']!=""){ ?>
							<button type="submit" name="btnUpdate" value="Update Followup Details"  class="btn btn-success">Update Followup Details</button><? }else{ ?>
							<button type="submit" name="btnAdd" value="Add Followup Details" class="btn btn-success">Add Followup Details</button><? } ?>
                        </form>
						<!--========== Add Followup ==========-->
						<? }
						else {	?>
						<!--========== List View ==========-->
						<!-- Search & Shorting -->
						<form name="frmsearch" id="frmsearch" action="" method="get" onSubmit="MM_validateFormDate();return document.MM_returnValue">
							<div class="row dataTables_wrapper">
								<div class="col-sm-12 col-md-4">
									<label>Search Party Name :</label>
                                    <? $listParty=$objParty->selectStatus(); ?>
									<select name="Client_Name" id="Client_Name" class="js-example-basic-single form-control col-sm-12">
										<option value="">--Select Client--</option>
										<? for($i=0;$i<count($listParty);$i++)
                                        { ?>
                                        <option value="<?=$listParty[$i]['id'];?>" <?=($listEdit[0]['party']==$listParty[$i]['id']) ? "selected" : "";?>><?=$listParty[$i]['party_name'];?></option>
                                        <? } ?>
									</select>
                                     <script type="text/javascript">
									$('#Client_Name').tokenize({
										placeholder: "",
										maxElements: 1,
										data: { objid:"Party_Name", functionname: ""}
									});
								</script>
								</div>
								<div class="form-group col-md-3">
									<label>From Date</label>
									<input type="texxt" name="From_Date" id="From_Date" value="<?=$_REQUEST['From_Date'];?>" class="form-control dd">
                                </div>
								<div class="form-group col-md-3">
									<label>To Date</label>
									<input type="text" name="To_Date" id="To_Date" value="<?=$_REQUEST['To_Date'];?>"  class="form-control dd">
                                </div>
								<div class="form-group col-md-2">
                                    <label>&nbsp;</label><br>
									<button type="submit" name="btnSearch" id="btnSearch" value="Search" class="btn btn-info btn-sm has-ripple"><i class="fas fa-search" aria-hidden="true"></i></button>
									<a href="codePartyFollowup.php" class="btn btn-info btn-sm has-ripple">Reset</a>
                                </div>
								<div class="clearfix"></div>
								
								<div class="col-md-3"></div>
								<div class="form-group col-md-2">
									<div class="form-check">
										<input class="form-check-input" type="radio" name="rdoStatus" id="rdoStatus" value="Pending" onclick="changeStatus(this)" <?=($_GET['fstatus']=="Pending") ? "checked" : "";?>>
										<label class="form-check-label"><a href="codePartyFollowup.php?fstatus=Pending">Pending Followup</a></label>
									</div>
								</div>
								<div class="form-group col-md-2">
									<div class="form-check">
										<input class="form-check-input" type="radio" name="rdoStatus" id="rdoStatus" value="Today" onclick="changeStatus(this)" <?=($_GET['fstatus']=="Today") ? "checked" : "";?> >
										<label class="form-check-label"><a href="codePartyFollowup.php?fstatus=Today">Today's Followup</a></label>
									</div>
								</div>
								<div class="form-group col-md-2">
									<div class="form-check">
										<input class="form-check-input" type="radio" name="rdoStatus" id="rdoStatus" value="Future" onclick="changeStatus(this)" <?=($_GET['fstatus']=="Future") ? "checked" : "";?>>
										<label class="form-check-label"><a href="codePartyFollowup.php?fstatus=Future">Future Followup</a></label>
									</div>
								</div>
								<div class="col-md-3"></div>
								<div class="clearfix"></div>
							</div>
						</form>	
						<!-- Search & Shorting -->

						<!-- Table -->
                        <form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                         <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
                         <input type="hidden" name="fstatus" id="fstatus" value="<?=$_GET['fstatus'];?>" />
						<div class="dt-responsive table-responsive">
							<table id="tblFollowup" class="table table-striped table-bordered nowrap table-responsive" width="100%">
								<thead>
									<tr>
										<th colspan="8" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0" name="optAction" id="optAction">
													<option value="">--Select Action--</option>
													  <? if($_SESSION['act_del']==1){ ?>
                                                      <option value="0">Delete</option><? } ?>
                                                      <? if($_SESSION['act_edit']==1){ ?>
                                                      <option value="1">Published</option>
                                                      <option value="2">Unpublished</option><? } ?>
                                                      <option value="3">Print</option>
												</select>
											</label>
											<input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" value="Submit" name="btnAction" id="btnAction" />
										</th>			
									</tr>
									<tr>
										<th class="sorting_asc sorting_desc">Sr. No.</th>
										<th class="sorting_asc sorting_desc">Party Details</th>
										<th class="sorting_asc sorting_desc">Next Followup Date</th>
										<th class="sorting_asc sorting_desc">Due Date</th>
										<th class="sorting_asc sorting_desc">Remarks</th>
										<th class="sorting_asc sorting_desc">Amount</th>
										<th class="sorting_asc sorting_desc">Last Followup Date</th>
										<th class="sorting_asc sorting_desc">
											<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"> Action
										</th>
									</tr>
								</thead>
								<tbody>
                                	<? for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
										<td>
											 <?
                	$objParty->id = $listRec[$e]['fprt'];
					$partynm = $objParty->selectRecById();
					echo "<strong>".$partynm[0]['party_name']."</strong><br/>";
					echo ($partynm[0]['office_no1']!="") ? $partynm[0]['office_no1']."<br/>" : "";
					echo ($partynm[0]['email']!="") ? $partynm[0]['email']."<br/>" : "";
					echo ($partynm[0]['domain_name']!="") ? $partynm[0]['domain_name']."<br/>" : "";
					echo ($partynm[0]['contact_name1']!="") ? $partynm[0]['contact_name1']."<br/>" : "";
					echo ($partynm[0]['contact_no1']!="") ? $partynm[0]['contact_no1']."<br/>" : "";
					echo ($partynm[0]['email1']!="") ? $partynm[0]['email1']."<br/>" : "";
					
				?>
										</td>
										<td><?=$listRec[$e]['ndt'];?></td>
										<td><?=$listRec[$e]['ddt'];?></td>
										<td><?=$listRec[$e]['flremarks'];?></td>
										<td><?=$listRec[$e]['amount'];?></td>
										<td><?=$listRec[$e]['fdt'];?></td>
										<td class="table-action">
										<? if($_SESSION['act_edit']==1 || $_SESSION['act_del']==1){	?>
	                					<input type="checkbox" name="chkAction[]" id="chkAction[]" value="<? echo $listRec[$e]['fid'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10 chkboxcontent"/>&nbsp;<? } ?>
                                        <? if($_SESSION['act_edit']==1){
											if($listRec[$e]['status']=='1'){?>
                                            	<a href="?status=0&amp;uid=<?=$listRec[$e]['fid'];?>&fstatus=<?=$_REQUEST['fstatus'];?>"><i class="fas fa-check mr-10 clr-green"></i></a><? }else{ ?>
                                            	<a href="?status=1&amp;uid=<?=$listRec[$e]['fid'];?>&fstatus=<?=$_REQUEST['fstatus'];?>"><i class="fas fa-minus mr-10 clr-red"></i></a><? } ?>
											<a href="<?=$pageName;?>?id=<?=$listRec[$e]['fid'];?>&fstatus=<?=$_REQUEST['fstatus'];?>">
												<i class="fas fa-pencil-alt mr-10"></i>
											</a>
                                        <? } 
											if($_SESSION['act_del']==1){?>
											<a onclick="deleteAll(<?php echo $listRec[$e]['fid'];?>,'pending followup','Party','<?=$pageName;?>')">
												<i class="fas fa-trash-alt mr-10 clr-red"></i>
											</a>
                                            <? } ?>
										</td>
									</tr>
                                    <? } ?>
								</tbody>
							</table>
						</div>
                        </form>
						<!-- Table -->
						<!--========== List View ==========-->
						<? } ?>
                    </div>
                </div>
            </div>
        </div>
	</div>
</div>