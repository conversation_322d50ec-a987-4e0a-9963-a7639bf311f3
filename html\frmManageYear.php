<div class="pcoded-main-container">
    <div class="pcoded-content">
		
        <!--========== Breadcrumb ==========-->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h5 class="m-b-10"><?=$heading;?></h5>
                        </div>
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">
								<i class="feather icon-home"></i></a>
							</li>
                            <li class="breadcrumb-item"><a href="#">Master</a></li>
                            <li class="breadcrumb-item"><a href="#"><?=$heading;?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!--========== Breadcrumb ==========-->
		
		<div class="row">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header">
						<div class="row align-items-center m-l-0">
							<div class="col-md-6">
								<h5>Manage State</h5>
							</div>
							<div class="col-md-6">
								<? if(isset($_REQUEST['btnAddUser']) or isset($_REQUEST['id'])){ ?>
									<form action="<?=$pageName;?>" method="post">
										<button type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnBack" id="btnBack">
											<i class="fas fa-angle-left"></i> Back
										</button>
									</form>
								<? }
								else{?>
								
									<form action="<?=$pageName;?>" method="post">
										<button class="btn btn-success btn-sm btn-round has-ripple f-right" name="btnAddUser" id="btnAddUser">
											<i class="fas fa-plus"></i> Add Financial Year
										</button>
									</form>									
								
								<? } ?>
								
							</div>
							<div class="clearfix"></div>
						</div>	
                    </div>
                    <div class="card-body">
						
						<? if(isset($_REQUEST['btnAddUser']) or isset($_REQUEST['id']) or ($_REQUEST['msg']=="add")){ ?>
						<!--========== Add Financial Year ==========-->
						<form action="codeManageYear.php" method="post" name="frmManage">
	                        <input name="hid" type="hidden" value="<?=$_GET['id'];?>" />
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label>Financial Year</label>
                                    <input name="Year_Name" type="text" id="Year_Name"  value="<?=$listEdit[0]['year_name'];?>" class="form-control" required>
                                </div>
								<div class="col-md-8"></div>
								<div class="form-group col-md-12">
									<div class="form-check">
										<input class="form-check-input" name="Current_Year" type="checkbox" id="Current_Year" value="1" <?=($listEdit[0]['current_year']==1) ? "checked": "";?>>
										<label class="form-check-label">Is current financial year ?</label>
									</div>
								</div>							
                            </div>
                            <?php if(isset($_GET['id'])){?>
            			<input type="submit" name="btnUpdate" value="Update Financial Year" class="btn btn-success" />
			      <?php } else {?>
            			<input type="submit" name="btnAdd" value="Add Financial Year" class="btn btn-success" />
			      <?php } ?> 
                        </form>
						<!--========== Add Financial Year ==========-->
						<? }
						else {
						?>
						
						<!--========== List View ==========-->
						<div class="dt-responsive table-responsive">
                        	<form action="<?=$pageName;?>" class="table-view" method="post" name="frmManageDetails" onsubmit="return checkActionValidation();">
                             <input type="hidden" name="total_records" id="total_records" value="<?=count($listRec);?>" />
                            <div id="error_frmvalidate" class="err_msg" style="float:right; font-weight:bold; font-style:italic; padding:3px;"></div><div style="clear:both;"></div>
							<table id="tblFinancialYear" class="table table-striped table-bordered nowrap">
								<thead>
									<tr>
										<th colspan="4" class="text-right">
											<label class="mb-0 mr-15"> Action :
												<select name="optAction" id="optAction" class="custom-select custom-select-sm form-control form-control-sm pt-0 pb-0 pr-0">
													<option value="">-- Select Action --</option>
													<option value="Published">Published</option>
													<option value="Unpublished">Unpublished</option>
												</select>
											</label>
											<input type="submit" class="btn btn-success btn-sm btn-round has-ripple f-right"  value="Submit" name="btnAction" id="btnAction" />
										</th>			
									</tr>
									<tr>
										<th class="sorting_asc sorting_desc">Sr. No.</th>
										<th class="sorting_asc sorting_desc">Year Name</th>
										<th class="sorting_asc sorting_desc">
                                        	<input type="checkbox" id="selectAll" name="selectAll" onclick="check_all(this.form,this);" class="v-align-middle mr-10"/> Action
										</th>
									</tr>
								</thead>
								<tbody>
                                <? if(count($listRec)>0){	
									for($e=0;$e<count($listRec);$e++){ ?>
									<tr>
										<td><?=$e+1;?></td>
                <td><?=$listRec[$e]['year_name'];?></td>
										<td class="table-action">
                                        	<input type="checkbox" name="chkAction[]" id="chkAction[]"  value="<? echo $listRec[$e]['id'];?>" onclick="chkTotal(this.form)" class="v-align-middle mr-10"/>
                              		<?php  if($_SESSION['act_edit']==1){
										if($listRec[$e]['status']=='0'){ ?><a href="?status=1&amp;uid=<?=$listRec[$e]['id'];?>"><i class="fas fa-minus mr-10 clr-red"></i></a>
										<?php } else {?>
											<a href="?status=0&amp;uid=<?=$listRec[$e]['id'];?>"><i class="fas fa-check mr-10 clr-green"></i></a>
										<?php } ?>
											<a href="codeManageYear.php?id=<?=$listRec[$e]['id'];?>"><i class="fas fa-pencil-alt mr-10"></i></a><? } ?>
										</td>
									</tr>
                                    <? }
									} ?>
								</tbody>
							</table>
                            </form>
						</div>
						<!--========== List View ==========-->
						
						<? } ?>
						
                    </div>
                </div>
            </div>
        </div>
		
	</div>
</div>
