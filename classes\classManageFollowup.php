<?php
global $db;
class followupMaster
{
   /* Variable Declaration */		
	var $tablename='followup';
	var $id;
	var $company;
	var $financial_year;
	var $party;
	var $followup_date;
	var $next_date;
	var $remarks;
	var $amount;
	var $limit;
	var $start;

	/*Constructor to intialize the Dabatabase*/
	function followupMaster()
	{
		$this->db = new dbclass();
	}

	/*Insert Record into Database*/		
	function insert() 
	{
		$sql = "insert into `$this->tablename` values ('',
				'$this->company',
				'$this->financial_year',
				'$this->party',
				'$this->followup_date',
				'$this->next_date',
				'$this->remarks',
				'$this->amount',
				'$this->due_date',
				'$this->status')";
		// echo $sql; die();
		$id=$this->db->insert($sql);
		//$id=mysql_insert_id();
		return($id);
	} 			

	/* update the record in database */		
	function update()
	{
		$sql = "update `$this->tablename` set
						`company`='$this->company',
						`financial_year`='$this->financial_year',
						`party`='$this->party',
						`followup_date`='$this->followup_date',
						`next_date`='$this->next_date',
						`remarks`='$this->remarks',
						`amount`='$this->amount',
						`due_date`='$this->due_date'
						where `id`=$this->id";
		 // echo $sql;die();
		$this->db->edit($sql);		
		return true;
	}	

	/*update single status*/	
	function status()
	{
		$sql = "update `$this->tablename` set
					   `status`='$this->status'
						where `id`=$this->id";
						// echo $sql;die();	
		$this->db->edit($sql);		
		return true;
	}



	/* Fetch all the records */	
	function select()
	{
		$sql ="select * from `$this->tablename`";
		// echo $sql;die();
		$result=$this->db->select($sql);
		return($result);
	}	

	/*update selected status publish*/	
	function statusUpdatePublish($chk)
	{
	
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=1
							where `id`='$id'";
			//echo $sql;die();	
			$this->db->edit($sql);		
		}
		return true;
	}	

	/*update selected status unpublish*/	
	function statusUpdateUnPublish($chk)
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql = "update `$this->tablename` set
						   `status`=0
							where `id`='$id'";//echo $sql;die();	
					$this->db->edit($sql);		
		}
		return true;
	}	

	/*Fectch record by id from Database*/		
	function selectRecById()
	{
		$sql ="select *, date_format(`followup_date`, '%d-%m-%Y') as fdt, date_format(`next_date`, '%d-%m-%Y') as ndt, date_format(`due_date`, '%d-%m-%Y') as ddt from `$this->tablename` where id='$this->id'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectDateByParty()
	{
		$sql ="select date_format(`followup_date`, '%d-%m-%Y') as fdt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and financial_year = '".$_SESSION['fyear']."'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	function selectSearchDateByParty($query)
	{
		$sql ="select date_format(`date`, '%d-%m-%Y') as tdate from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' ".$query;
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
	/*Fectch record by id from Database*/		
	function selectSumByParty()
	{
		$sql ="select SUM(amount) as totamt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."'";
		// echo $sql; // die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}
	
		/*Fectch record by id from Database*/		
	function selectSumByPartyBeforeDate()
	{
		$sql ="select SUM(amount) as totamt from `$this->tablename` where party='$this->party' and company = '".$_SESSION['company']."' and date < '$this->bdate'";
		// echo $sql; die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	
	
	/*Fectch Details by party from Database*/		
	function selectDetByParty()
	{
		$sql ="select chb.id as chid, chb.party as party, cd.id as cdid, cd.cid as cid, (cd.sale_qty * cd.weight) * cd.amount as total_amount, cd.amount as amount from chitthibook_details cd  left join  chitthibook chb on chb.id = cd.cid where chb.party='$this->party'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	


	/*Fectch record by id from Database*/		
	function selectRecByPartyDate()
	{
		$sql ="select *, date_format(`date`, '%d-%m-%Y') as bdt from `$this->tablename` where party = '$this->party' and `date` like '%$this->tdt%'";
		// echo $sql;die();
	   	$result=$this->db->select($sql);
	   	return($result);
	}	

	/*delete a record from database*/	
	function delete()
	{
		$sql="delete from `$this->tablename` 
			  where `id`=$this->id";
		//echo $sql;die();
		//mysql_query($sql);
		$this->db->sql_query($sql);
	}	

	/*delete a record from database*/	
	function deleteByParty()
	{
		$sql="delete from `$this->tablename` 
			  where `party`='".$this->party."'";
		$this->db->sql_query($sql);
	}			

	/*delete the selected record*/	
	function deleteSelect($chk) 
	{
		for($i=0;$i<count($chk);$i++)
		{
			$id = $chk[$i];
			$sql="delete from `$this->tablename` where `id` = '$id'";
			$this->db->sql_query($sql);
		}
		return true;
	}	
					
	/*...paging...*/
	function paging($query)
	{			
		$pages = new Paging();
		$pages->sql = "select flu.id as fid, date_format(flu.followup_date,'%d-%m-%Y') as fdt, date_format(flu.next_date,'%d-%m-%Y') as ndt, date_format(flu.due_date,'%d-%m-%Y') as ddt, flu.party as fprt, flu.remarks as flremarks, flu.amount as amount, flu.status as status, pm.id as pid, pm.party_name as pmpn from `$this->tablename` flu left join party_master pm on flu.party=pm.id where flu.party!=''".$query." and company='".$_SESSION['company']."' order by flu.next_date";

			// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}

	function selectByQuery($query)
	{			
			$sql = "select flu.id as fid, date_format(flu.followup_date,'%d-%m-%Y') as fdt, date_format(flu.next_date,'%d-%m-%Y') as ndt, date_format(flu.due_date,'%d-%m-%Y') as ddt, flu.party as fprt, flu.remarks as flremarks, flu.amount as amount, flu.status as status, pm.id as pid, pm.party_name as pmpn from `$this->tablename` flu left join party_master pm on flu.party=pm.id where flu.party!=''".$query." and company='".$_SESSION['company']."' order by flu.next_date";
		$result=$this->db->select($sql);
		return($result);
	}

	/*...paging...*/
	function selectRecBySearch($query)
	{
		$sql ="select *, date_format(`date`,'%d-%m-%Y') as tdt from `$this->tablename` where party!=''".$query;
		// echo $sql; die;
	   	$result=$this->db->select($sql);
	   	return($result);
	}

	/*...paging...*/
	function pagingAccountStatus()
	{			
		$pages = new Paging();
		$pages->sql ="select party, Sum(amount) as amount from `cashbook` group by party";
		// echo $pages->sql; die;
		$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
		$pages->limit = 50;
		$pages->GeneratePaging();
		$this->pagination=$pages->pagination; 
		$result=$this->db->select($pages->sql);
		return($result);
	}	
}		
?>   