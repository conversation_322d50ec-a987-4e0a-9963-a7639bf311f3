<?php 
include("template.php");
function main()
{
	$heading="Manage Chitthibook ";
    include("inc/clsObj.php");	
	
	extract($_POST);	
	$cdate = explode("-",$Chitthi_Date);
	$chitthi_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];
	$objChitthi->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
	$objChitthi->party=ProperCase($Party_Name);
	$objChitthi->chitthi_date=$chitthi_date;
	$objChitthi->remarks=$Remarks;
	$objChitthi->extra_amount=$Extra_Amount;
	$objChitthi->status=isset($_GET['status']) ? $_GET['status'] : 1;
	
	if(isset($_POST['btnAdd']))
	{
		$objChitthi->insert();
		redirect("codeManageChitthibook.php?msg=add");
	}

	if(isset($_POST['btnUpdate']))
	{
		$objChitthi->update();
		redirect("codeManageChitthibook.php?msg=edit");
	}

	if(isset($_POST['btnAction']))
	{
		extract($_POST);
		switch($optAction)
		{
			case 0:
				$objChitthi->deleteSelect($chkAction);
				break;
			case 1:
				$objChitthi->statusUpdatePublish($chkAction);
				break;
			case 2:
				$objChitthi->statusUpdateUnPublish($chkAction);
				break;
			case 3:
				$cid = implode(",",$chkAction); ?>
				<script language="javascript">
					window.open('print_chitthibook_all.php?cid=<?=$cid;?>', 'newwindow');
				</script>
				<?
				// redirect("print_chitthibook_all.php?cid=".$cid);
		} 		  
		redirect("codeManageChitthibook.php?msg=edit");
	}	

	if(isset($_GET['delete']))
	{	
		$objChitthi->deleteById();
		$objChitthi->deleteItemsById();
		redirect("codeManageChitthibook.php?msg=del");
	}	

	if(isset($_GET['status']))
	{			 
		$objChitthi->status();
		redirect("codeManageChitthibook.php?msg=status");		
	}

	if(isset($_GET['id']))
	{			
		$listEdit=$objChitthi->selectRecById();

		$objChitthi->cid = $listEdit[0]['id'];
		$listItems=$objChitthi->selectItemsByChitthiNo();	
	}
	$listRec=$objChitthi->paging();
    include("html/frmManageChitthibook.php");
} 
?>
