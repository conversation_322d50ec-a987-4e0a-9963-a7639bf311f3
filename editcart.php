<?php
session_start();
include("functions.php");

/* include("inc/fileInclude.php");
include("inc/clsObj.php");	*/

if(isset($_GET['edit']))
{
	if($_GET['action']=="addrow")
	{
		switch($_GET['edit'])
		{
			case "add": 
				$_SESSION['party']=$_REQUEST['Party_Name'];
				$_SESSION['cdate']=$_REQUEST['Chitthi_Date'];
				$_SESSION['remarks']=$_REQUEST['Remarks'];
				$_SESSION['extra_amount']=$_REQUEST['Extra_Amount'];
				
				add($_REQUEST['Company_Name'], $_REQUEST['Item_Name'], $_REQUEST['LR_No'], $_REQUEST['Available_Qty'], $_REQUEST['Sale_Qty'], $_REQUEST['Weight_Per_Bag'],$_REQUEST['Your_Price']);
				break;
			case "remove":
				remove($_REQUEST['rid']);		
				break;
		}
		redirect("codeManageChitthibook.php?cmd=btnAdd&id=".$_GET['hid']);			
	}
	elseif($_GET['action']=="addrecord")
	{
		include("inc/fileInclude.php");
		include("inc/clsObj.php");	
	
		extract($_GET);	
		$cdate = explode("-",$Chitthi_Date);
		$chitthi_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];
		$objChitthi->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
		$objChitthi->party=$Party_Name;
		$objChitthi->chitthi_date=$chitthi_date;
		$objChitthi->remarks=$Remarks;
		$objChitthi->extra_amount=$Extra_Amount;
		$objChitthi->status=isset($_GET['status']) ? $_GET['status'] : 1;

		$iid = $objChitthi->insert();

		$cart=$_SESSION['cart'];

		for($i=0;$i<count($cart[0]);$i++)
		{
			$objChitthi->cid = $iid;
			$objChitthi->company = $cart[0][$i];
			$objChitthi->item_name = $cart[1][$i];
			$objChitthi->lrno = $cart[2][$i];
			$objChitthi->aqty = $cart[3][$i];
			$objChitthi->sqty = $cart[4][$i];
			$objChitthi->weight = $cart[5][$i];
			$objChitthi->yp = $cart[6][$i];
			$objChitthi->insert_items();	
		}  
		redirect("codeManageChitthibook.php?msg=add");			
	}
	else if($_GET['action']=="editrecord")
	{
		include("inc/fileInclude.php");
		include("inc/clsObj.php");	
	
		extract($_GET);	
		
		$cdate = explode("-",$Chitthi_Date);
		$chitthi_date=$cdate[2]."-".$cdate[1]."-".$cdate[0];
		$objChitthi->id=isset($_GET['uid']) ? $_GET['uid'] : ( isset($_GET['delete']) ? $_GET['delete'] : ( isset($_GET['id']) ? $_GET['id'] : $hid)) ;
		$objChitthi->party=$Party_Name;
		$objChitthi->chitthi_date=$chitthi_date;
		$objChitthi->remarks=$Remarks;
		$objChitthi->extra_amount=$Extra_Amount;
		$objChitthi->status=isset($_GET['status']) ? $_GET['status'] : 1;

		$objChitthi->update();
		$objChitthi->delete_items();

		$cart=$_SESSION['cart'];

		for($i=0;$i<count($cart[0]);$i++)
		{
			$objChitthi->cid = $_GET['hid'];
			$objChitthi->company = $cart[0][$i];
			$objChitthi->item_name = $cart[1][$i];
			$objChitthi->lrno = $cart[2][$i];
			$objChitthi->aqty = $cart[3][$i];
			$objChitthi->sqty = $cart[4][$i];
			$objChitthi->weight = $cart[5][$i];
			$objChitthi->yp = $cart[6][$i];
			$objChitthi->insert_items();	
		}  
		redirect("codeManageChitthibook.php?msg=edit");			
	}
}
?>