<?php
global $db;
class adminMenuMaster
	{
	   var $tablename='adminmenumgr';
	   var $id;
	   var $adminMenuSubId;
	   var $adminMenuSeqNo;
	   var $adminMenuName;
	   var $adminMenuDesc;
	   var $status;
	   var $adminMenuLink;
	   var $limit;
	   var $start;
        function adminMenuMaster()
			{
						$this->db = new dbclass();
			}
		function insert()
			{
			  	$sql = "insert into `$this->tablename`(
								`adminMenuSubId`,
								`adminMenuName`,
								`status`,
								`adminMenuLink`,
								`adminRights`,
								`adminMenuSeqNo`)values(
								$this->adminMenuSubId,
								'$this->adminMenuName',
								$this->status,
								'$this->adminMenuLink',
								'$this->adminRights',
								$this->adminMenuSeqNo)";//echo $sql;die();										
					$this->db->insert($sql);
					$id=mysql_insert_id();
					return($id);
			}
		function update()
			{
				$sql = "update `$this->tablename` set
	  							`adminMenuSubId`='$this->adminMenuSubId',
								`adminMenuName`='$this->adminMenuName',
								`adminMenuLink`='$this->adminMenuLink',
								`adminMenuSeqNo`='$this->adminMenuSeqNo',
								`adminRights`='$this->adminRights'								
						    	 where `id`=$this->id";//echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}							
		function menuCategoryList()//order by id
			{
				 $sql ="select * from `$this->tablename` 
				   		 		 where adminMenuSubId=0  and  status=1 order by adminMenuSeqNo";//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}
		function menuCategoryListRec()//order by adminMenuSeqNo
			{
				 $sql ="select * from `$this->tablename` 
							  where adminMenuSubId=0  order by adminMenuSeqNo ";//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}
		function menuSubCategoryList()//order by id
			{
				 $sql ="select * from `$this->tablename` 
				   		 	 where adminMenuSubId=$this->id and status=1 order by adminMenuSeqNo";//echo $sql;die();
    			   $result=$this->db->select($sql);
	 		       return($result);
			}	
		function status()
			{
				$sql = "update `$this->tablename` set
	  						   `status`='$this->status'
							    where `id`=$this->id";//echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}
		function delete()
			{
				$sql="delete from `$this->tablename` 
					  where `adminMenuSubId`=$this->id";
				$this->db->sql_query($sql);
				$sql="delete from `$this->tablename` 
					  where `id`=$this->id";//echo $sql;die();
				$this->db->sql_query($sql);
			}	
		function selectRecById()
			{
				$sql ="select * from `$this->tablename` 
					   where id='$this->id'";//echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result);
			}
		
		function getIdByName($pagename)
			{
				$sql ="select id,adminMenuLink from `$this->tablename` 
					   where adminMenuLink='$pagename'";//echo $sql;die();
				   $result=$this->db->select($sql);
	 		       return($result[0]['id']);
			}
		
		function sequenceUpdate()
			{
				$sql = "update `$this->tablename` set
	  						   `adminMenuSeqNo`='$this->adminMenuSeqNo'
							    where `id`=$this->id";//echo $sql;die();	
						$this->db->edit($sql);		
						return true;
			}
		/*update selected status publish*/	
		function statusUpdatePublish($chk)
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql = "update `$this->tablename` set
										   `status`=1
											where `id`='$id'";//echo $sql;die();	
									$this->db->edit($sql);		
	  				    }
			    return true;
			}	
		/*update selected status unpublish*/	
		function statusUpdateUnPublish($chk)
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql = "update `$this->tablename` set
										   `status`=0
											where `id`='$id'";//echo $sql;die();	
								   	$this->db->edit($sql);		
	  				    }
			    return true;
			}	
		/*delete the selected record*/	
		function deleteSelect($chk) 
			{
				for($i=0;$i<count($chk);$i++)
						{
							$id = $chk[$i];
							$sql="delete from `$this->tablename` where `id` = '$id'";
	     					//$res = mysql_query($sql);
							$this->db->sql_query($sql);
						}
				return true;
			}	
		/*...paging...*/
		function paging()
			{				
				$pages = new Paging();
				$pages->sql ="select * from `$this->tablename`";
				$pages->page = isset($_REQUEST['page']) ? $_REQUEST['page'] : 1;
				$pages->limit = 10;
				$pages->GeneratePaging();
                $this->pagination=$pages->pagination; 
				$result=$this->db->select($pages->sql);
	 		    return($result);
			}   				
													
}		
?>   