<?php
include("template.php");
function main()
{
	$heading="Manage Quotation ";
    include("inc/clsObj.php");
	$objQuote->id=$_GET['id'];
	$listEdit=$objQuote->selectRecById();
	$objQuote->qid = $listEdit[0]['id'];
	$listItems=$objQuote->selectItemsByBillNo();
	//echo count($listItems);
?>
<?php /*?><link href="css/new_style.css" rel="stylesheet" type="text/css" />
<link href="css/forms.css" rel="stylesheet" type="text/css" />
<?php */?>
<style>
#print_header{
	font-size:12px!important;
	font-family:Verdana, Geneva, sans-serif;
}

.tbl_invoice td{
	font-size:13px!important;
	padding:3px!important;
}

.tbl_invoice td ul, .tbl_invoice td ol{
	padding:0px 20px!important;
	margin-top:0px!important;
}

.tbl_invoice td ul li, .tbl_invoice td ol li{
	font-size:12px!important;
}

.tbl_invoice td strong{
	font-size:13px!important;
}
</style>


<form action="codeManageQuotation.php" method="post" name="frmAddRec">
<h2 class="pagetitle"><span>Manange</span> Quotation</h2>
    <a href="codeManageQuotation.php">
    	<input name="btnBack" id="btnBack"  type="button" class="back button" style="float:right;" onMouseOver="this.className='back button_hover'" onMouseOut="this.className='back button'" value="Back" /></a>
</form>

<div style="width:100%; margin-top:60px;" id="print_header">
<div style="width:700px; margin:0 auto;">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
<tr>
  <td>
<table width="100%" border="0" cellspacing="0" cellpadding="5" style="border-collapse:collapse; border:#999 0px solid;" class="tbl_invoice">
<tr><td width="49%" style="text-align:left; font-size:25px!important; font-weight:bold; height:50px; border-top:#999999 1px solid; border-left:#999999 1px solid; border-bottom:#999999 1px solid;"><img src="<?=BASE_URL."/images/logo.png";?>"></td>
  <td style="text-align:left; border-top:#999999 1px solid; border-right:#999999 1px solid; border-bottom:#999999 1px solid;" width="51%">
  <strong><?=$_SESSION['company'];?></strong><br />
  <?=$objCompany->getQuotationHeaderByCompany($listEdit[0]['company']);?>
  </td>
</tr>

  <tr class="toprow">
    <td width="55%" align="left" valign="top" style="border-top:#999999 1px solid; border-left:#999999 1px solid; border-right:#999999 1px solid; border-bottom:#999999 1px solid; padding:5px!important;" rowspan="2">
    	<strong>M/s :</strong><br/>
		<?=$listEdit[0]['party']."<br/>".$listEdit[0]['party_address']."<br/>".$listEdit[0]['party_city']; ?></td>
    <td width="45%" align="right" valign="top" style="border-top:#999999 1px solid; border-right:#999999 1px solid; border-bottom:#999999 1px solid; padding:5px!important;" >
      <strong>Quotation No. : </strong>
      <?=$listEdit[0]['quote_no'];?>      </td>
  </tr>
  <tr class="toprow">
    <td align="right" valign="bottom" style="border-top:#999999 1px solid; border-right:#999999 1px solid; border-bottom:#999999 1px solid; border-right:#999999 1px solid; padding:5px!important;" ><strong>Date : </strong><?=$listEdit[0]['bdt'];?></td>
  </tr>
  
  <tr class="toprow"><td align="center" valign="top" colspan="2" style="border-left:#999999 1px solid; border-right:#999999 1px solid; padding:10px;"><strong style="font-size:15px!important;">Quotation : <?=$listEdit[0]['quote_for'];?></strong></td></tr>
  </table>
</td></tr>
<tr>
<td>
<table width="100%" border="1" bordercolor="#999999" cellspacing="0" cellpadding="5" style="border-collapse:collapse;" class="tbl_invoice">
    <tr>
      <td width="5%" style="border-right:#999999 1px solid; text-align:center;"><strong>No.</strong></td>
      <td width="50%" style="border-right:#999999 1px solid; text-align:center;"><strong>Particulars</strong></td>
      <td width="17%" style="border-right:#999999 1px solid; text-align:center;"><strong>Rate</strong></td>
      <td width="10%" style="border-right:#999999 1px solid; text-align:center;"><strong>Quantity</strong></td>
      <td width="18%" style="border-right:#999999 1px solid; text-align:center;"><strong>Amount</strong></td>
    </tr>
  <? for($i=0;$i<count($listItems);$i++) { ?>
  <tr>
  	<td style="border-right:#999999 1px solid; text-align:center;" valign="top"><?=$i+1;?></td>
   <td style="border-right:#999999 1px solid;"  valign="top">
   	<? 	$objItem->id=$listItems[$i]['item'];
		$itemdet=$objItem->selectRecById();
		// echo "<strong>".$itemdet[0]['item_name']."</strong><br/>";
		echo $listItems[$i]['description'];
	?>
	</td>
    <td style="border-right:#999999 1px solid; text-align:center;"  valign="top"><img src="images/inr_img.png" width="8" height="10"> <?=$listItems[$i]['rate_per_qty'];?></td>
    <td style="border-right:#999999 1px solid; text-align:center;"  valign="top"><?=$listItems[$i]['no_of_items'];?></td>
    <td style="border-right:#999999 1px solid; text-align:right;"  valign="top"><img src="images/inr_img.png" width="8" height="10"> <?=$listItems[$i]['amount'];?></td>
  </tr>
<? } ?>  
  <tr class="toprow">
    <td colspan="4" style="text-align:right;">Net Amount :</td>
	<td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['net_amount'];?></td></tr>
    <? if($listEdit[0]['quote_type']=="GST"){ ?>
    	<tr class="toprow">
        <td colspan="4" style="text-align:right;">CGST@9% :</td>
        <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['cgst_amt'];?></td></tr>
        <tr class="toprow">
        <td colspan="4" style="text-align:right;">SGST@9% :</td>
        <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['sgst_amt'];?></td></tr>
    <? }
	if($listEdit[0]['quote_type']=="IGST"){ ?>
		<tr class="toprow">
        <td colspan="4" style="text-align:right;">IGST@18% :</td>
        <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['igst_amt'];?></td></tr>
	<? } ?>
    <tr>
    <td colspan="4" style="text-align:right;">Total Amount :</td>
    <td style="text-align:right;"><img src="images/inr_img.png" width="8" height="10"> <?=$listEdit[0]['amount'];?></td>
  </tr>  
  <tr><td colspan="5">
<?
function no_to_words($no)
    {
    $words = array('0'=> '' ,'1'=> 'one' ,'2'=> 'two' ,'3' => 'three','4' => 'four','5' => 'five','6' => 'six','7' => 'seven','8' => 'eight','9' => 'nine','10' => 'ten','11' => 'eleven','12' => 'twelve','13' => 'thirteen','14' => 'fouteen','15' => 'fifteen','16' => 'sixteen','17' => 'seventeen','18' => 'eighteen','19' => 'nineteen','20' => 'twenty','30' => 'thirty','40' => 'fourty','50' => 'fifty','60' => 'sixty','70' => 'seventy','80' => 'eighty','90' => 'ninty','100' => 'hundred','1000' => 'thousand','100000' => 'lakh','10000000' => 'crore');
    if($no == 0)
    return ' ';
    else {
    $novalue='';
    $highno=$no;
    $remainno=0;
    $value=100;
    $value1=1000;
    while($no>=100) {
    if(($value <= $no) &&($no < $value1)) {
    $novalue=$words["$value"];
    $highno = (int)($no/$value);
    $remainno = $no % $value;
    break;
    }
    $value= $value1;
    $value1 = $value * 100;
    }
    if(array_key_exists("$highno",$words))
    return $words["$highno"]." ".$novalue." ".no_to_words($remainno);
    else {
    $unit=$highno%10;
    $ten =(int)($highno/10)*10;
    return $words["$ten"]." ".$words["$unit"]." ".$novalue." ".no_to_words($remainno);
    }
    }
    }

echo "<strong>Amount Chargeable (in words) :</strong> Rs. ".ucwords(no_to_words($listEdit[0]['amount']))." Only";
?>
</td></tr>
</table>
</td>
</tr>

<tr>
<td>
<table border="1" cellspacing="0" bordercolor="#999999" cellpadding="5" width="100%" style="border-collapse:collapse;" class="tbl_invoice">
  <tr>
    <td width="348" valign="top">
<strong><u>TERMS &amp; CONDITION ::</u></strong><br />
<?=$listEdit[0]['quote_terms'];?>
</td>
    <td width="226" valign="top" ><br/>
    <div style="padding-left:20px!important;">
    <?=$listEdit[0]['quote_signature'];?>
    </div>
  <br/><br/>
  <hr/>
  <br/>
      <p align="center">For, <?=$_SESSION['company'];?></p>
      <br/><p align="center"><img src="images/signature.jpg" /></p><br/>
      <p align="center">AUTHORISED    SIGNATORY</p></td>
  </tr>
</table>
</td>
</tr>
</table><br/><br/>
<a href="codeManageQuotation.php?id=<?=$_GET['id'];?>">
<input name="btnEdit" id="btnEdit"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Edit Quotation" style="margin:0 auto!important;" /></a>
&nbsp;&nbsp;&nbsp;
<a href="print_quotation.php?id=<?=$_GET['id'];?>" target="_blank">
<input name="btnPrint" id="btnPrint"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Print Soft Quotation" style="text-decoration:none;" /></a>
&nbsp;&nbsp;&nbsp;
<a href="print_quotation_hard.php?id=<?=$_GET['id'];?>" target="_blank">
<input name="btnPrint" id="btnPrint"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Print Hard Quotation" style="text-decoration:none;" /></a>
&nbsp;&nbsp;&nbsp;


<a href="print_quotation_letterhead.php?id=<?=$_GET['id'];?>" target="_blank">
<input name="btnPrint" id="btnPrint"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Quotation - Letterhead" style="text-decoration:none;" /></a>
&nbsp;&nbsp;&nbsp;
<?php /*?><a href="codeManageBill.php?bid=<?=$_GET['id'];?>" target="_blank">
<input name="btnEmail" id="btnEmail"  type="button" class="button" onmouseover="this.className='button_hover'" onmouseout="this.className='button'" value="Send Email to Client" style="text-decoration:none;" /></a><?php */?>

</div>

</div>
<? } ?>