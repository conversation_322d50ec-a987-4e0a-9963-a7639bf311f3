<?  session_start();  	
	include("inc/fileInclude.php"); 
	include("inc/clsObj.php"); 

	$objBill->company=$_GET['cid'];
	$objBill->item=$_GET['itemid'];
	$objBill->bid=$_GET['bid'];
	$sale_qty = 0;


	$current_cart=$_SESSION['cart'];
	
	for($i=0;$i<count($current_cart[0]);$i++)
	{
		if($_GET['cid'] == $current_cart[0][$i] && $_GET['itemid'] == $current_cart[1][$i] && $_GET['bid'] == $current_cart[2][$i])
			$sale_qty+=$current_cart[4][$i];
	}

	//========check available stock from chitthibook ===========
	if($sale_qty==0)
	{
		$objChitthi->company=$_GET['cid'];
		$objChitthi->item=$_GET['itemid'];
		$objChitthi->lrno=$_GET['bid'];
		if(isset($objBill->company))
			$cbRec=$objChitthi->selectByCompanyItemBid();		
		if(count($cbRec)>0)
		{
			for($c=0;$c<count($cbRec);$c++)
			{
				$sale_qty+=$cbRec[$c]['sale_qty'];
			}
		}
	}
	//===========end of chitthibook details====================

	if(isset($objBill->company))
		$billRec=$objBill->selectByCompanyItemBid();	
		
	$total_stock = 0;
	for($b=0;$b<count($billRec);$b++)
	{
		$total_stock+=$billRec[$b]['stock_qty'];
	}
		
	$stock_qty = $total_stock - $sale_qty;

	if($stock_qty<0)
		$stock_qty = 0;
	
	echo $stock_qty."***".$billRec[0]['wpb']."***".$billRec[0]['rpq'];
?>